"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, HelpCircle, ShoppingCart, Package, CreditCard, Users, Mail, Phone, MessageSquare } from "lucide-react"

// FAQ data organized by categories
const faqData = {
  general: [
    {
      question: "What is PASSDOWN?",
      answer: "PASSDOWN is a multi-vendor marketplace platform for buying and selling computer components, with a focus on second-hand items. Our platform allows users to register as buyers or sellers, list products, browse listings, add items to cart, and complete purchases."
    },
    {
      question: "How do I create an account?",
      answer: "To create an account, click on the 'Register' button in the top right corner of the page. Fill in your details, choose whether you want to register as a buyer or seller, and follow the instructions to complete your registration."
    },
    {
      question: "Is PASSDOWN available internationally?",
      answer: "Currently, PASSDOWN is only available in India. We plan to expand to other countries in the future."
    },
    {
      question: "How can I contact customer support?",
      answer: "You can contact our customer support team through the Contact Us page, by <NAME_EMAIL>, or by phone at +91-********** during business hours (Monday to Friday, 9 AM to 6 PM IST)."
    }
  ],
  buying: [
    {
      question: "How do I search for products?",
      answer: "You can search for products using the search bar at the top of the page. You can also browse products by category or use the advanced search filters to narrow down your search."
    },
    {
      question: "How do I know if a product is in good condition?",
      answer: "All products on PASSDOWN have a condition rating (New, Like New, Good, Fair, or Poor). Sellers are required to provide detailed descriptions and images of their products, including any defects or issues. You can also check the seller's ratings and reviews before making a purchase."
    },
    {
      question: "What payment methods are accepted?",
      answer: "PASSDOWN accepts various payment methods including UPI, credit cards, debit cards, net banking, and digital wallets."
    },
    {
      question: "How long does shipping take?",
      answer: "Shipping times vary depending on your location and the seller's location. Typically, orders are delivered within 3-7 business days. You can check the estimated delivery time on the product page before placing your order."
    },
    {
      question: "Can I return a product if I'm not satisfied?",
      answer: "Yes, PASSDOWN has a 7-day return policy. If you're not satisfied with your purchase, you can initiate a return within 7 days of receiving the product. Please note that the product must be in the same condition as when you received it."
    }
  ],
  selling: [
    {
      question: "How do I list a product for sale?",
      answer: "To list a product, you need to register as a seller. Once registered, go to your account dashboard and click on 'Sell' or 'Create Listing'. Fill in the product details, upload images, set a price, and publish your listing."
    },
    {
      question: "What fees does PASSDOWN charge?",
      answer: "PASSDOWN charges a 5% commission on each successful sale. There are no listing fees or monthly subscription fees."
    },
    {
      question: "How do I get paid for my sales?",
      answer: "When a buyer purchases your product, the payment is held in escrow until the buyer receives and accepts the product. Once the buyer confirms receipt or after 7 days with no issues reported, the payment (minus our commission) is transferred to your registered bank account."
    },
    {
      question: "How do I ship products to buyers?",
      answer: "After a sale is confirmed, you'll receive the buyer's shipping details. You're responsible for packaging and shipping the product. You can use any courier service of your choice, but we recommend using our integrated shipping partners for better rates and tracking."
    },
    {
      question: "What happens if a buyer wants to return a product?",
      answer: "If a buyer initiates a return within the 7-day return period, you'll receive a notification. Once you receive the returned product and verify its condition, the refund process will begin. If there's a dispute, our customer support team will help resolve it."
    }
  ],
  account: [
    {
      question: "How do I reset my password?",
      answer: "To reset your password, click on 'Login', then 'Forgot password?'. Enter your email address, and we'll send you a password reset link."
    },
    {
      question: "How do I update my account information?",
      answer: "You can update your account information by going to your account dashboard and clicking on 'Settings' or 'Edit Profile'."
    },
    {
      question: "Can I change my account type from buyer to seller or vice versa?",
      answer: "Yes, you can change your account type by going to your account settings. If you're changing from buyer to seller, you'll need to provide additional information to set up your seller profile."
    },
    {
      question: "How do I delete my account?",
      answer: "To delete your account, go to your account settings and click on 'Delete Account'. Please note that this action is irreversible, and all your data will be permanently deleted."
    }
  ],
  technical: [
    {
      question: "What browsers are supported?",
      answer: "PASSDOWN supports all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of these browsers."
    },
    {
      question: "Is my personal and payment information secure?",
      answer: "Yes, we take security very seriously. All personal and payment information is encrypted using industry-standard SSL encryption. We do not store your full credit card details on our servers."
    },
    {
      question: "The website is not loading properly. What should I do?",
      answer: "Try clearing your browser cache and cookies, or try using a different browser. If the problem persists, please contact our support team."
    },
    {
      question: "Can I use PASSDOWN on my mobile device?",
      answer: "Yes, PASSDOWN is fully responsive and works on all mobile devices. You can access it through your mobile browser or download our mobile app from the App Store or Google Play Store."
    }
  ]
}

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<{category: string, items: typeof faqData.general}[]>([])
  
  // Handle search
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      return
    }
    
    const query = searchQuery.toLowerCase()
    const results: {category: string, items: typeof faqData.general}[] = []
    
    Object.entries(faqData).forEach(([category, items]) => {
      const matchedItems = items.filter(
        item => 
          item.question.toLowerCase().includes(query) || 
          item.answer.toLowerCase().includes(query)
      )
      
      if (matchedItems.length > 0) {
        results.push({
          category,
          items: matchedItems
        })
      }
    })
    
    setSearchResults(results)
  }
  
  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Help Center</h1>
          <p className="text-slate-500 max-w-2xl mx-auto">
            Find answers to common questions about using PASSDOWN, or contact our support team for further assistance.
          </p>
          
          {/* Search */}
          <div className="mt-8 max-w-md mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <Input
                type="search"
                placeholder="Search for answers..."
                className="pl-10 pr-4"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
              <Button 
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 bg-teal-600 hover:bg-teal-700"
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>
          </div>
        </div>
        
        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="mb-12">
            <h2 className="text-xl font-semibold mb-4">Search Results</h2>
            <Accordion type="multiple" className="w-full">
              {searchResults.map((result, categoryIndex) => (
                <div key={categoryIndex} className="mb-6">
                  <h3 className="text-lg font-medium mb-2 capitalize">{result.category} Questions</h3>
                  {result.items.map((item, itemIndex) => (
                    <AccordionItem key={`${categoryIndex}-${itemIndex}`} value={`search-${categoryIndex}-${itemIndex}`}>
                      <AccordionTrigger className="text-left">
                        {item.question}
                      </AccordionTrigger>
                      <AccordionContent>
                        <p className="text-slate-600">{item.answer}</p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </div>
              ))}
            </Accordion>
            <Button variant="outline" onClick={() => setSearchResults([])}>
              Clear Search Results
            </Button>
          </div>
        )}
        
        {/* FAQ Categories */}
        {searchResults.length === 0 && (
          <Tabs defaultValue="general">
            <TabsList className="w-full grid grid-cols-2 md:grid-cols-5 mb-8">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="buying">Buying</TabsTrigger>
              <TabsTrigger value="selling">Selling</TabsTrigger>
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="technical">Technical</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general">
              <Accordion type="multiple" className="w-full">
                {faqData.general.map((item, index) => (
                  <AccordionItem key={index} value={`general-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-slate-600">{item.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
            
            <TabsContent value="buying">
              <Accordion type="multiple" className="w-full">
                {faqData.buying.map((item, index) => (
                  <AccordionItem key={index} value={`buying-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-slate-600">{item.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
            
            <TabsContent value="selling">
              <Accordion type="multiple" className="w-full">
                {faqData.selling.map((item, index) => (
                  <AccordionItem key={index} value={`selling-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-slate-600">{item.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
            
            <TabsContent value="account">
              <Accordion type="multiple" className="w-full">
                {faqData.account.map((item, index) => (
                  <AccordionItem key={index} value={`account-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-slate-600">{item.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
            
            <TabsContent value="technical">
              <Accordion type="multiple" className="w-full">
                {faqData.technical.map((item, index) => (
                  <AccordionItem key={index} value={`technical-${index}`}>
                    <AccordionTrigger className="text-left">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-slate-600">{item.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
          </Tabs>
        )}
        
        {/* Contact Support */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6 text-center">Still Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <Mail className="h-8 w-8 text-teal-600" />
                <div>
                  <CardTitle>Email Support</CardTitle>
                  <CardDescription>Get a response within 24 hours</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">Send us an email and our support team will get back to you as soon as possible.</p>
                <Button asChild className="w-full bg-teal-600 hover:bg-teal-700">
                  <Link href="mailto:<EMAIL>">Email Us</Link>
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <Phone className="h-8 w-8 text-teal-600" />
                <div>
                  <CardTitle>Phone Support</CardTitle>
                  <CardDescription>Available Mon-Fri, 9AM-6PM</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">Call our customer support team directly for immediate assistance.</p>
                <Button asChild className="w-full bg-teal-600 hover:bg-teal-700">
                  <Link href="tel:+91**********">+91 **********</Link>
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <MessageSquare className="h-8 w-8 text-teal-600" />
                <div>
                  <CardTitle>Contact Form</CardTitle>
                  <CardDescription>Fill out our detailed form</CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">Use our contact form to provide more details about your issue.</p>
                <Button asChild className="w-full bg-teal-600 hover:bg-teal-700">
                  <Link href="/contact">Contact Form</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
