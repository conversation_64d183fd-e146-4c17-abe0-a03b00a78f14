"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useInView } from "react-intersection-observer"
import ProductCard from "@/app/components/product-card"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import type { Product } from "@/app/types"

interface ProductGridProps {
  products: Product[]
  loading?: boolean
  columns?: 1 | 2 | 3 | 4
  initialLimit?: number
  incrementAmount?: number
  showLoadMore?: boolean
  variant?: "default" | "compact" | "featured"
  className?: string
  emptyMessage?: string
}

export default function ProductGrid({
  products,
  loading = false,
  columns = 4,
  initialLimit = 8,
  incrementAmount = 8,
  showLoadMore = true,
  variant = "default",
  className = "",
  emptyMessage = "No products found"
}: ProductGridProps) {
  const [visibleProducts, setVisibleProducts] = useState<Product[]>([])
  const [limit, setLimit] = useState(initialLimit)
  const [loadingMore, setLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // Set up intersection observer for infinite scroll
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '200px',
    triggerOnce: false,
  })

  // Update visible products when products or limit changes
  useEffect(() => {
    if (products.length === 0) {
      setVisibleProducts([])
      setHasMore(false)
      return
    }

    const slicedProducts = products.slice(0, limit)
    setVisibleProducts(slicedProducts)
    setHasMore(slicedProducts.length < products.length)
  }, [products, limit])

  // Load more products when the load more element comes into view
  useEffect(() => {
    if (inView && hasMore && !loadingMore && !loading) {
      loadMore()
    }
  }, [inView, hasMore, loadingMore, loading])

  // Load more products
  const loadMore = useCallback(() => {
    if (!hasMore || loadingMore) return

    setLoadingMore(true)

    try {
      // Simulate loading delay
      setTimeout(() => {
        setLimit(prevLimit => prevLimit + incrementAmount)
        setLoadingMore(false)
      }, 500)
    } catch (error) {
      console.error("Error loading more products:", error)
      setLoadingMore(false)
    }
  }, [hasMore, loadingMore, incrementAmount])

  // Get grid columns class based on columns prop
  const getColumnsClass = () => {
    switch (columns) {
      case 1:
        return "grid-cols-1"
      case 2:
        return "grid-cols-1 sm:grid-cols-2"
      case 3:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
      case 4:
      default:
        return "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
    }
  }

  // Render loading skeletons
  if (loading) {
    return (
      <div className={`grid ${getColumnsClass()} gap-6 ${className}`}>
        {Array.from({ length: initialLimit }).map((_, index) => (
          <div key={index} className="space-y-3">
            <Skeleton className="h-[300px] w-full rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <div className="flex justify-between items-center pt-2">
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-9 w-20 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  // Render empty state
  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-500">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <>
      <div className={`grid ${getColumnsClass()} gap-6 ${className}`}>
        {visibleProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            variant={variant}
            className="h-full"
          />
        ))}
      </div>

      {hasMore && showLoadMore && (
        <div
          ref={ref}
          className="flex justify-center mt-8"
        >
          <Button
            ref={loadMoreRef}
            variant="outline"
            size="lg"
            onClick={loadMore}
            disabled={loadingMore}
          >
            {loadingMore ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              'Load More Products'
            )}
          </Button>
        </div>
      )}
    </>
  )
}
