"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Heart,
  ShoppingCart,
  Eye,
  Check,
  MapPin,
  Star,
  ArrowRight,
  Percent
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { OptimizedImage } from "@/components/ui/optimized-image"
import ProductImage from "@/components/product-image"
import type { Product } from "../types"
import { useCart } from "@/hooks/use-cart"
import { useState } from "react"
import { cn } from "@/lib/utils"

interface ProductCardProps {
  product: Product
  variant?: "default" | "compact" | "featured"
  className?: string
}

export default function ProductCard({
  product,
  variant = "default",
  className
}: ProductCardProps) {
  const { addItem } = useCart()
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsAddingToCart(true)
    addItem(product, 1)
    setTimeout(() => setIsAddingToCart(false), 800)
  }

  const getConditionBadgeColor = (condition: string) => {
    switch (condition) {
      case "New":
        return "bg-green-500 hover:bg-green-600"
      case "Like New":
        return "bg-blue-500 hover:bg-blue-600"
      case "Good":
        return "bg-teal-500 hover:bg-teal-600"
      case "Fair":
        return "bg-amber-500 hover:bg-amber-600"
      case "Poor":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-slate-500 hover:bg-slate-600"
    }
  }

  const calculateDiscount = () => {
    if (!product.originalPrice) return null
    const discount = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    return discount > 0 ? discount : null
  }

  const discount = calculateDiscount()

  if (variant === "compact") {
    return (
      <Card className={cn("overflow-hidden h-full transition-all hover:shadow-md", className)}>
        <Link href={`/product/${product.id}`} className="flex h-full">
          <div className="relative w-1/3 min-w-[100px]">
            <ProductImage
              product={product}
              width={100}
              height={100}
              fill={true}
              showActions={false}
              showCondition={false}
              showDiscount={true}
              linkToProduct={true}
              className="h-full"
            />
          </div>
          <CardContent className="p-3 flex-1">
            <h3 className="font-medium text-sm line-clamp-2">{product.name}</h3>
            <div className="mt-1 flex items-center gap-1">
              <p className="text-sm font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</p>
              {product.originalPrice && (
                <p className="text-xs text-slate-500 line-through">₹{product.originalPrice.toLocaleString("en-IN")}</p>
              )}
            </div>
            <div className="mt-1 flex items-center text-xs text-slate-500">
              <MapPin className="h-3 w-3 mr-1" />
              {product.location}
            </div>
          </CardContent>
        </Link>
      </Card>
    )
  }

  if (variant === "featured") {
    return (
      <Card className={cn("overflow-hidden transition-all hover:shadow-md", className)}>
        <div className="grid md:grid-cols-2 h-full">
          <div className="relative aspect-square md:aspect-auto">
            <ProductImage
              product={product}
              width={400}
              height={400}
              fill={true}
              priority={true}
              showActions={false}
              showCondition={true}
              showDiscount={true}
              className="h-full"
            />
          </div>
          <CardContent className="p-6 flex flex-col justify-between">
            <div>
              <h3 className="text-xl font-bold mb-2 line-clamp-2">{product.name}</h3>
              <div className="flex items-center gap-2 mb-4">
                <span className="text-2xl font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</span>
                {product.originalPrice && (
                  <span className="text-lg text-slate-500 line-through">
                    ₹{product.originalPrice.toLocaleString("en-IN")}
                  </span>
                )}
              </div>
              <p className="text-slate-600 mb-4 line-clamp-3">{product.description}</p>
              <div className="flex items-center text-sm text-slate-500 mb-2">
                <MapPin className="h-4 w-4 mr-1" />
                {product.location}
              </div>
              <div className="flex items-center text-sm text-slate-500">
                <Star className="h-4 w-4 mr-1 text-yellow-400 fill-yellow-400" />
                4.5 (24 reviews)
              </div>
            </div>
            <div className="flex gap-3 mt-6">
              <Button
                className="bg-teal-600 hover:bg-teal-700 flex-1"
                onClick={handleAddToCart}
                disabled={isAddingToCart}
              >
                {isAddingToCart ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Added
                  </>
                ) : (
                  <>
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </>
                )}
              </Button>
              <Button variant="outline" asChild className="flex-1">
                <Link href={`/product/${product.id}`}>
                  View Details
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </div>
      </Card>
    )
  }

  // Default variant
  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all hover:shadow-md h-full",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        <Badge
          className={cn(
            "absolute top-2 left-2 z-10",
            getConditionBadgeColor(product.condition)
          )}
        >
          {product.condition}
        </Badge>

        {discount && (
          <Badge className="absolute top-2 right-2 z-10 bg-red-500 hover:bg-red-600">
            -{discount}%
          </Badge>
        )}

        <div
          className={cn(
            "absolute inset-0 bg-black/60 z-20 flex items-center justify-center gap-3 opacity-0 transition-opacity",
            isHovered ? "opacity-100" : "opacity-0"
          )}
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="secondary"
                  className="rounded-full h-10 w-10"
                  onClick={handleAddToCart}
                  disabled={isAddingToCart}
                >
                  {isAddingToCart ? <Check className="h-5 w-5" /> : <ShoppingCart className="h-5 w-5" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isAddingToCart ? "Added to cart" : "Add to cart"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button size="icon" variant="secondary" className="rounded-full h-10 w-10">
                  <Heart className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Add to wishlist</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href={`/product/${product.id}`}>
                  <Button size="icon" variant="secondary" className="rounded-full h-10 w-10">
                    <Eye className="h-5 w-5" />
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>View details</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <ProductImage
          product={product}
          width={300}
          height={300}
          showActions={true}
          showCondition={false}
          priority={false}
        />
      </div>
      <CardContent className="p-4">
        <div className="flex items-center text-xs text-slate-500 mb-1">
          <MapPin className="h-3 w-3 mr-1" />
          {product.location}
        </div>
        <Link href={`/product/${product.id}`}>
          <h3 className="font-medium line-clamp-2 min-h-[48px] hover:text-teal-600 transition-colors">
            {product.name}
          </h3>
        </Link>
        <div className="mt-2 flex items-center justify-between">
          <div>
            <p className="text-lg font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</p>
            {product.originalPrice && (
              <p className="text-sm text-slate-500 line-through">₹{product.originalPrice.toLocaleString("en-IN")}</p>
            )}
          </div>
          <Button
            size="sm"
            className={cn(
              "rounded-full transition-all duration-300",
              isAddingToCart
                ? "bg-green-600 hover:bg-green-700"
                : "bg-teal-600 hover:bg-teal-700"
            )}
            onClick={handleAddToCart}
            disabled={isAddingToCart}
          >
            {isAddingToCart ? (
              <>
                <Check className="h-4 w-4 mr-1" />
                Added
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4 mr-1" />
                Add
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
