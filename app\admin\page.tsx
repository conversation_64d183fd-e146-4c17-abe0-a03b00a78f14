"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  BarChart3,
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  AlertCircle,
  Shield,
  CheckCircle2,
  XCircle,
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"

export default function AdminDashboard() {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState({
    totalSales: 0,
    totalOrders: 0,
    totalProducts: 0,
    totalUsers: 0,
    recentOrders: [],
    salesData: {
      thisMonth: 0,
      lastMonth: 0,
      growth: 0,
    },
    lowStockProducts: [],
    pendingSellerApprovals: 0,
  })

  // Fetch dashboard data
  useEffect(() => {
    // Simulate API call with mock data
    setTimeout(() => {
      setDashboardData({
        totalSales: 245000,
        totalOrders: 78,
        totalProducts: 156,
        totalUsers: 89,
        recentOrders: [
          {
            id: "ORD-001",
            customer: "Rahul Sharma",
            date: "2023-05-10",
            amount: 12999,
            status: "Delivered",
            items: 2,
          },
          {
            id: "ORD-002",
            customer: "Priya Patel",
            date: "2023-05-08",
            amount: 8499,
            status: "Shipped",
            items: 1,
          },
          {
            id: "ORD-003",
            customer: "Amit Kumar",
            date: "2023-05-05",
            amount: 24999,
            status: "Processing",
            items: 3,
          },
          {
            id: "ORD-004",
            customer: "Sneha Gupta",
            date: "2023-05-03",
            amount: 5999,
            status: "Delivered",
            items: 1,
          },
          {
            id: "ORD-005",
            customer: "Vikram Singh",
            date: "2023-05-01",
            amount: 36999,
            status: "Delivered",
            items: 2,
          },
        ],
        salesData: {
          thisMonth: 98500,
          lastMonth: 85000,
          growth: 15.9,
        },
        lowStockProducts: [
          {
            id: "PROD-001",
            name: "NVIDIA GeForce RTX 3070",
            stock: 2,
            threshold: 5,
          },
          {
            id: "PROD-002",
            name: "AMD Ryzen 9 5900X",
            stock: 3,
            threshold: 5,
          },
          {
            id: "PROD-003",
            name: "Samsung 970 EVO Plus 1TB SSD",
            stock: 4,
            threshold: 10,
          },
        ],
        pendingSellerApprovals: 3,
      })
      setIsLoading(false)
    }, 1000)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading dashboard data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-slate-500 mt-1">
            Welcome back, {user?.name || "Admin"}. Here's what's happening with your store today.
          </p>
        </div>
        <div className="flex gap-3">
          <Button asChild className="bg-teal-600 hover:bg-teal-700">
            <Link href="/admin/products/new">
              <Package className="h-4 w-4 mr-2" />
              Add New Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Authentication Status Card */}
      <Card className="bg-slate-50 border-slate-200">
        <CardContent className="p-4">
          <div className="flex items-center">
            <Shield className="h-5 w-5 text-teal-600 mr-3" />
            <div>
              <p className="text-sm font-medium">Authentication Status</p>
              <div className="flex items-center mt-1">
                {user ? (
                  <>
                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-700">Authenticated as Admin</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-500 mr-1" />
                    <span className="text-sm text-red-700">Not authenticated</span>
                  </>
                )}
              </div>
            </div>
            <Button variant="outline" size="sm" asChild className="ml-auto">
              <Link href="/health">System Health</Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alert for pending approvals */}
      {dashboardData.pendingSellerApprovals > 0 && (
        <div className="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-amber-400 mr-3" />
            <div>
              <p className="text-sm text-amber-700">
                <span className="font-medium">Attention needed:</span> You have {dashboardData.pendingSellerApprovals} pending seller approval
                {dashboardData.pendingSellerApprovals > 1 ? "s" : ""}.
              </p>
            </div>
            <Button variant="link" asChild className="ml-auto text-amber-700">
              <Link href="/admin/users/sellers?status=pending">Review now</Link>
            </Button>
          </div>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">₹{dashboardData.totalSales.toLocaleString("en-IN")}</h3>
                  </div>
                  <div className="bg-teal-100 p-3 rounded-full">
                    <DollarSign className="h-5 w-5 text-teal-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>12.5%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Orders</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalOrders}</h3>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <ShoppingCart className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>8.2%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Products</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalProducts}</h3>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-full">
                    <Package className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>4.3%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Users</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalUsers}</h3>
                  </div>
                  <div className="bg-amber-100 p-3 rounded-full">
                    <Users className="h-5 w-5 text-amber-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>6.8%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Orders */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Latest orders across the platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left font-medium p-2 pl-0">Order ID</th>
                      <th className="text-left font-medium p-2">Customer</th>
                      <th className="text-left font-medium p-2">Date</th>
                      <th className="text-left font-medium p-2">Amount</th>
                      <th className="text-left font-medium p-2">Status</th>
                      <th className="text-left font-medium p-2">Items</th>
                      <th className="text-right font-medium p-2 pr-0">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dashboardData.recentOrders.map((order: any) => (
                      <tr key={order.id} className="border-b">
                        <td className="p-2 pl-0">{order.id}</td>
                        <td className="p-2">{order.customer}</td>
                        <td className="p-2">{order.date}</td>
                        <td className="p-2">₹{order.amount.toLocaleString("en-IN")}</td>
                        <td className="p-2">
                          <Badge
                            className={
                              order.status === "Delivered"
                                ? "bg-green-100 text-green-800 hover:bg-green-100"
                                : order.status === "Shipped"
                                ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                                : "bg-amber-100 text-amber-800 hover:bg-amber-100"
                            }
                          >
                            {order.status}
                          </Badge>
                        </td>
                        <td className="p-2">{order.items}</td>
                        <td className="p-2 pr-0 text-right">
                          <Button variant="link" asChild className="h-auto p-0">
                            <Link href={`/admin/orders/${order.id}`}>View</Link>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-4 text-center">
                <Button variant="outline" asChild>
                  <Link href="/admin/orders">View All Orders</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other tabs will be implemented in separate files */}
        <TabsContent value="sales">
          <Card>
            <CardHeader>
              <CardTitle>Sales Analytics</CardTitle>
              <CardDescription>Detailed sales performance metrics</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Sales Analytics</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  Detailed sales analytics will be available here. Your sales this month are ₹
                  {dashboardData.salesData.thisMonth.toLocaleString("en-IN")}, which is {dashboardData.salesData.growth}% higher
                  than last month.
                </p>
                <Button variant="outline" className="mt-4" asChild>
                  <Link href="/admin/analytics">Go to Full Analytics</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Product Management</CardTitle>
              <CardDescription>Manage your product inventory</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <Package className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Product Management</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  Manage all your products, categories, and inventory from the product management section.
                </p>
                <Button variant="outline" className="mt-4" asChild>
                  <Link href="/admin/products">Go to Products</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>Manage users and seller accounts</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <Users className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">User Management</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  Manage all users, approve seller accounts, and handle user permissions from the user management section.
                </p>
                <Button variant="outline" className="mt-4" asChild>
                  <Link href="/admin/users">Go to Users</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
