"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Sheet<PERSON>eader, SheetTitle, SheetDescription, SheetFooter } from "@/components/ui/sheet"
import { Filter, X } from "lucide-react"

interface FilterOption {
  id: string
  label: string
  checked: boolean
}

interface MobileFilterSheetProps {
  categories: FilterOption[]
  conditions: FilterOption[]
  priceRange: [number, number]
  maxPrice: number
  sortOptions: { value: string; label: string }[]
  onApplyFilters: (filters: any) => void
  onResetFilters: () => void
}

export function MobileFilterSheet({
  categories,
  conditions,
  priceRange,
  maxPrice,
  sortOptions,
  onApplyFilters,
  onResetFilters,
}: MobileFilterSheetProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Initialize state from URL params
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    categories.filter(c => c.checked).map(c => c.id)
  )
  const [selectedConditions, setSelectedConditions] = useState<string[]>(
    conditions.filter(c => c.checked).map(c => c.id)
  )
  const [selectedPriceRange, setSelectedPriceRange] = useState<[number, number]>(priceRange)
  const [selectedSort, setSelectedSort] = useState<string>(
    searchParams.get("sort") || "newest"
  )
  
  // Handle category selection
  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    setSelectedCategories(prev => 
      checked 
        ? [...prev, categoryId]
        : prev.filter(id => id !== categoryId)
    )
  }
  
  // Handle condition selection
  const handleConditionChange = (conditionId: string, checked: boolean) => {
    setSelectedConditions(prev => 
      checked 
        ? [...prev, conditionId]
        : prev.filter(id => id !== conditionId)
    )
  }
  
  // Handle price range change
  const handlePriceRangeChange = (values: number[]) => {
    setSelectedPriceRange([values[0], values[1]])
  }
  
  // Handle sort change
  const handleSortChange = (value: string) => {
    setSelectedSort(value)
  }
  
  // Apply filters
  const applyFilters = () => {
    onApplyFilters({
      categories: selectedCategories,
      conditions: selectedConditions,
      priceRange: selectedPriceRange,
      sort: selectedSort,
    })
  }
  
  // Reset filters
  const resetFilters = () => {
    setSelectedCategories([])
    setSelectedConditions([])
    setSelectedPriceRange([0, maxPrice])
    setSelectedSort("newest")
    onResetFilters()
  }
  
  return (
    <>
      <SheetHeader className="px-1">
        <SheetTitle className="flex items-center">
          <Filter className="h-5 w-5 mr-2" />
          Filter & Sort
        </SheetTitle>
        <SheetDescription>
          Refine your product search with these filters
        </SheetDescription>
      </SheetHeader>
      
      <div className="flex flex-col gap-6 py-4 overflow-y-auto max-h-[60vh]">
        {/* Sort Options */}
        <div className="space-y-4">
          <h3 className="font-medium">Sort By</h3>
          <RadioGroup value={selectedSort} onValueChange={handleSortChange}>
            {sortOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`sort-${option.value}`} />
                <Label htmlFor={`sort-${option.value}`}>{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        
        {/* Price Range */}
        <div className="space-y-4">
          <h3 className="font-medium">Price Range</h3>
          <div className="px-2">
            <Slider
              defaultValue={selectedPriceRange}
              min={0}
              max={maxPrice}
              step={100}
              onValueChange={handlePriceRangeChange}
              className="my-6"
            />
            <div className="flex justify-between">
              <span>₹{selectedPriceRange[0].toLocaleString()}</span>
              <span>₹{selectedPriceRange[1].toLocaleString()}</span>
            </div>
          </div>
        </div>
        
        {/* Categories */}
        <Accordion type="single" collapsible defaultValue="categories">
          <AccordionItem value="categories" className="border-none">
            <AccordionTrigger className="py-2 font-medium">Categories</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={(checked) => 
                        handleCategoryChange(category.id, checked as boolean)
                      }
                    />
                    <Label htmlFor={`category-${category.id}`}>{category.label}</Label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        
        {/* Conditions */}
        <Accordion type="single" collapsible defaultValue="conditions">
          <AccordionItem value="conditions" className="border-none">
            <AccordionTrigger className="py-2 font-medium">Condition</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                {conditions.map((condition) => (
                  <div key={condition.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`condition-${condition.id}`}
                      checked={selectedConditions.includes(condition.id)}
                      onCheckedChange={(checked) => 
                        handleConditionChange(condition.id, checked as boolean)
                      }
                    />
                    <Label htmlFor={`condition-${condition.id}`}>{condition.label}</Label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      
      <SheetFooter className="flex-row gap-3 pt-2">
        <Button variant="outline" onClick={resetFilters} className="flex-1">
          <X className="h-4 w-4 mr-2" />
          Reset
        </Button>
        <Button onClick={applyFilters} className="flex-1 bg-teal-600 hover:bg-teal-700">
          Apply Filters
        </Button>
      </SheetFooter>
    </>
  )
}
