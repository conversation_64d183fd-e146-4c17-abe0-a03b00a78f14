"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  Home,
  Search,
  Heart,
  ShoppingCart,
  User,
  Menu,
  Package,
  Store,
  Settings,
  LogOut,
  Bell,
  MessageSquare,
  Grid3X3,
  Filter,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useCart } from "@/hooks/use-cart"
import { useWishlistContext } from "@/hooks/use-wishlist"

interface MobileNavigationProps {
  user?: {
    id: string
    name: string
    email: string
    role: string
    avatar?: string
  }
  onLogout?: () => void
}

const navigationItems = [
  { href: "/", icon: Home, label: "Home" },
  { href: "/search", icon: Search, label: "Search" },
  { href: "/wishlist", icon: Heart, label: "Wishlist" },
  { href: "/cart", icon: ShoppingCart, label: "Cart" },
  { href: "/account", icon: User, label: "Account" },
]

const categories = [
  { name: "Electronics", slug: "electronics", icon: "📱" },
  { name: "Clothing & Fashion", slug: "clothing-fashion", icon: "👕" },
  { name: "Home & Kitchen", slug: "home-kitchen", icon: "🏠" },
  { name: "Books & Media", slug: "books-media", icon: "📚" },
  { name: "Toys & Games", slug: "toys-games", icon: "🎮" },
  { name: "Computer Parts", slug: "computer-parts", icon: "💻" },
]

export function MobileNavigation({ user, onLogout }: MobileNavigationProps) {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [showSearch, setShowSearch] = useState(false)
  const { totalItems: cartItems } = useCart()
  const { totalItems: wishlistItems } = useWishlistContext()

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false)
    setShowSearch(false)
  }, [pathname])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  return (
    <>
      {/* Mobile Header */}
      <header className="lg:hidden sticky top-0 z-40 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container flex h-14 items-center px-4">
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="mr-2">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="p-4 border-b">
                  <div className="flex items-center justify-between">
                    <Link href="/" className="flex items-center space-x-2">
                      <div className="bg-teal-600 text-white p-2 rounded-lg">
                        <Package className="h-5 w-5" />
                      </div>
                      <span className="font-bold text-lg">PASSDOWN</span>
                    </Link>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>
                  
                  {user && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="bg-teal-600 text-white w-10 h-10 rounded-full flex items-center justify-center">
                          {user.avatar ? (
                            <img src={user.avatar} alt={user.name} className="w-full h-full rounded-full object-cover" />
                          ) : (
                            <span className="text-sm font-medium">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{user.name}</p>
                          <p className="text-xs text-gray-500 truncate">{user.email}</p>
                          <Badge variant="outline" className="mt-1 text-xs">
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Navigation */}
                <div className="flex-1 overflow-y-auto">
                  <div className="p-4 space-y-6">
                    {/* Quick Actions */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-3">Quick Actions</h3>
                      <div className="space-y-1">
                        <Link
                          href="/search"
                          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Search className="h-5 w-5 text-gray-400" />
                          <span>Search Products</span>
                        </Link>
                        <Link
                          href="/wishlist"
                          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Heart className="h-5 w-5 text-gray-400" />
                          <span>Wishlist</span>
                          {wishlistItems > 0 && (
                            <Badge variant="secondary" className="ml-auto">
                              {wishlistItems}
                            </Badge>
                          )}
                        </Link>
                        <Link
                          href="/cart"
                          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <ShoppingCart className="h-5 w-5 text-gray-400" />
                          <span>Shopping Cart</span>
                          {cartItems > 0 && (
                            <Badge variant="secondary" className="ml-auto">
                              {cartItems}
                            </Badge>
                          )}
                        </Link>
                      </div>
                    </div>

                    <Separator />

                    {/* Categories */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-3">Categories</h3>
                      <div className="space-y-1">
                        {categories.map((category) => (
                          <Link
                            key={category.slug}
                            href={`/category/${category.slug}`}
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <span className="text-lg">{category.icon}</span>
                            <span>{category.name}</span>
                          </Link>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Account & Settings */}
                    {user && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-3">Account</h3>
                        <div className="space-y-1">
                          <Link
                            href="/account"
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <User className="h-5 w-5 text-gray-400" />
                            <span>My Account</span>
                          </Link>
                          <Link
                            href="/account/orders"
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <Package className="h-5 w-5 text-gray-400" />
                            <span>My Orders</span>
                          </Link>
                          <Link
                            href="/account/messages"
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <MessageSquare className="h-5 w-5 text-gray-400" />
                            <span>Messages</span>
                          </Link>
                          {(user.role === 'seller' || user.role === 'admin') && (
                            <Link
                              href="/sell"
                              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                              <Store className="h-5 w-5 text-gray-400" />
                              <span>Seller Dashboard</span>
                            </Link>
                          )}
                          {user.role === 'admin' && (
                            <Link
                              href="/admin"
                              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                              <Settings className="h-5 w-5 text-gray-400" />
                              <span>Admin Panel</span>
                            </Link>
                          )}
                          <button
                            onClick={onLogout}
                            className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors w-full text-left text-red-600"
                          >
                            <LogOut className="h-5 w-5" />
                            <span>Sign Out</span>
                          </button>
                        </div>
                      </div>
                    )}

                    {!user && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-3">Account</h3>
                        <div className="space-y-2">
                          <Link href="/login">
                            <Button className="w-full bg-teal-600 hover:bg-teal-700">
                              Sign In
                            </Button>
                          </Link>
                          <Link href="/register">
                            <Button variant="outline" className="w-full">
                              Create Account
                            </Button>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 flex-1">
            <div className="bg-teal-600 text-white p-1.5 rounded">
              <Package className="h-4 w-4" />
            </div>
            <span className="font-bold text-lg">PASSDOWN</span>
          </Link>

          {/* Search Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowSearch(!showSearch)}
            className="mr-2"
          >
            <Search className="h-5 w-5" />
          </Button>

          {/* Cart Icon */}
          <Link href="/cart">
            <Button variant="ghost" size="icon" className="relative">
              <ShoppingCart className="h-5 w-5" />
              {cartItems > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                >
                  {cartItems}
                </Badge>
              )}
            </Button>
          </Link>
        </div>

        {/* Mobile Search Bar */}
        {showSearch && (
          <div className="border-t p-4">
            <form onSubmit={handleSearch} className="flex space-x-2">
              <Input
                type="search"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
                autoFocus
              />
              <Button type="submit" size="icon" className="bg-teal-600 hover:bg-teal-700">
                <Search className="h-4 w-4" />
              </Button>
            </form>
          </div>
        )}
      </header>

      {/* Bottom Navigation */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t">
        <div className="grid grid-cols-5 h-16">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex flex-col items-center justify-center space-y-1 transition-colors relative",
                  isActive
                    ? "text-teal-600"
                    : "text-gray-400 hover:text-gray-600"
                )}
              >
                <div className="relative">
                  <Icon className="h-5 w-5" />
                  {item.label === "Cart" && cartItems > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-xs"
                    >
                      {cartItems}
                    </Badge>
                  )}
                  {item.label === "Wishlist" && wishlistItems > 0 && (
                    <Badge
                      variant="secondary"
                      className="absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 text-xs"
                    >
                      {wishlistItems}
                    </Badge>
                  )}
                </div>
                <span className="text-xs font-medium">{item.label}</span>
              </Link>
            )
          })}
        </div>
      </nav>
    </>
  )
}
