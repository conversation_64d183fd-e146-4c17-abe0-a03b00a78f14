"use client"

import { useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { CreditCard, Landmark, Wallet } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/hooks/use-toast"

export default function CheckoutPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const { items, subtotal, clearCart } = useCart()
  const [isProcessing, setIsProcessing] = useState(false)

  // Calculate cart totals
  const shipping = items.length > 0 ? 499 : 0
  const tax = Math.round(subtotal * 0.18) // 18% GST
  const total = subtotal + shipping + tax

  // Redirect to cart if cart is empty
  if (typeof window !== "undefined" && items.length === 0) {
    router.push("/cart")
  }

  const handlePlaceOrder = () => {
    setIsProcessing(true)

    // Simulate payment processing
    setTimeout(() => {
      // In a real app, you would call your payment API here

      // Show success toast
      toast({
        title: "Order placed successfully!",
        description: "Your order has been placed and will be processed soon.",
      })

      // Clear cart
      clearCart()

      // Redirect to success page (we'll create this next)
      router.push("/checkout/success")
    }, 2000)
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Checkout Form */}
        <div className="w-full lg:w-2/3 space-y-8">
          {/* Shipping Information */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Shipping Information</h2>
              <Button variant="ghost" size="sm" className="text-teal-600">
                Use Saved Address
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first-name">First Name</Label>
                <Input id="first-name" />
              </div>
              <div>
                <Label htmlFor="last-name">Last Name</Label>
                <Input id="last-name" />
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input id="email" type="email" />
            </div>

            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input id="phone" type="tel" />
            </div>

            <div>
              <Label htmlFor="address">Address</Label>
              <Input id="address" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input id="city" />
              </div>
              <div>
                <Label htmlFor="state">State</Label>
                <Input id="state" />
              </div>
              <div>
                <Label htmlFor="pincode">PIN Code</Label>
                <Input id="pincode" />
              </div>
            </div>
          </div>

          <Separator />

          {/* Payment Method */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Payment Method</h2>

            <RadioGroup defaultValue="card">
              <div className="flex items-center space-x-2 border rounded-lg p-4">
                <RadioGroupItem value="card" id="card" />
                <Label htmlFor="card" className="flex items-center gap-2 cursor-pointer">
                  <CreditCard className="h-5 w-5 text-slate-500" />
                  Credit/Debit Card
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-lg p-4">
                <RadioGroupItem value="upi" id="upi" />
                <Label htmlFor="upi" className="flex items-center gap-2 cursor-pointer">
                  <Wallet className="h-5 w-5 text-slate-500" />
                  UPI
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-lg p-4">
                <RadioGroupItem value="netbanking" id="netbanking" />
                <Label htmlFor="netbanking" className="flex items-center gap-2 cursor-pointer">
                  <Landmark className="h-5 w-5 text-slate-500" />
                  Net Banking
                </Label>
              </div>
            </RadioGroup>

            <div className="pt-4 space-y-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="card-number">Card Number</Label>
                  <Input id="card-number" placeholder="1234 5678 9012 3456" />
                </div>
                <div>
                  <Label htmlFor="card-name">Name on Card</Label>
                  <Input id="card-name" placeholder="John Doe" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input id="expiry" placeholder="MM/YY" />
                </div>
                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input id="cvv" placeholder="123" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className="w-full lg:w-1/3">
          <div className="rounded-lg border overflow-hidden sticky top-24">
            <div className="bg-slate-50 p-4 border-b">
              <h2 className="font-medium">Order Summary</h2>
            </div>

            <div className="p-4">
              <div className="space-y-4 max-h-64 overflow-y-auto">
                {items.map((item) => (
                  <div key={item.id} className="flex gap-4">
                    <div className="relative w-16 h-16 rounded-md overflow-hidden border shrink-0">
                      <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium line-clamp-1">{item.name}</div>
                      <div className="text-sm text-slate-500">Qty: {item.quantity}</div>
                      <div className="font-medium text-teal-600">₹{item.price.toLocaleString("en-IN")}</div>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-600">Subtotal</span>
                  <span className="font-medium">₹{subtotal.toLocaleString("en-IN")}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-600">Shipping</span>
                  <span className="font-medium">₹{shipping.toLocaleString("en-IN")}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-600">Tax (18% GST)</span>
                  <span className="font-medium">₹{tax.toLocaleString("en-IN")}</span>
                </div>

                <div className="pt-4 border-t flex justify-between">
                  <span className="font-medium">Total</span>
                  <span className="font-bold text-lg">₹{total.toLocaleString("en-IN")}</span>
                </div>
              </div>

              <Button
                className="w-full bg-teal-600 hover:bg-teal-700 mt-6"
                onClick={handlePlaceOrder}
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Place Order"}
              </Button>

              <p className="text-xs text-center text-slate-500 mt-4">
                By placing your order, you agree to our Terms of Service and Privacy Policy
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
