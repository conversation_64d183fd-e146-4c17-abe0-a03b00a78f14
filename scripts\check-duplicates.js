// Check for duplicate files in the project
// Run with: node scripts/check-duplicates.js

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper function to print colored messages
function print(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Helper function to print section headers
function printSection(title) {
  console.log('\n');
  console.log(`${colors.bright}${colors.cyan}=== ${title} ===${colors.reset}`);
  console.log('');
}

// Directories to exclude from scanning
const excludeDirs = [
  'node_modules',
  '.git',
  '.next',
  'dist',
  'build',
  'coverage',
];

// File extensions to check
const includeExtensions = [
  '.js',
  '.jsx',
  '.ts',
  '.tsx',
  '.json',
  '.css',
  '.scss',
  '.html',
  '.md',
  '.bat',
  '.sh',
  '.sql',
];

// Function to calculate file hash
function calculateFileHash(filePath) {
  const fileBuffer = fs.readFileSync(filePath);
  const hashSum = crypto.createHash('sha256');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

// Function to get all files in a directory recursively
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(file)) {
        fileList = getAllFiles(filePath, fileList);
      }
    } else {
      const ext = path.extname(file).toLowerCase();
      if (includeExtensions.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

// Function to find duplicate files by content
function findDuplicatesByContent(files) {
  const hashMap = new Map();
  const duplicates = [];
  
  files.forEach(file => {
    try {
      const hash = calculateFileHash(file);
      
      if (hashMap.has(hash)) {
        hashMap.get(hash).push(file);
      } else {
        hashMap.set(hash, [file]);
      }
    } catch (error) {
      console.error(`Error processing file ${file}:`, error);
    }
  });
  
  // Filter out unique files
  hashMap.forEach((files, hash) => {
    if (files.length > 1) {
      duplicates.push(files);
    }
  });
  
  return duplicates;
}

// Function to find similar file names
function findSimilarFileNames(files) {
  const nameMap = new Map();
  const similar = [];
  
  files.forEach(file => {
    const fileName = path.basename(file).toLowerCase();
    
    if (nameMap.has(fileName)) {
      nameMap.get(fileName).push(file);
    } else {
      nameMap.set(fileName, [file]);
    }
  });
  
  // Filter out unique file names
  nameMap.forEach((files, name) => {
    if (files.length > 1) {
      similar.push(files);
    }
  });
  
  return similar;
}

// Main function
async function main() {
  printSection('Checking for Duplicate Files');
  print('Scanning project directory for duplicate files...', 'dim');
  
  try {
    const rootDir = process.cwd();
    print(`Root directory: ${rootDir}`, 'dim');
    
    // Get all files
    const files = getAllFiles(rootDir);
    print(`Found ${files.length} files to check`, 'dim');
    
    // Find duplicates by content
    const contentDuplicates = findDuplicatesByContent(files);
    
    // Find similar file names
    const similarNames = findSimilarFileNames(files);
    
    // Print results
    printSection('Results');
    
    if (contentDuplicates.length === 0) {
      print('✅ No duplicate files found by content', 'green');
    } else {
      print(`❌ Found ${contentDuplicates.length} sets of duplicate files by content:`, 'red');
      contentDuplicates.forEach((group, index) => {
        print(`\nDuplicate set #${index + 1}:`, 'yellow');
        group.forEach(file => {
          print(`  - ${path.relative(rootDir, file)}`, 'yellow');
        });
      });
    }
    
    if (similarNames.length === 0) {
      print('\n✅ No files with identical names found', 'green');
    } else {
      print(`\n⚠️ Found ${similarNames.length} sets of files with identical names:`, 'yellow');
      similarNames.forEach((group, index) => {
        print(`\nSimilar name set #${index + 1}: ${path.basename(group[0])}`, 'yellow');
        group.forEach(file => {
          print(`  - ${path.relative(rootDir, file)}`, 'yellow');
        });
      });
    }
    
    // Print summary
    printSection('Summary');
    print(`Total files scanned: ${files.length}`, 'bright');
    print(`Duplicate file sets by content: ${contentDuplicates.length}`, contentDuplicates.length > 0 ? 'red' : 'green');
    print(`Files with identical names: ${similarNames.length}`, similarNames.length > 0 ? 'yellow' : 'green');
    
    // Provide recommendations
    if (contentDuplicates.length > 0 || similarNames.length > 0) {
      printSection('Recommendations');
      
      if (contentDuplicates.length > 0) {
        print('For duplicate files by content:', 'bright');
        print('1. Review each set of duplicates and determine which ones to keep', 'dim');
        print('2. Delete unnecessary duplicates using the remove-files tool', 'dim');
        print('3. Update imports/references to point to the remaining file', 'dim');
      }
      
      if (similarNames.length > 0) {
        print('\nFor files with identical names:', 'bright');
        print('1. Check if these files serve different purposes in different directories', 'dim');
        print('2. Consider renaming files to be more descriptive of their specific purpose', 'dim');
        print('3. If appropriate, consolidate functionality into a single file', 'dim');
      }
    }
  } catch (error) {
    console.error('Error checking for duplicates:', error);
  }
}

// Run the main function
main();
