"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, ThumbsUp, ThumbsDown, Flag, MoreHorizontal, Verified } from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"

interface Review {
  id: string
  rating: number
  title: string
  content: string
  createdAt: string
  user: {
    id: string
    name: string
    avatar?: string
  }
  helpful: number
  notHelpful: number
  isVerifiedPurchase?: boolean
}

interface ReviewListProps {
  productId: string
  reviews: Review[]
  totalReviews: number
  averageRating: number
  ratingDistribution: { [key: number]: number }
  onLoadMore?: () => void
  hasMore?: boolean
  isLoading?: boolean
  className?: string
}

const sortOptions = [
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "highest", label: "Highest Rating" },
  { value: "lowest", label: "Lowest Rating" },
  { value: "helpful", label: "Most Helpful" }
]

export function ReviewList({
  productId,
  reviews,
  totalReviews,
  averageRating,
  ratingDistribution,
  onLoadMore,
  hasMore = false,
  isLoading = false,
  className
}: ReviewListProps) {
  const [sortBy, setSortBy] = useState("newest")
  const [filterRating, setFilterRating] = useState<number | null>(null)

  const filteredReviews = reviews.filter(review => 
    filterRating === null || review.rating === filterRating
  )

  const handleHelpful = async (reviewId: string, helpful: boolean) => {
    try {
      await fetch(`/api/reviews/${reviewId}/helpful`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ helpful }),
      })
      // Refresh reviews or update state
    } catch (error) {
      console.error("Error marking review as helpful:", error)
    }
  }

  const handleReport = async (reviewId: string) => {
    try {
      await fetch(`/api/reviews/${reviewId}/report`, {
        method: "POST",
      })
      // Show success message
    } catch (error) {
      console.error("Error reporting review:", error)
    }
  }

  const renderStars = (rating: number, size: "sm" | "md" | "lg" = "sm") => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-5 w-5",
      lg: "h-6 w-6"
    }

    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              sizeClasses[size],
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            )}
          />
        ))}
      </div>
    )
  }

  const renderRatingDistribution = () => {
    const maxCount = Math.max(...Object.values(ratingDistribution))
    
    return (
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((rating) => {
          const count = ratingDistribution[rating] || 0
          const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0
          
          return (
            <button
              key={rating}
              onClick={() => setFilterRating(filterRating === rating ? null : rating)}
              className={cn(
                "flex items-center gap-2 w-full text-left p-2 rounded hover:bg-gray-50 transition-colors",
                filterRating === rating && "bg-teal-50 border border-teal-200"
              )}
            >
              <span className="text-sm font-medium w-6">{rating}</span>
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-sm text-gray-600 w-8">{count}</span>
            </button>
          )
        })}
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Rating Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Overall Rating */}
            <div className="text-center md:text-left">
              <div className="flex items-center justify-center md:justify-start gap-2 mb-2">
                <span className="text-3xl font-bold">{averageRating.toFixed(1)}</span>
                {renderStars(Math.round(averageRating), "lg")}
              </div>
              <p className="text-gray-600">
                Based on {totalReviews} review{totalReviews !== 1 ? 's' : ''}
              </p>
            </div>

            {/* Rating Distribution */}
            <div>
              <h4 className="font-medium mb-3">Rating Breakdown</h4>
              {renderRatingDistribution()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {filterRating && (
            <Badge variant="secondary" className="flex items-center gap-1">
              {filterRating} star{filterRating !== 1 ? 's' : ''}
              <button
                onClick={() => setFilterRating(null)}
                className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
              >
                ×
              </button>
            </Badge>
          )}
        </div>

        <p className="text-sm text-gray-600">
          Showing {filteredReviews.length} of {totalReviews} reviews
        </p>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Review Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={review.user.avatar} />
                      <AvatarFallback>
                        {review.user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{review.user.name}</span>
                        {review.isVerifiedPurchase && (
                          <Badge variant="outline" className="text-xs">
                            <Verified className="h-3 w-3 mr-1" />
                            Verified Purchase
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        {renderStars(review.rating)}
                        <span className="text-sm text-gray-500">
                          {formatDistanceToNow(new Date(review.createdAt), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>

                {/* Review Content */}
                <div>
                  <h4 className="font-medium mb-2">{review.title}</h4>
                  <p className="text-gray-700 leading-relaxed">{review.content}</p>
                </div>

                {/* Review Actions */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => handleHelpful(review.id, true)}
                      className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ThumbsUp className="h-4 w-4" />
                      Helpful ({review.helpful})
                    </button>
                    <button
                      onClick={() => handleHelpful(review.id, false)}
                      className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ThumbsDown className="h-4 w-4" />
                      ({review.notHelpful})
                    </button>
                  </div>
                  
                  <button
                    onClick={() => handleReport(review.id)}
                    className="flex items-center gap-1 text-sm text-gray-500 hover:text-red-600 transition-colors"
                  >
                    <Flag className="h-4 w-4" />
                    Report
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={onLoadMore}
            disabled={isLoading}
            className="min-w-[120px]"
          >
            {isLoading ? "Loading..." : "Load More Reviews"}
          </Button>
        </div>
      )}

      {filteredReviews.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-gray-500">
              {filterRating 
                ? `No ${filterRating}-star reviews found.`
                : "No reviews yet. Be the first to review this product!"
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
