# Supabase Integration for PASSDOWN

This directory contains the necessary files to set up and configure Supabase for the PASSDOWN e-commerce platform.

## Setup Instructions

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and sign up or log in
2. Create a new project
3. Note your project URL and anon key (public API key)

### 2. Set Environment Variables

Create or update your `.env` file in the root directory with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
JWT_SECRET=your-secret-key-at-least-32-characters-long
```

### 3. Set Up Database Schema

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the contents of `schema.sql` from this directory
3. Paste and run the SQL to create all necessary tables and policies

### 4. Seed the Database

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the contents of `seed.sql` from this directory
3. Paste and run the SQL to populate the database with initial data

## Database Structure

The database consists of the following tables:

- `users`: User accounts with authentication information
- `seller_profiles`: Extended profiles for seller accounts
- `categories`: Product categories and subcategories
- `products`: Product listings with details
- `product_images`: Images associated with products
- `orders`: Customer orders
- `order_items`: Individual items within orders
- `wishlist`: User wishlist items

## Row Level Security (RLS)

The database uses Row Level Security to ensure data privacy and security:

- Public data (categories, active products) is readable by anyone
- User data is only accessible to the user who owns it
- Seller data is only modifiable by the seller who owns it
- Admin operations are restricted to users with the admin role

## API Usage

The Supabase client is configured in `lib/supabase.ts`. Use the helper functions in `lib/db-utils.ts` to interact with the database.

Example:

```typescript
import { getProducts } from '@/lib/db-utils';

// Get products with filtering and pagination
const { products, totalCount } = await getProducts({
  category: 'electronics',
  page: 1,
  limit: 10,
  sortBy: 'price',
  sortOrder: 'asc'
});
```

## Authentication

Authentication is handled through Supabase Auth with JWT tokens. The authentication flow is:

1. User signs up or logs in
2. JWT token is stored in HTTP-only cookies
3. Token is verified on protected routes
4. User role determines access to different features

## File Storage

Product images can be stored in Supabase Storage. Configure the storage bucket with appropriate permissions:

```sql
-- Create a public bucket for product images
INSERT INTO storage.buckets (id, name, public)
VALUES ('product-images', 'Product Images', true);

-- Allow authenticated users to upload images
CREATE POLICY "Authenticated users can upload product images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'product-images');

-- Allow anyone to view product images
CREATE POLICY "Anyone can view product images"
ON storage.objects FOR SELECT
TO anon
USING (bucket_id = 'product-images');
```

## Realtime Updates

Supabase supports realtime updates through WebSockets. Enable realtime for specific tables:

```sql
-- Enable realtime for products table
ALTER PUBLICATION supabase_realtime ADD TABLE products;
```

Then subscribe to changes in your frontend:

```typescript
const channel = supabase
  .channel('public:products')
  .on('postgres_changes', { event: '*', schema: 'public', table: 'products' }, (payload) => {
    console.log('Change received!', payload);
    // Update UI based on the change
  })
  .subscribe();
```
