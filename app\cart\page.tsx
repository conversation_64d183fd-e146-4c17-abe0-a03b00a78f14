"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Trash2, Plus, Minus, ShoppingBag, ArrowRight } from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import { useState } from "react"

export default function CartPage() {
  const { items, removeItem, updateQuantity, clearCart, subtotal } = useCart()
  const [couponCode, setCouponCode] = useState("")
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false)

  // Calculate cart totals
  const shipping = items.length > 0 ? 499 : 0
  const tax = Math.round(subtotal * 0.18) // 18% GST
  const total = subtotal + shipping + tax

  const handleApplyCoupon = () => {
    if (!couponCode) return

    setIsApplyingCoupon(true)
    // Simulate API call
    setTimeout(() => {
      setIsApplyingCoupon(false)
      // For now, just clear the coupon code
      setCouponCode("")
      // In a real app, you would apply the discount
    }, 1000)
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <h1 className="text-2xl md:text-3xl font-bold mb-8">Shopping Cart</h1>

      {items.length > 0 ? (
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Cart Items */}
          <div className="w-full lg:w-2/3">
            <div className="rounded-lg border overflow-hidden">
              <div className="bg-slate-50 p-4 border-b">
                <div className="grid grid-cols-12 gap-4">
                  <div className="col-span-6 font-medium">Product</div>
                  <div className="col-span-2 font-medium text-center">Price</div>
                  <div className="col-span-2 font-medium text-center">Quantity</div>
                  <div className="col-span-2 font-medium text-right">Total</div>
                </div>
              </div>

              <div className="divide-y">
                {items.map((item) => (
                  <div key={item.id} className="p-4">
                    <div className="grid grid-cols-12 gap-4 items-center">
                      <div className="col-span-6">
                        <div className="flex items-center gap-4">
                          <div className="relative w-16 h-16 rounded-md overflow-hidden border">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <Link href={`/product/${item.id}`} className="font-medium hover:text-teal-600">
                              {item.name}
                            </Link>
                            <div className="text-sm text-slate-500">Condition: {item.condition}</div>
                            <div className="text-sm text-slate-500">Seller: {item.seller}</div>
                          </div>
                        </div>
                      </div>

                      <div className="col-span-2 text-center">
                        <div className="font-medium text-teal-600">₹{item.price.toLocaleString("en-IN")}</div>
                        {item.originalPrice && (
                          <div className="text-sm text-slate-500 line-through">
                            ₹{item.originalPrice.toLocaleString("en-IN")}
                          </div>
                        )}
                      </div>

                      <div className="col-span-2">
                        <div className="flex items-center justify-center">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 rounded-r-none"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                            <span className="sr-only">Decrease quantity</span>
                          </Button>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => {
                              const value = parseInt(e.target.value)
                              if (!isNaN(value) && value >= 1) {
                                updateQuantity(item.id, value)
                              }
                            }}
                            className="h-8 w-12 rounded-none text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 rounded-l-none"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                            <span className="sr-only">Increase quantity</span>
                          </Button>
                        </div>
                      </div>

                      <div className="col-span-2 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <div className="font-medium">₹{(item.price * item.quantity).toLocaleString("en-IN")}</div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-slate-500 hover:text-red-500"
                            onClick={() => removeItem(item.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Remove item</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-6 flex flex-wrap gap-4">
              <div className="flex-1">
                <Button variant="outline" asChild className="w-full">
                  <Link href="/">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Continue Shopping
                  </Link>
                </Button>
              </div>
              <div className="flex items-center gap-2 flex-1">
                <Input
                  placeholder="Coupon code"
                  className="flex-1"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                />
                <Button
                  onClick={handleApplyCoupon}
                  disabled={!couponCode || isApplyingCoupon}
                >
                  {isApplyingCoupon ? "Applying..." : "Apply"}
                </Button>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="w-full lg:w-1/3">
            <div className="rounded-lg border overflow-hidden">
              <div className="bg-slate-50 p-4 border-b">
                <h2 className="font-medium">Order Summary</h2>
              </div>

              <div className="p-4 space-y-4">
                <div className="flex justify-between">
                  <span className="text-slate-600">Subtotal</span>
                  <span className="font-medium">₹{subtotal.toLocaleString("en-IN")}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-600">Shipping</span>
                  <span className="font-medium">₹{shipping.toLocaleString("en-IN")}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-slate-600">Tax (18% GST)</span>
                  <span className="font-medium">₹{tax.toLocaleString("en-IN")}</span>
                </div>

                <div className="pt-4 border-t flex justify-between">
                  <span className="font-medium">Total</span>
                  <span className="font-bold text-lg">₹{total.toLocaleString("en-IN")}</span>
                </div>

                <Button
                  className="w-full bg-teal-600 hover:bg-teal-700 mt-4"
                  asChild
                >
                  <Link href="/checkout">
                    Proceed to Checkout
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>

                <div className="text-xs text-center text-slate-500 mt-4">Secure checkout powered by Razorpay</div>
              </div>
            </div>

            <div className="mt-6 space-y-4 p-4 rounded-lg border">
              <h3 className="font-medium">Accepted Payment Methods</h3>
              <div className="flex flex-wrap gap-2">
                {["UPI", "Credit Card", "Debit Card", "Net Banking", "Wallet"].map((method) => (
                  <div key={method} className="px-3 py-1 bg-slate-100 rounded-md text-xs">
                    {method}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 mb-4">
            <ShoppingBag className="h-8 w-8 text-slate-500" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-slate-500 mb-6">Looks like you haven't added any items to your cart yet.</p>
          <Button asChild>
            <Link href="/">Start Shopping</Link>
          </Button>
        </div>
      )}
    </div>
  )
}
