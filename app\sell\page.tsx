"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from "@/components/ui/select"
import { <PERSON>bs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Upload, Camera, Info, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/hooks/use-auth"

export default function SellPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isLoading } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([])

  // Redirect to login if not authenticated or not a seller
  useEffect(() => {
    if (!isLoading && (!user || (user.role !== "seller" && user.role !== "admin"))) {
      router.push("/login?callbackUrl=/sell")
    }
  }, [user, isLoading, router])

  // Show loading or redirect if not authenticated
  if (isLoading || !user || (user.role !== "seller" && user.role !== "admin")) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your account information.</p>
        </div>
      </div>
    )
  }

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    // In a real app, you would upload these files to a storage service
    // For now, we'll just create object URLs for preview
    const newPhotos = Array.from(files).map((file) => URL.createObjectURL(file))
    setUploadedPhotos((prev) => [...prev, ...newPhotos].slice(0, 4)) // Limit to 4 photos
  }

  const handleSubmit = () => {
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Product listed successfully!",
        description: "Your product has been listed and is now visible to buyers.",
      })

      // Redirect to account/listings
      router.push("/account/listings")
    }, 2000)
  }
  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Sell Your Tech</h1>
        <p className="text-slate-500 mb-8">List your computer components and tech items for sale on PASSDOWN</p>

        <Tabs defaultValue="details" className="space-y-8">
          <TabsList className="w-full grid grid-cols-3">
            <TabsTrigger value="details">Product Details</TabsTrigger>
            <TabsTrigger value="photos">Photos</TabsTrigger>
            <TabsTrigger value="pricing">Pricing & Shipping</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Electronics</SelectLabel>
                      <SelectItem value="electronics-smartphones">Smartphones</SelectItem>
                      <SelectItem value="electronics-laptops">Laptops and Computers</SelectItem>
                      <SelectItem value="electronics-tablets">Tablets</SelectItem>
                      <SelectItem value="electronics-smartwatches">Smartwatches</SelectItem>
                      <SelectItem value="electronics-headphones">Headphones and Earbuds</SelectItem>
                      <SelectItem value="electronics-cameras">Cameras</SelectItem>
                      <SelectItem value="electronics-gaming">Gaming Consoles</SelectItem>
                      <SelectItem value="electronics-smarthome">Smart Home Devices</SelectItem>
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>Clothing & Fashion</SelectLabel>
                      <SelectItem value="clothing-womens">Women's Clothing</SelectItem>
                      <SelectItem value="clothing-mens">Men's Clothing</SelectItem>
                      <SelectItem value="clothing-kids">Children's Clothing</SelectItem>
                      <SelectItem value="clothing-shoes">Shoes</SelectItem>
                      <SelectItem value="clothing-accessories">Accessories</SelectItem>
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>Computer Parts</SelectLabel>
                      <SelectItem value="graphics-cards">Graphics Cards</SelectItem>
                      <SelectItem value="processors">Processors</SelectItem>
                      <SelectItem value="motherboards">Motherboards</SelectItem>
                      <SelectItem value="ram">RAM</SelectItem>
                      <SelectItem value="storage">Storage</SelectItem>
                    </SelectGroup>
                    {/* Add more category groups as needed */}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="title">Product Title</Label>
                <Input id="title" placeholder="e.g., NVIDIA GeForce RTX 3070 8GB Graphics Card" />
                <p className="text-xs text-slate-500 mt-1">Include key details like brand, model, and specifications</p>
              </div>

              <div>
                <Label htmlFor="condition">Condition</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="like-new">Like New</SelectItem>
                    <SelectItem value="used">Used</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your item in detail. Include information about its condition, usage history, and any accessories included."
                  className="min-h-32"
                />
                <p className="text-xs text-slate-500 mt-1">
                  Be honest and thorough about the condition and any defects
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="brand">Brand</Label>
                  <Input id="brand" placeholder="e.g., NVIDIA, AMD, Intel" />
                </div>
                <div>
                  <Label htmlFor="model">Model</Label>
                  <Input id="model" placeholder="e.g., RTX 3070, Ryzen 7 5800X" />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button>Continue to Photos</Button>
            </div>
          </TabsContent>

          <TabsContent value="photos" className="space-y-6">
            <div className="space-y-4">
              <div className="border-2 border-dashed rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="rounded-full bg-slate-100 p-3">
                    <Upload className="h-6 w-6 text-slate-500" />
                  </div>
                  <div>
                    <p className="font-medium">Drag and drop your photos here</p>
                    <p className="text-sm text-slate-500">or click to browse files</p>
                  </div>
                  <Button variant="outline" asChild>
                    <label>
                      Upload Photos
                      <input type="file" accept="image/*" multiple className="hidden" onChange={handlePhotoUpload} />
                    </label>
                  </Button>
                </div>
              </div>

              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-slate-500 shrink-0 mt-0.5" />
                <div className="text-sm text-slate-500">
                  <p className="font-medium text-slate-700">Photo Tips:</p>
                  <ul className="list-disc list-inside space-y-1 mt-1">
                    <li>Upload at least 4 clear photos from different angles</li>
                    <li>Include photos of any defects or wear</li>
                    <li>Take photos in good lighting</li>
                    <li>Show the product powered on if applicable</li>
                  </ul>
                </div>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6">
                {uploadedPhotos.length > 0 ? (
                  // Show uploaded photos
                  uploadedPhotos.map((photo, i) => (
                    <div
                      key={i}
                      className="aspect-square rounded-lg border relative overflow-hidden"
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img src={photo} alt={`Product photo ${i + 1}`} className="w-full h-full object-cover" />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={() => setUploadedPhotos(uploadedPhotos.filter((_, index) => index !== i))}
                      >
                        ×
                      </Button>
                    </div>
                  ))
                ) : (
                  // Show empty photo slots
                  [...Array(4)].map((_, i) => (
                    <div
                      key={i}
                      className="aspect-square rounded-lg border-2 border-dashed flex items-center justify-center"
                    >
                      <div className="text-center">
                        <Camera className="h-6 w-6 text-slate-400 mx-auto" />
                        <span className="text-xs text-slate-500 mt-1 block">Photo {i + 1}</span>
                      </div>
                    </div>
                  ))
                )}

                {/* Add more photos if less than 4 */}
                {uploadedPhotos.length > 0 && uploadedPhotos.length < 4 && (
                  [...Array(4 - uploadedPhotos.length)].map((_, i) => (
                    <div
                      key={i}
                      className="aspect-square rounded-lg border-2 border-dashed flex items-center justify-center"
                    >
                      <div className="text-center">
                        <Camera className="h-6 w-6 text-slate-400 mx-auto" />
                        <span className="text-xs text-slate-500 mt-1 block">Photo {uploadedPhotos.length + i + 1}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline">Back</Button>
              <Button>Continue to Pricing</Button>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="price">Price (₹)</Label>
                  <Input id="price" type="number" placeholder="e.g., 42999" />
                  <p className="text-xs text-slate-500 mt-1">
                    Set a competitive price based on condition and market value
                  </p>
                </div>
                <div>
                  <Label htmlFor="original-price">Original Price (₹) (Optional)</Label>
                  <Input id="original-price" type="number" placeholder="e.g., 54999" />
                  <p className="text-xs text-slate-500 mt-1">The original retail price when new</p>
                </div>
              </div>

              <div>
                <Label htmlFor="shipping-method">Shipping Method</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select shipping method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="seller-ships">I'll Ship It</SelectItem>
                    <SelectItem value="local-pickup">Local Pickup Only</SelectItem>
                    <SelectItem value="both">Both Shipping & Pickup</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="location">Your Location</Label>
                <Input id="location" placeholder="e.g., Mumbai, Maharashtra" />
                <p className="text-xs text-slate-500 mt-1">City and state where the item is located</p>
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Listing Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Listing Price</span>
                    <span>₹0.00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">PASSDOWN Fee (5%)</span>
                    <span>₹0.00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Payment Processing (2%)</span>
                    <span>₹0.00</span>
                  </div>
                  <div className="pt-2 border-t flex justify-between font-medium">
                    <span>You'll Receive</span>
                    <span>₹0.00</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline">Back</Button>
              <Button
                className="bg-teal-600 hover:bg-teal-700"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Listing Item...
                  </>
                ) : (
                  "List Item for Sale"
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

