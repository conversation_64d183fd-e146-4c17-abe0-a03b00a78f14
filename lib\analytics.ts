import { supabase } from './supabase';
import { format, subDays, startOfDay, endOfDay, startOfMonth, endOfMonth } from 'date-fns';

export interface AnalyticsData {
  date: string;
  totalSales: number;
  totalOrders: number;
  newUsers: number;
  pageViews: number;
}

export interface ProductAnalytics {
  productId: string;
  productName: string;
  totalSales: number;
  quantity: number;
  views: number;
}

export interface CategoryAnalytics {
  categoryId: string;
  categoryName: string;
  totalSales: number;
  productCount: number;
}

export interface UserAnalytics {
  role: string;
  count: number;
  percentage: number;
}

/**
 * Get daily analytics data for a specified date range
 */
export async function getDailyAnalytics(
  startDate: Date,
  endDate: Date = new Date()
): Promise<AnalyticsData[]> {
  try {
    const { data, error } = await supabase
      .from('analytics')
      .select('*')
      .gte('date', format(startDate, 'yyyy-MM-dd'))
      .lte('date', format(endDate, 'yyyy-MM-dd'))
      .order('date', { ascending: true });

    if (error) {
      throw error;
    }

    return data.map(item => ({
      date: item.date,
      totalSales: item.total_sales,
      totalOrders: item.total_orders,
      newUsers: item.new_users,
      pageViews: item.page_views,
    }));
  } catch (error) {
    console.error('Error fetching daily analytics:', error);
    return [];
  }
}

/**
 * Get analytics data for the last N days
 */
export async function getLastNDaysAnalytics(days: number = 7): Promise<AnalyticsData[]> {
  const startDate = subDays(new Date(), days);
  return getDailyAnalytics(startDate);
}

/**
 * Get analytics data for the current month
 */
export async function getCurrentMonthAnalytics(): Promise<AnalyticsData[]> {
  const now = new Date();
  const startDate = startOfMonth(now);
  const endDate = endOfMonth(now);
  return getDailyAnalytics(startDate, endDate);
}

/**
 * Get top selling products for a specified date range
 */
export async function getTopSellingProducts(
  startDate: Date,
  endDate: Date = new Date(),
  limit: number = 10
): Promise<ProductAnalytics[]> {
  try {
    const { data, error } = await supabase
      .rpc('get_top_selling_products', {
        start_date: format(startDate, 'yyyy-MM-dd'),
        end_date: format(endDate, 'yyyy-MM-dd'),
        limit_count: limit,
      });

    if (error) {
      throw error;
    }

    return data.map(item => ({
      productId: item.product_id,
      productName: item.product_name,
      totalSales: item.total_sales,
      quantity: item.quantity,
      views: item.views,
    }));
  } catch (error) {
    console.error('Error fetching top selling products:', error);
    
    // Fallback to mock data if the RPC function doesn't exist yet
    return getMockTopSellingProducts(limit);
  }
}

/**
 * Get category performance for a specified date range
 */
export async function getCategoryAnalytics(
  startDate: Date,
  endDate: Date = new Date()
): Promise<CategoryAnalytics[]> {
  try {
    const { data, error } = await supabase
      .rpc('get_category_analytics', {
        start_date: format(startDate, 'yyyy-MM-dd'),
        end_date: format(endDate, 'yyyy-MM-dd'),
      });

    if (error) {
      throw error;
    }

    return data.map(item => ({
      categoryId: item.category_id,
      categoryName: item.category_name,
      totalSales: item.total_sales,
      productCount: item.product_count,
    }));
  } catch (error) {
    console.error('Error fetching category analytics:', error);
    
    // Fallback to mock data if the RPC function doesn't exist yet
    return getMockCategoryAnalytics();
  }
}

/**
 * Get user distribution by role
 */
export async function getUserAnalytics(): Promise<UserAnalytics[]> {
  try {
    const { data, error } = await supabase
      .rpc('get_user_distribution');

    if (error) {
      throw error;
    }

    return data.map(item => ({
      role: item.role,
      count: item.count,
      percentage: item.percentage,
    }));
  } catch (error) {
    console.error('Error fetching user analytics:', error);
    
    // Fallback to mock data if the RPC function doesn't exist yet
    return getMockUserAnalytics();
  }
}

/**
 * Record a page view for analytics
 */
export async function recordPageView(page: string): Promise<void> {
  try {
    const today = format(new Date(), 'yyyy-MM-dd');
    
    // First, try to update the existing record for today
    const { data, error } = await supabase
      .from('analytics')
      .select('id, page_views')
      .eq('date', today)
      .single();
    
    if (error && error.code !== 'PGSQL_ERROR_NO_DATA') {
      throw error;
    }
    
    if (data) {
      // Update existing record
      await supabase
        .from('analytics')
        .update({ page_views: data.page_views + 1 })
        .eq('id', data.id);
    } else {
      // Create new record for today
      await supabase
        .from('analytics')
        .insert({
          date: today,
          page_views: 1,
          total_sales: 0,
          total_orders: 0,
          new_users: 0,
        });
    }
    
    // Also record the specific page view
    await supabase
      .from('page_views')
      .insert({
        page,
        timestamp: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Error recording page view:', error);
  }
}

// Mock data functions for fallback when Supabase RPC functions are not yet available

function getMockTopSellingProducts(limit: number = 10): ProductAnalytics[] {
  return [
    { productId: '1', productName: 'NVIDIA GeForce RTX 3070', totalSales: 128997, quantity: 3, views: 245 },
    { productId: '2', productName: 'AMD Ryzen 7 5800X', totalSales: 74997, quantity: 3, views: 189 },
    { productId: '3', productName: 'Dell XPS 13 (2022)', totalSales: 179998, quantity: 2, views: 312 },
    { productId: '4', productName: 'ASUS ROG Strix B550-F', totalSales: 31998, quantity: 2, views: 156 },
    { productId: '5', productName: 'Corsair Vengeance LPX 32GB', totalSales: 29997, quantity: 3, views: 134 },
    { productId: '6', productName: 'Samsung 970 EVO Plus 1TB', totalSales: 25497, quantity: 3, views: 178 },
    { productId: '7', productName: 'Logitech G Pro X Keyboard', totalSales: 15998, quantity: 2, views: 98 },
    { productId: '8', productName: 'Cooler Master Hyper 212', totalSales: 8997, quantity: 3, views: 76 },
  ].slice(0, limit);
}

function getMockCategoryAnalytics(): CategoryAnalytics[] {
  return [
    { categoryId: '1', categoryName: 'Graphics Cards', totalSales: 128997, productCount: 3 },
    { categoryId: '2', categoryName: 'Processors', totalSales: 74997, productCount: 5 },
    { categoryId: '3', categoryName: 'Laptops', totalSales: 179998, productCount: 4 },
    { categoryId: '4', categoryName: 'Motherboards', totalSales: 31998, productCount: 6 },
    { categoryId: '5', categoryName: 'RAM', totalSales: 29997, productCount: 7 },
    { categoryId: '6', categoryName: 'Storage', totalSales: 25497, productCount: 8 },
  ];
}

function getMockUserAnalytics(): UserAnalytics[] {
  return [
    { role: 'buyer', count: 120, percentage: 75 },
    { role: 'seller', count: 35, percentage: 21.9 },
    { role: 'admin', count: 5, percentage: 3.1 },
  ];
}
