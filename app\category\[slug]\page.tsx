import ProductCard from "@/app/components/product-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { SlidersHorizontal } from "lucide-react"
import { getProducts } from "@/lib/db-utils"
import { allExpandedProducts } from "@/app/data/expanded-products"
import type { Product } from "@/app/types"

async function getProductsByCategory(slug: string): Promise<Product[]> {
  try {
    // Try to get products from Supabase
    const { products } = await getProducts({
      category: slug,
      limit: 50,
    });

    if (products.length === 0) {
      console.log("No products found in database for category, using mock data");
      // Filter mock data by category
      return allExpandedProducts.filter(product => product.category === slug);
    }

    return products;
  } catch (error) {
    console.error("Error fetching products by category:", error);
    // Fallback to mock data
    return allExpandedProducts.filter(product => product.category === slug);
  }
}

export default async function CategoryPage({ params }: { params: { slug: string } }) {
  // Use await Promise.resolve to handle async params
  const slug = await Promise.resolve(params.slug);

  // Get products by category
  const products = await getProductsByCategory(slug);

  // Format category name for display
  const categoryName = slug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters sidebar */}
        <div className="w-full md:w-64 shrink-0">
          <div className="sticky top-24 space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">Filters</h2>
              <Button variant="ghost" size="sm" className="h-8 text-xs">
                Clear All
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-3">Price Range</h3>
                <div className="space-y-4">
                  <Slider defaultValue={[5000, 50000]} min={0} max={100000} step={1000} />
                  <div className="flex items-center justify-between">
                    <span className="text-sm">₹5,000</span>
                    <span className="text-sm">₹50,000</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-3">Condition</h3>
                <div className="space-y-2">
                  {["New", "Like New", "Used"].map((condition) => (
                    <div key={condition} className="flex items-center space-x-2">
                      <Checkbox id={`condition-${condition}`} />
                      <label
                        htmlFor={`condition-${condition}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {condition}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-3">Brand</h3>
                <div className="space-y-2">
                  {["NVIDIA", "AMD", "Intel", "ASUS", "MSI", "Gigabyte"].map((brand) => (
                    <div key={brand} className="flex items-center space-x-2">
                      <Checkbox id={`brand-${brand}`} />
                      <label
                        htmlFor={`brand-${brand}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {brand}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-3">Location</h3>
                <div className="space-y-2">
                  {["Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai"].map((location) => (
                    <div key={location} className="flex items-center space-x-2">
                      <Checkbox id={`location-${location}`} />
                      <label
                        htmlFor={`location-${location}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {location}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Products grid */}
        <div className="flex-1">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
            <h1 className="text-2xl md:text-3xl font-bold">{categoryName}</h1>

            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" className="md:hidden">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
              </Button>

              <select className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm">
                <option value="featured">Featured</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="newest">Newest First</option>
              </select>
            </div>
          </div>

          {products.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold mb-2">No products found</h2>
              <p className="text-slate-500">Try adjusting your filters or check back later for new listings.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
