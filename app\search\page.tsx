"use client"

import { useState, useEffect, useCallback } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  SlidersHorizontal,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import ProductCard from "@/app/components/product-card"
import ProductGrid from "@/components/product-grid"
import { allExpandedProducts } from "@/app/data/expanded-products"
import { supabase } from "@/lib/supabase"
import type { Product } from "@/app/types"

export default function SearchPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const query = searchParams.get("q") || ""
  const [searchInput, setSearchInput] = useState(query)
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [priceRange, setPriceRange] = useState([0, 100000])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedConditions, setSelectedConditions] = useState<string[]>([])
  const [sortOption, setSortOption] = useState("newest")
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const productsPerPage = 12

  // Debounce search input
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      // Create new URLSearchParams object based on current query parameters
      const params = new URLSearchParams(searchParams.toString())

      // Update or remove the 'q' parameter based on the search value
      if (value) {
        params.set("q", value)
      } else {
        params.delete("q")
      }

      // Update the URL with the new parameters
      router.push(`/search?${params.toString()}`)
    }, 300),
    [router, searchParams]
  )

  // Debounce function
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null

    return function(...args: Parameters<T>) {
      if (timeout) clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchInput(value)
    debouncedSearch(value)
  }

  // Categories from the products
  const categories = Array.from(new Set(allExpandedProducts.map(p => p.category)))

  // Conditions from the products
  const conditions = ["New", "Like New", "Good", "Fair", "Poor"]

  // Filter and search products
  useEffect(() => {
    setLoading(true)

    // Try to fetch from Supabase first, fallback to mock data
    const fetchProducts = async () => {
      try {
        // Try to fetch from Supabase
        const { data, error } = await supabase
          .from('products')
          .select(`
            *,
            categories (*),
            users!seller_id (*),
            product_images (*)
          `)

        if (error || !data || data.length === 0) {
          // Fallback to mock data
          console.log("Using mock data for search results")
          let filteredProducts = [...allExpandedProducts]

          // Filter by search query
          if (query) {
            filteredProducts = filteredProducts.filter(
              product =>
                product.name.toLowerCase().includes(query.toLowerCase()) ||
                product.description.toLowerCase().includes(query.toLowerCase())
            )
          }

      // Filter by price range
      filteredProducts = filteredProducts.filter(
        product => product.price >= priceRange[0] && product.price <= priceRange[1]
      )

      // Filter by categories
      if (selectedCategories.length > 0) {
        filteredProducts = filteredProducts.filter(
          product => selectedCategories.includes(product.category)
        )
      }

      // Filter by conditions
      if (selectedConditions.length > 0) {
        filteredProducts = filteredProducts.filter(
          product => selectedConditions.includes(product.condition)
        )
      }

      // Sort products
      switch (sortOption) {
        case "price-low":
          filteredProducts.sort((a, b) => a.price - b.price)
          break
        case "price-high":
          filteredProducts.sort((a, b) => b.price - a.price)
          break
        case "newest":
          // In a real app, you would sort by date
          // For now, we'll just use the default order
          break
        case "popular":
          // In a real app, you would sort by popularity
          // For now, we'll just use a random order
          filteredProducts.sort(() => Math.random() - 0.5)
          break
      }

          setProducts(filteredProducts)
          setLoading(false)
        } else {
          // Transform Supabase data to match Product type
          let transformedProducts = data.map(item => ({
            id: item.id,
            name: item.name,
            description: item.description,
            price: item.price,
            originalPrice: item.original_price,
            image: item.main_image || item.product_images[0]?.url || '/placeholder.svg',
            condition: item.condition as any,
            category: item.categories.slug as any,
            subcategory: item.categories.slug.split('-')[0] as any,
            seller: item.users.name || "Unknown Seller",
            location: item.location,
          }))

          // Filter by search query
          if (query) {
            transformedProducts = transformedProducts.filter(
              product =>
                product.name.toLowerCase().includes(query.toLowerCase()) ||
                product.description.toLowerCase().includes(query.toLowerCase())
            )
          }

          // Filter by price range
          transformedProducts = transformedProducts.filter(
            product => product.price >= priceRange[0] && product.price <= priceRange[1]
          )

          // Filter by categories
          if (selectedCategories.length > 0) {
            transformedProducts = transformedProducts.filter(
              product => selectedCategories.includes(product.category)
            )
          }

          // Filter by conditions
          if (selectedConditions.length > 0) {
            transformedProducts = transformedProducts.filter(
              product => selectedConditions.includes(product.condition)
            )
          }

          // Sort products
          switch (sortOption) {
            case "price-low":
              transformedProducts.sort((a, b) => a.price - b.price)
              break
            case "price-high":
              transformedProducts.sort((a, b) => b.price - a.price)
              break
            case "newest":
              // In a real app, you would sort by date
              // For now, we'll just use the default order
              break
            case "popular":
              // In a real app, you would sort by popularity
              // For now, we'll just use a random order
              transformedProducts.sort(() => Math.random() - 0.5)
              break
          }

          setProducts(transformedProducts)
          setLoading(false)
        }
      } catch (error) {
        console.error("Error fetching products:", error)
        // Fallback to mock data
        let filteredProducts = [...allExpandedProducts]

        // Apply all filters
        if (query) {
          filteredProducts = filteredProducts.filter(
            product =>
              product.name.toLowerCase().includes(query.toLowerCase()) ||
              product.description.toLowerCase().includes(query.toLowerCase())
          )
        }

        // Apply other filters and sorting
        filteredProducts = filteredProducts.filter(
          product => product.price >= priceRange[0] && product.price <= priceRange[1]
        )

        if (selectedCategories.length > 0) {
          filteredProducts = filteredProducts.filter(
            product => selectedCategories.includes(product.category)
          )
        }

        if (selectedConditions.length > 0) {
          filteredProducts = filteredProducts.filter(
            product => selectedConditions.includes(product.condition)
          )
        }

        switch (sortOption) {
          case "price-low":
            filteredProducts.sort((a, b) => a.price - b.price)
            break
          case "price-high":
            filteredProducts.sort((a, b) => b.price - a.price)
            break
          case "newest":
            break
          case "popular":
            filteredProducts.sort(() => Math.random() - 0.5)
            break
        }

        setProducts(filteredProducts)
        setLoading(false)
      }
    }

    fetchProducts()
  }, [query, priceRange, selectedCategories, selectedConditions, sortOption])

  // Calculate pagination
  const totalPages = Math.ceil(products.length / productsPerPage)
  const indexOfLastProduct = currentPage * productsPerPage
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage
  const currentProducts = products.slice(indexOfFirstProduct, indexOfLastProduct)

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
    setCurrentPage(1)
  }

  // Handle condition selection
  const handleConditionChange = (condition: string) => {
    setSelectedConditions(prev =>
      prev.includes(condition)
        ? prev.filter(c => c !== condition)
        : [...prev, condition]
    )
    setCurrentPage(1)
  }

  // Clear all filters
  const clearFilters = () => {
    setPriceRange([0, 100000])
    setSelectedCategories([])
    setSelectedConditions([])
    setSortOption("newest")
    setCurrentPage(1)
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      {/* Search header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            {query ? `Search results for "${query}"` : "All Products"}
          </h1>
          <p className="text-slate-500 mt-1">
            {products.length} {products.length === 1 ? "product" : "products"} found
          </p>
        </div>
        <div className="flex items-center gap-2 w-full md:w-auto">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Refine your search..."
              className="pl-8 pr-4"
              value={searchInput}
              onChange={handleSearchChange}
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            className="md:hidden"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
          <Select value={sortOption} onValueChange={setSortOption}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="popular">Popularity</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters sidebar - desktop */}
        <div className={`w-full md:w-64 md:block ${showFilters ? 'block' : 'hidden'}`}>
          <div className="sticky top-24 space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold text-lg">Filters</h2>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear all
              </Button>
            </div>

            <Separator />

            {/* Price Range */}
            <div className="space-y-4">
              <h3 className="font-medium">Price Range</h3>
              <div className="px-2">
                <Slider
                  defaultValue={[0, 100000]}
                  max={100000}
                  step={1000}
                  value={priceRange}
                  onValueChange={setPriceRange}
                />
              </div>
              <div className="flex items-center justify-between">
                <span>₹{priceRange[0].toLocaleString("en-IN")}</span>
                <span>₹{priceRange[1].toLocaleString("en-IN")}</span>
              </div>
            </div>

            <Separator />

            {/* Categories */}
            <Accordion type="multiple" defaultValue={["categories"]}>
              <AccordionItem value="categories">
                <AccordionTrigger>Categories</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category}`}
                          checked={selectedCategories.includes(category)}
                          onCheckedChange={() => handleCategoryChange(category)}
                        />
                        <label
                          htmlFor={`category-${category}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <Separator />

            {/* Condition */}
            <Accordion type="multiple" defaultValue={["condition"]}>
              <AccordionItem value="condition">
                <AccordionTrigger>Condition</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2">
                    {conditions.map((condition) => (
                      <div key={condition} className="flex items-center space-x-2">
                        <Checkbox
                          id={`condition-${condition}`}
                          checked={selectedConditions.includes(condition)}
                          onCheckedChange={() => handleConditionChange(condition)}
                        />
                        <label
                          htmlFor={`condition-${condition}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {condition}
                        </label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>

        {/* Product grid */}
        <div className="flex-1">
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 animate-pulse">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="rounded-xl bg-slate-100 aspect-square"></div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <>
              {/* Active filters */}
              {(selectedCategories.length > 0 || selectedConditions.length > 0) && (
                <div className="mb-6 flex flex-wrap gap-2">
                  {selectedCategories.map(category => (
                    <Badge key={category} variant="secondary" className="flex items-center gap-1">
                      {category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleCategoryChange(category)}
                      />
                    </Badge>
                  ))}
                  {selectedConditions.map(condition => (
                    <Badge key={condition} variant="secondary" className="flex items-center gap-1">
                      {condition}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleConditionChange(condition)}
                      />
                    </Badge>
                  ))}
                  {(selectedCategories.length > 0 || selectedConditions.length > 0) && (
                    <Button variant="ghost" size="sm" onClick={clearFilters} className="h-6">
                      Clear all
                    </Button>
                  )}
                </div>
              )}

              {/* Products grid */}
              <ProductGrid
                products={products}
                loading={loading}
                columns={3}
                initialLimit={12}
                incrementAmount={12}
                showLoadMore={true}
                emptyMessage={`No products found matching "${query}". Try adjusting your filters.`}
              />
            </>
          ) : (
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold mb-2">No products found</h2>
              <p className="text-slate-500 mb-6">Try adjusting your search or filter criteria</p>
              <Button onClick={clearFilters}>Clear all filters</Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
