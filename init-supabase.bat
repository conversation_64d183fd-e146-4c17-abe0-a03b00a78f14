@echo off
echo Initializing Supabase for PASSDOWN e-commerce platform...

echo Installing required dependencies...
npm install dotenv @supabase/supabase-js

echo Creating Supabase schema...
echo Please run the SQL script in the Supabase SQL Editor:
echo scripts/supabase-schema.sql

echo Migrating data from local database to Supabase...
node scripts/migrate-to-supabase.js

echo Supabase initialization complete!
echo.
echo Admin credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Seller credentials:
echo Email: <EMAIL>
echo Password: seller123
echo.
echo Buyer credentials:
echo Email: <EMAIL>
echo Password: buyer123
echo.
pause
