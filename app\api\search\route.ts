import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q') || '';
    const category = searchParams.get('category');
    const minPrice = searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined;
    const maxPrice = searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined;
    const condition = searchParams.get('condition');
    const sortBy = searchParams.get('sortBy') || 'newest';
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 12;
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Build where clause
    let whereClause: any = {};
    
    // Search query
    if (query) {
      whereClause = {
        ...whereClause,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
        ],
      };
    }
    
    // Category filter
    if (category) {
      const categoryRecord = await prisma.category.findUnique({
        where: { slug: category },
      });
      
      if (categoryRecord) {
        whereClause = {
          ...whereClause,
          categoryId: categoryRecord.id,
        };
      }
    }
    
    // Price range filter
    if (minPrice !== undefined || maxPrice !== undefined) {
      whereClause = {
        ...whereClause,
        price: {
          ...(minPrice !== undefined && { gte: minPrice }),
          ...(maxPrice !== undefined && { lte: maxPrice }),
        },
      };
    }
    
    // Condition filter
    if (condition) {
      whereClause = {
        ...whereClause,
        condition,
      };
    }
    
    // Determine order by
    let orderBy: any = {};
    
    switch (sortBy) {
      case 'price-low':
        orderBy = { price: 'asc' };
        break;
      case 'price-high':
        orderBy = { price: 'desc' };
        break;
      case 'popular':
        orderBy = { views: 'desc' };
        break;
      case 'newest':
      default:
        orderBy = { createdAt: 'desc' };
        break;
    }
    
    // Get total count for pagination
    const totalCount = await prisma.product.count({
      where: whereClause,
    });
    
    // Get products
    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        category: true,
        seller: {
          select: {
            id: true,
            name: true,
            avatar: true,
            sellerProfile: {
              select: {
                rating: true,
                verified: true,
              },
            },
          },
        },
        images: true,
      },
      orderBy,
      skip,
      take: limit,
    });
    
    // Transform products for response
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.mainImage || product.images[0]?.url || '/placeholder.svg?height=400&width=400',
      condition: product.condition,
      category: product.category.slug,
      seller: product.seller.name || 'Unknown Seller',
      location: product.location,
      sellerInfo: {
        id: product.seller.id,
        name: product.seller.name,
        avatar: product.seller.avatar,
        rating: product.seller.sellerProfile?.rating,
        verified: product.seller.sellerProfile?.verified,
      },
    }));
    
    return NextResponse.json({
      products: transformedProducts,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
