import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  ShieldCheck, 
  Recycle, 
  Leaf, 
  Users, 
  BarChart, 
  Globe, 
  Mail, 
  Phone, 
  MapPin 
} from "lucide-react"

export default function AboutPage() {
  return (
    <div className="container px-4 py-12 md:py-24">
      {/* Hero Section */}
      <div className="flex flex-col md:flex-row gap-12 items-center mb-24">
        <div className="w-full md:w-1/2 space-y-6">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
            Giving Products a <span className="text-teal-600">Second Life</span>
          </h1>
          <p className="text-lg text-slate-600">
            PASSDOWN is India's premier marketplace for quality second-hand products. 
            We're on a mission to reduce waste, promote sustainability, and make quality 
            products accessible to everyone.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="bg-teal-600 hover:bg-teal-700" size="lg" asChild>
              <Link href="/products">Browse Products</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
        <div className="w-full md:w-1/2 relative">
          <div className="aspect-square relative rounded-lg overflow-hidden">
            <Image 
              src="https://images.unsplash.com/photo-1556742031-c6961e8560b0?q=80&w=2070&auto=format&fit=crop" 
              alt="PASSDOWN Team" 
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Our Story */}
      <div className="mb-24">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Our Story</h2>
          <p className="text-lg text-slate-600">
            Founded in 2022, PASSDOWN was born from a simple idea: quality products deserve a second life.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className="relative h-[400px] rounded-lg overflow-hidden">
            <Image 
              src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=2084&auto=format&fit=crop" 
              alt="PASSDOWN Team Meeting" 
              fill
              className="object-cover"
            />
          </div>
          <div className="space-y-6">
            <p className="text-slate-600">
              It all started when our founders, Rahul and Priya, noticed how many quality products were being discarded 
              while still having years of useful life left in them. At the same time, many people couldn't afford to buy 
              these items new.
            </p>
            <p className="text-slate-600">
              They envisioned a platform that would connect sellers of pre-owned items with buyers looking for quality 
              at affordable prices. But more than just a marketplace, they wanted to create a community that values 
              sustainability and responsible consumption.
            </p>
            <p className="text-slate-600">
              Today, PASSDOWN has grown into India's leading second-hand marketplace, with thousands of products 
              finding new homes every month. We've helped extend the life of countless products, reducing waste 
              and making quality items accessible to more people.
            </p>
          </div>
        </div>
      </div>

      {/* Our Values */}
      <div className="mb-24">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Our Values</h2>
          <p className="text-lg text-slate-600">
            At PASSDOWN, we're guided by a set of core values that shape everything we do.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <ShieldCheck className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Trust & Transparency</h3>
            <p className="text-slate-600">
              We believe in complete transparency about product condition and history. 
              Our verification process ensures that what you see is what you get.
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Recycle className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Sustainability</h3>
            <p className="text-slate-600">
              By extending the life of products, we reduce waste and minimize environmental impact. 
              Every purchase on PASSDOWN is a step toward a more sustainable future.
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Community</h3>
            <p className="text-slate-600">
              We're building a community of conscious consumers who value quality, affordability, 
              and sustainability. Together, we're changing how India shops.
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Leaf className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Environmental Impact</h3>
            <p className="text-slate-600">
              Every product that finds a new home through PASSDOWN is one less item in a landfill. 
              We're proud of the positive environmental impact we're making.
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <BarChart className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Quality Over Quantity</h3>
            <p className="text-slate-600">
              We focus on quality products that still have value and utility. Our curation process 
              ensures that only items in good condition make it to our platform.
            </p>
          </div>
          <div className="bg-white p-6 rounded-lg border">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Globe className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Accessibility</h3>
            <p className="text-slate-600">
              We believe quality products should be accessible to everyone. By offering pre-owned items, 
              we make premium products available at affordable prices.
            </p>
          </div>
        </div>
      </div>

      {/* Impact */}
      <div className="mb-24 bg-slate-50 p-12 rounded-lg">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Our Impact</h2>
          <p className="text-lg text-slate-600">
            Since our founding, we've made a significant positive impact on both the environment and our community.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-4xl font-bold text-teal-600 mb-2">50,000+</div>
            <p className="text-slate-600">Products given a second life</p>
          </div>
          <div>
            <div className="text-4xl font-bold text-teal-600 mb-2">₹1.2 Cr+</div>
            <p className="text-slate-600">Saved by our customers</p>
          </div>
          <div>
            <div className="text-4xl font-bold text-teal-600 mb-2">500+</div>
            <p className="text-slate-600">Tons of waste diverted from landfills</p>
          </div>
          <div>
            <div className="text-4xl font-bold text-teal-600 mb-2">10,000+</div>
            <p className="text-slate-600">Verified sellers on our platform</p>
          </div>
        </div>
      </div>

      {/* Team */}
      <div className="mb-24">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Our Team</h2>
          <p className="text-lg text-slate-600">
            Meet the passionate individuals behind PASSDOWN.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="aspect-square relative rounded-full overflow-hidden mb-4 max-w-[200px] mx-auto">
              <Image 
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop" 
                alt="Rahul Sharma" 
                fill
                className="object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">Rahul Sharma</h3>
            <p className="text-slate-600">Co-Founder & CEO</p>
          </div>
          <div className="text-center">
            <div className="aspect-square relative rounded-full overflow-hidden mb-4 max-w-[200px] mx-auto">
              <Image 
                src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1974&auto=format&fit=crop" 
                alt="Priya Patel" 
                fill
                className="object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">Priya Patel</h3>
            <p className="text-slate-600">Co-Founder & COO</p>
          </div>
          <div className="text-center">
            <div className="aspect-square relative rounded-full overflow-hidden mb-4 max-w-[200px] mx-auto">
              <Image 
                src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop" 
                alt="Amit Kumar" 
                fill
                className="object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">Amit Kumar</h3>
            <p className="text-slate-600">CTO</p>
          </div>
          <div className="text-center">
            <div className="aspect-square relative rounded-full overflow-hidden mb-4 max-w-[200px] mx-auto">
              <Image 
                src="https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?q=80&w=1974&auto=format&fit=crop" 
                alt="Neha Singh" 
                fill
                className="object-cover"
              />
            </div>
            <h3 className="text-xl font-bold">Neha Singh</h3>
            <p className="text-slate-600">Head of Marketing</p>
          </div>
        </div>
      </div>

      {/* Contact */}
      <div className="bg-white p-12 rounded-lg border">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold mb-4">Get in Touch</h2>
          <p className="text-lg text-slate-600">
            Have questions or want to learn more about PASSDOWN? We'd love to hear from you.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Mail className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Email Us</h3>
            <p className="text-slate-600 mb-4">For general inquiries and support</p>
            <a href="mailto:<EMAIL>" className="text-teal-600 hover:underline"><EMAIL></a>
          </div>
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <Phone className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Call Us</h3>
            <p className="text-slate-600 mb-4">Mon-Fri, 9am-6pm IST</p>
            <a href="tel:+919876543210" className="text-teal-600 hover:underline">+91 98765 43210</a>
          </div>
          <div className="flex flex-col items-center text-center">
            <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mb-4">
              <MapPin className="h-6 w-6 text-teal-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Visit Us</h3>
            <p className="text-slate-600 mb-4">Our headquarters</p>
            <address className="text-teal-600 not-italic">
              123 Tech Park, Koramangala<br />
              Bangalore, Karnataka 560034
            </address>
          </div>
        </div>
        <div className="text-center mt-12">
          <Button className="bg-teal-600 hover:bg-teal-700" size="lg" asChild>
            <Link href="/contact">Contact Us</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
