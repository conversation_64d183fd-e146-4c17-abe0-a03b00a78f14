import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Clock, Tag, Percent, ArrowRight } from "lucide-react"
import ProductCard from "@/app/components/product-card"
import { allProducts } from "@/app/data/enhanced-products"
import type { Product } from "@/app/types"

// Helper function to calculate discount percentage
function calculateDiscount(originalPrice: number, currentPrice: number): number {
  if (!originalPrice || originalPrice <= currentPrice) return 0
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

// Filter products with discounts
const discountedProducts = allProducts.filter(
  (product) => product.originalPrice && product.originalPrice > product.price
)

// Sort products by discount percentage (highest first)
const sortedByDiscount = [...discountedProducts].sort((a, b) => {
  const discountA = calculateDiscount(a.originalPrice || 0, a.price)
  const discountB = calculateDiscount(b.originalPrice || 0, b.price)
  return discountB - discountA
})

// Get top deals (highest discount)
const topDeals = sortedByDiscount.slice(0, 4)

// Get flash deals (random selection from discounted products)
const flashDeals = [...discountedProducts]
  .sort(() => 0.5 - Math.random())
  .slice(0, 4)

// Get clearance deals (lowest priced discounted products)
const clearanceDeals = [...discountedProducts]
  .sort((a, b) => a.price - b.price)
  .slice(0, 4)

export default function DealsPage() {
  return (
    <div className="container px-4 py-8 md:py-12">
      {/* Hero Section */}
      <div className="relative rounded-xl overflow-hidden mb-12">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900 to-slate-800/80 z-10"></div>
        <Image
          src="/placeholder.svg?height=400&width=1200"
          alt="Special Deals"
          width={1200}
          height={400}
          className="w-full h-64 md:h-80 object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col justify-center px-6 md:px-12">
          <div className="max-w-2xl">
            <Badge className="mb-4 bg-teal-500 hover:bg-teal-600">Limited Time</Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
              Special Deals & Discounts
            </h1>
            <p className="text-slate-200 mb-6 max-w-lg">
              Discover amazing discounts on quality tech components. Save big on graphics cards, processors, and more!
            </p>
            <Button className="bg-teal-500 hover:bg-teal-600 text-white">
              Shop All Deals
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Deals Tabs */}
      <Tabs defaultValue="top-deals" className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl md:text-3xl font-bold">Featured Deals</h2>
          <TabsList>
            <TabsTrigger value="top-deals">Top Deals</TabsTrigger>
            <TabsTrigger value="flash-deals">Flash Sales</TabsTrigger>
            <TabsTrigger value="clearance">Clearance</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="top-deals" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {topDeals.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                className="h-full"
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="flash-deals" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {flashDeals.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                className="h-full"
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="clearance" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {clearanceDeals.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                className="h-full"
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Deal Categories */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Shop Deals by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { name: "Graphics Cards", slug: "graphics-cards", image: "/placeholder.svg?height=200&width=200" },
            { name: "Processors", slug: "processors", image: "/placeholder.svg?height=200&width=200" },
            { name: "Storage", slug: "storage", image: "/placeholder.svg?height=200&width=200" },
            { name: "RAM", slug: "ram", image: "/placeholder.svg?height=200&width=200" },
          ].map((category) => (
            <Link
              key={category.slug}
              href={`/category/${category.slug}?discount=true`}
              className="group relative rounded-lg overflow-hidden"
            >
              <div className="absolute inset-0 bg-black/50 group-hover:bg-black/60 transition-colors z-10"></div>
              <Image
                src={category.image}
                alt={category.name}
                width={200}
                height={200}
                className="w-full aspect-square object-cover"
              />
              <div className="absolute inset-0 z-20 flex items-center justify-center">
                <h3 className="text-white font-semibold text-lg md:text-xl">{category.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Deal of the Day */}
      {topDeals[0] && (
        <section className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Deal of the Day</h2>
            <Badge variant="outline" className="text-teal-600 border-teal-600">
              <Clock className="mr-1 h-3 w-3" />
              Ends in 23:59:59
            </Badge>
          </div>
          <ProductCard
            product={topDeals[0]}
            variant="featured"
          />
        </section>
      )}

      {/* All Deals */}
      <section>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">All Deals</h2>
          <Button variant="outline" asChild>
            <Link href="/search?discount=true">View All</Link>
          </Button>
        </div>

        {/* First row - normal cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {discountedProducts.slice(0, 4).map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              className="h-full"
            />
          ))}
        </div>

        {/* Second row - compact cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {discountedProducts.slice(4, 8).map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              variant="compact"
            />
          ))}
        </div>
      </section>
    </div>
  )
}
