"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { User, Session } from "@supabase/supabase-js"

interface SupabaseUser {
  id: string
  name: string | null
  email: string
  role: "buyer" | "seller" | "admin"
  avatar: string | null
  createdAt: string
  sellerProfile?: {
    id: string
    storeName: string | null
    description: string | null
    location: string | null
    rating: number | null
    verified: boolean
  }
}

interface SupabaseAuthContextType {
  user: SupabaseUser | null
  supabaseUser: User | null
  session: Session | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (name: string, email: string, password: string, role: "buyer" | "seller") => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

const SupabaseAuthContext = createContext<SupabaseAuthContextType | undefined>(undefined)

export function SupabaseAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [supabaseUser, setSupabaseUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true)
      
      try {
        // Get the current session
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          setUser(null)
          setSession(null)
          setSupabaseUser(null)
        } else if (session) {
          setSession(session)
          setSupabaseUser(session.user)
          
          // Convert Supabase user to our app's user format
          await fetchUserProfile(session.user)
        }
      } catch (error) {
        console.error('Unexpected error during auth initialization:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()

    // Set up the auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event)
        setSession(session)
        setSupabaseUser(session?.user ?? null)
        
        if (session?.user) {
          await fetchUserProfile(session.user)
        } else {
          setUser(null)
        }
        
        // Force a router refresh to update server components
        if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
          router.refresh()
        }
      }
    )

    // Clean up the subscription when the component unmounts
    return () => {
      subscription.unsubscribe()
    }
  }, [router])

  // Fetch user profile from Supabase
  const fetchUserProfile = async (supabaseUser: User) => {
    try {
      // Get user profile from the users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()

      if (userError) {
        console.error('Error fetching user profile:', userError)
        return null
      }

      // If the user is a seller, get their seller profile
      let sellerProfile = null
      if (userData.role === 'seller') {
        const { data: sellerData, error: sellerError } = await supabase
          .from('seller_profiles')
          .select('*')
          .eq('user_id', supabaseUser.id)
          .single()

        if (!sellerError && sellerData) {
          sellerProfile = {
            id: sellerData.id,
            storeName: sellerData.store_name,
            description: sellerData.description,
            location: sellerData.location,
            rating: sellerData.rating,
            verified: sellerData.verified
          }
        }
      }

      // Create user object in our app's format
      const appUser: SupabaseUser = {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        avatar: userData.avatar_url,
        createdAt: userData.created_at,
        sellerProfile: sellerProfile
      }

      setUser(appUser)
      return appUser
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      return null
    }
  }

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        toast({
          title: "Login failed",
          description: error.message,
          variant: "destructive"
        })
        throw error
      }

      toast({
        title: "Login successful",
        description: "Welcome back!"
      })
    } catch (error) {
      console.error("Login error:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (name: string, email: string, password: string, role: "buyer" | "seller") => {
    setIsLoading(true)
    try {
      // Sign up with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role
          }
        }
      })

      if (error) {
        toast({
          title: "Registration failed",
          description: error.message,
          variant: "destructive"
        })
        throw error
      }

      // Create user record in our users table
      if (data.user) {
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              email: email,
              name: name,
              role: role,
              created_at: new Date().toISOString()
            }
          ])

        if (profileError) {
          console.error("Error creating user profile:", profileError)
        }

        // If registering as a seller, create seller profile
        if (role === 'seller') {
          const { error: sellerError } = await supabase
            .from('seller_profiles')
            .insert([
              {
                user_id: data.user.id,
                store_name: `${name}'s Store`,
                verified: false
              }
            ])

          if (sellerError) {
            console.error("Error creating seller profile:", sellerError)
          }
        }
      }

      toast({
        title: "Registration successful",
        description: "Your account has been created."
      })
    } catch (error) {
      console.error("Registration error:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        toast({
          title: "Logout failed",
          description: error.message,
          variant: "destructive"
        })
        throw error
      }

      setUser(null)
      router.push("/")
      
      toast({
        title: "Logged out",
        description: "You have been logged out successfully."
      })
    } catch (error) {
      console.error("Logout error:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        return await fetchUserProfile(session.user)
      }
      
      return null
    } catch (error) {
      console.error("Failed to refresh user:", error)
      return null
    }
  }

  return (
    <SupabaseAuthContext.Provider
      value={{
        user,
        supabaseUser,
        session,
        isLoading,
        login,
        register,
        logout,
        refreshUser
      }}
    >
      {children}
    </SupabaseAuthContext.Provider>
  )
}

export function useSupabaseAuth() {
  const context = useContext(SupabaseAuthContext)
  if (context === undefined) {
    throw new Error("useSupabaseAuth must be used within a SupabaseAuthProvider")
  }
  return context
}
