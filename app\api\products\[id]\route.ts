import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            sellerProfile: true,
          },
        },
        images: true,
      },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Transform the data to match the expected format in the frontend
    const transformedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.mainImage || product.images[0]?.url || '/placeholder.svg?height=400&width=400',
      condition: product.condition,
      category: product.category.slug,
      seller: product.seller.name || 'Unknown Seller',
      location: product.location,
      images: product.images.map(img => img.url),
      isSecondHand: product.isSecondHand,
      usageHistory: product.usageHistory,
      defectsDisclosure: product.defectsDisclosure,
      quantity: product.quantity,
      status: product.status,
      views: product.views,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      sellerInfo: {
        id: product.seller.id,
        name: product.seller.name,
        email: product.seller.email,
        avatar: product.seller.avatar,
        sellerProfile: product.seller.sellerProfile,
      },
    };

    // Increment view count
    await prisma.product.update({
      where: { id },
      data: { views: { increment: 1 } },
    });

    return NextResponse.json(transformedProduct);
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
