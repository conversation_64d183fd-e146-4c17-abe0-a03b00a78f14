"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart3,
  LineChart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Download,
} from "lucide-react"
import { supabase } from "@/lib/supabase"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts"

export default function AnalyticsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30days")
  const [analyticsData, setAnalyticsData] = useState({
    salesSummary: {
      total: 0,
      growth: 0,
      averageOrderValue: 0,
      conversionRate: 0,
    },
    topProducts: [],
    topCategories: [],
    userMetrics: {
      totalUsers: 0,
      newUsers: 0,
      activeUsers: 0,
      userGrowth: 0,
    },
    salesByDay: [],
    salesByCategory: [],
  })

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true)

        // Get date ranges based on selected time range
        let startDate = new Date()
        const endDate = new Date()

        if (timeRange === "7days") {
          startDate.setDate(startDate.getDate() - 7)
        } else if (timeRange === "30days") {
          startDate.setDate(startDate.getDate() - 30)
        } else if (timeRange === "90days") {
          startDate.setDate(startDate.getDate() - 90)
        } else if (timeRange === "year") {
          startDate.setFullYear(startDate.getFullYear() - 1)
        } else {
          // All time - use a far past date
          startDate = new Date(2020, 0, 1)
        }

        // Format dates for API calls
        const formattedStartDate = startDate.toISOString().split('T')[0]
        const formattedEndDate = endDate.toISOString().split('T')[0]

        // Fetch data from Supabase
        const { data: salesData, error: salesError } = await supabase
          .from('orders')
          .select('id, total, created_at')
          .gte('created_at', formattedStartDate)
          .lte('created_at', formattedEndDate)

        if (salesError) {
          console.error('Error fetching sales data:', salesError)
          throw salesError
        }

        // Calculate sales metrics
        const totalSales = salesData?.reduce((sum, order) => sum + order.total, 0) || 0
        const averageOrderValue = salesData?.length ? totalSales / salesData.length : 0

        // Get previous period data for growth calculation
        const prevStartDate = new Date(startDate)
        prevStartDate.setDate(prevStartDate.getDate() - (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
        const formattedPrevStartDate = prevStartDate.toISOString().split('T')[0]

        const { data: prevSalesData } = await supabase
          .from('orders')
          .select('total')
          .gte('created_at', formattedPrevStartDate)
          .lt('created_at', formattedStartDate)

        const prevTotalSales = prevSalesData?.reduce((sum, order) => sum + order.total, 0) || 0
        const salesGrowth = prevTotalSales ? ((totalSales - prevTotalSales) / prevTotalSales) * 100 : 0

        // Fetch user metrics
        const { data: usersData, error: usersError } = await supabase
          .from('users')
          .select('id, created_at, role')

        if (usersError) {
          console.error('Error fetching users data:', usersError)
          throw usersError
        }

        const totalUsers = usersData?.length || 0
        const newUsers = usersData?.filter(user =>
          new Date(user.created_at) >= startDate && new Date(user.created_at) <= endDate
        ).length || 0

        // Fetch top products
        const { data: productsData, error: productsError } = await supabase
          .from('order_items')
          .select(`
            product_id,
            quantity,
            price,
            products:product_id (name)
          `)
          .order('quantity', { ascending: false })
          .limit(5)

        if (productsError) {
          console.error('Error fetching products data:', productsError)
          throw productsError
        }

        const topProducts = productsData?.map(item => ({
          id: item.product_id,
          name: item.products?.name || 'Unknown Product',
          sales: item.price * item.quantity,
          units: item.quantity
        })) || []

        // Fetch category data
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('id, name')

        if (categoriesError) {
          console.error('Error fetching categories data:', categoriesError)
          throw categoriesError
        }

        // For now, use mock data for categories since we need to join multiple tables
        const topCategories = [
          { name: "Graphics Cards", sales: 124500, percentage: 35 },
          { name: "Processors", sales: 89600, percentage: 25 },
          { name: "Memory", sales: 53800, percentage: 15 },
          { name: "Storage", sales: 35900, percentage: 10 },
          { name: "Motherboards", sales: 28500, percentage: 8 },
          { name: "Other", sales: 24700, percentage: 7 },
        ]

        // Update analytics data
        setAnalyticsData({
          salesSummary: {
            total: totalSales,
            growth: parseFloat(salesGrowth.toFixed(1)),
            averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
            conversionRate: 3.2, // Mock data for now
          },
          topProducts: topProducts.length > 0 ? topProducts : [
            { id: "1", name: "NVIDIA GeForce RTX 3070", sales: 42999 * 5, units: 5 },
            { id: "2", name: "AMD Ryzen 9 5900X", sales: 36999 * 4, units: 4 },
            { id: "3", name: "Samsung 970 EVO Plus 1TB", sales: 9999 * 8, units: 8 },
            { id: "4", name: "Corsair Vengeance RGB Pro 32GB", sales: 12999 * 6, units: 6 },
            { id: "5", name: "ASUS ROG Strix B550-F Gaming", sales: 18999 * 3, units: 3 },
          ],
          topCategories,
          userMetrics: {
            totalUsers,
            newUsers,
            activeUsers: Math.round(totalUsers * 0.5), // Estimate active users as 50% of total
            userGrowth: 8.5, // Mock data for now
          },
          salesByDay: [
            { date: "2023-05-01", sales: 12500 },
            { date: "2023-05-02", sales: 9800 },
            { date: "2023-05-03", sales: 11200 },
            { date: "2023-05-04", sales: 10500 },
            { date: "2023-05-05", sales: 13800 },
            { date: "2023-05-06", sales: 15200 },
            { date: "2023-05-07", sales: 14500 },
            { date: "2023-05-08", sales: 11900 },
            { date: "2023-05-09", sales: 10800 },
            { date: "2023-05-10", sales: 12300 },
            { date: "2023-05-11", sales: 13500 },
            { date: "2023-05-12", sales: 14200 },
            { date: "2023-05-13", sales: 15800 },
            { date: "2023-05-14", sales: 16500 },
          ],
          salesByCategory: [
            { category: "Graphics Cards", percentage: 35 },
            { category: "Processors", percentage: 25 },
            { category: "Memory", percentage: 15 },
            { category: "Storage", percentage: 10 },
            { category: "Motherboards", percentage: 8 },
            { category: "Other", percentage: 7 },
          ],
        })
      } catch (error) {
        console.error('Error fetching analytics data:', error)
        // Fallback to mock data
        setAnalyticsData({
          salesSummary: {
            total: 245000,
            growth: 15.8,
            averageOrderValue: 3141,
            conversionRate: 3.2,
          },
          topProducts: [
            { id: "1", name: "NVIDIA GeForce RTX 3070", sales: 42999 * 5, units: 5 },
            { id: "2", name: "AMD Ryzen 9 5900X", sales: 36999 * 4, units: 4 },
            { id: "3", name: "Samsung 970 EVO Plus 1TB", sales: 9999 * 8, units: 8 },
            { id: "4", name: "Corsair Vengeance RGB Pro 32GB", sales: 12999 * 6, units: 6 },
            { id: "5", name: "ASUS ROG Strix B550-F Gaming", sales: 18999 * 3, units: 3 },
          ],
          topCategories: [
            { name: "Graphics Cards", sales: 124500, percentage: 35 },
            { name: "Processors", sales: 89600, percentage: 25 },
            { name: "Memory", sales: 53800, percentage: 15 },
            { name: "Storage", sales: 35900, percentage: 10 },
            { name: "Motherboards", sales: 28500, percentage: 8 },
            { name: "Other", sales: 24700, percentage: 7 },
          ],
          userMetrics: {
            totalUsers: 89,
            newUsers: 12,
            activeUsers: 45,
            userGrowth: 8.5,
          },
          salesByDay: [
            { date: "2023-05-01", sales: 12500 },
            { date: "2023-05-02", sales: 9800 },
            { date: "2023-05-03", sales: 11200 },
            { date: "2023-05-04", sales: 10500 },
            { date: "2023-05-05", sales: 13800 },
            { date: "2023-05-06", sales: 15200 },
            { date: "2023-05-07", sales: 14500 },
            { date: "2023-05-08", sales: 11900 },
            { date: "2023-05-09", sales: 10800 },
            { date: "2023-05-10", sales: 12300 },
            { date: "2023-05-11", sales: 13500 },
            { date: "2023-05-12", sales: 14200 },
            { date: "2023-05-13", sales: 15800 },
            { date: "2023-05-14", sales: 16500 },
          ],
          salesByCategory: [
            { category: "Graphics Cards", percentage: 35 },
            { category: "Processors", percentage: 25 },
            { category: "Memory", percentage: 15 },
            { category: "Storage", percentage: 10 },
            { category: "Motherboards", percentage: 8 },
            { category: "Other", percentage: 7 },
          ],
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalyticsData()
  }, [timeRange])

  // Handle time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value)
    setIsLoading(true)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading analytics data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Analytics & Reports</h1>
          <p className="text-slate-500 mt-1">Track your store's performance and growth</p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Sales Summary */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">₹{analyticsData.salesSummary.total.toLocaleString("en-IN")}</h3>
                  </div>
                  <div className="bg-teal-100 p-3 rounded-full">
                    <DollarSign className="h-5 w-5 text-teal-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div
                    className={`flex items-center ${
                      analyticsData.salesSummary.growth >= 0 ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {analyticsData.salesSummary.growth >= 0 ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    )}
                    <span>{Math.abs(analyticsData.salesSummary.growth)}%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from previous period</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Avg. Order Value</p>
                    <h3 className="text-2xl font-bold mt-1">₹{analyticsData.salesSummary.averageOrderValue.toLocaleString("en-IN")}</h3>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <ShoppingCart className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>4.2%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from previous period</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Conversion Rate</p>
                    <h3 className="text-2xl font-bold mt-1">{analyticsData.salesSummary.conversionRate}%</h3>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-full">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>0.5%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from previous period</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Active Users</p>
                    <h3 className="text-2xl font-bold mt-1">{analyticsData.userMetrics.activeUsers}</h3>
                  </div>
                  <div className="bg-amber-100 p-3 rounded-full">
                    <Users className="h-5 w-5 text-amber-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>{analyticsData.userMetrics.userGrowth}%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from previous period</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Sales Trend</CardTitle>
              <CardDescription>Daily sales performance over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 flex items-center justify-center">
                <div className="text-center">
                  <LineChart className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sales Trend</h3>
                  <p className="text-slate-500 max-w-md mx-auto">
                    This chart will display your daily sales trend over time. Your sales are showing a positive trend with
                    an average daily revenue of ₹{(analyticsData.salesSummary.total / 30).toLocaleString("en-IN")}.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Products and Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
                <CardDescription>Best-selling products by revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.topProducts.slice(0, 5).map((product: any, index: number) => (
                    <div key={product.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-slate-100 text-slate-700 w-6 h-6 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                          {index + 1}
                        </div>
                        <div className="truncate max-w-[200px]">
                          <p className="font-medium truncate">{product.name}</p>
                          <p className="text-xs text-slate-500">{product.units} units sold</p>
                        </div>
                      </div>
                      <p className="font-medium">₹{product.sales.toLocaleString("en-IN")}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales by Category</CardTitle>
                <CardDescription>Revenue distribution across categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center mb-6">
                  <PieChart className="h-16 w-16 text-slate-300" />
                </div>
                <div className="space-y-4">
                  {analyticsData.topCategories.slice(0, 5).map((category: any) => (
                    <div key={category.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{category.name}</p>
                        <p className="text-sm font-medium">₹{category.sales.toLocaleString("en-IN")}</p>
                      </div>
                      <div className="w-full bg-slate-100 rounded-full h-2">
                        <div
                          className="bg-teal-600 h-2 rounded-full"
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-slate-500 text-right">{category.percentage}% of total sales</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Other tabs will be implemented in separate files */}
        <TabsContent value="sales">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Sales Analytics</CardTitle>
              <CardDescription>Comprehensive sales performance metrics</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Detailed Sales Analytics</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  This section will provide detailed sales analytics including revenue by time period, sales channels,
                  payment methods, and more.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Product Performance</CardTitle>
              <CardDescription>Detailed product performance metrics</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <Package className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Product Performance</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  This section will provide detailed product performance metrics including views, conversion rates,
                  inventory turnover, and more.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>User Analytics</CardTitle>
              <CardDescription>Detailed user behavior and demographics</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <Users className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">User Analytics</h3>
                <p className="text-slate-500 max-w-md mx-auto">
                  This section will provide detailed user analytics including acquisition channels, demographics,
                  engagement metrics, and more.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
