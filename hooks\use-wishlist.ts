"use client"

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'

interface WishlistItem {
  id: string
  productId: string
  userId: string
  createdAt: string
  product: {
    id: string
    name: string
    price: number
    originalPrice?: number
    mainImage?: string
    condition: string
    location: string
    status: string
  }
}

interface UseWishlistReturn {
  items: WishlistItem[]
  isLoading: boolean
  error: string | null
  addToWishlist: (productId: string) => Promise<boolean>
  removeFromWishlist: (productId: string) => Promise<boolean>
  isInWishlist: (productId: string) => boolean
  clearWishlist: () => Promise<boolean>
  refreshWishlist: () => Promise<void>
  totalItems: number
}

export function useWishlist(): UseWishlistReturn {
  const [items, setItems] = useState<WishlistItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch wishlist items
  const fetchWishlist = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/wishlist', {
        method: 'GET',
        credentials: 'include',
      })

      if (!response.ok) {
        if (response.status === 401) {
          // User not authenticated, clear wishlist
          setItems([])
          return
        }
        throw new Error('Failed to fetch wishlist')
      }

      const data = await response.json()
      setItems(data.items || [])
    } catch (err) {
      console.error('Error fetching wishlist:', err)
      setError(err instanceof Error ? err.message : 'Failed to load wishlist')
      setItems([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Add item to wishlist
  const addToWishlist = useCallback(async (productId: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/wishlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ productId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to add to wishlist')
      }

      const data = await response.json()
      
      // Add the new item to the local state
      setItems(prev => [...prev, data.item])
      
      toast.success('Added to wishlist')
      return true
    } catch (err) {
      console.error('Error adding to wishlist:', err)
      toast.error(err instanceof Error ? err.message : 'Failed to add to wishlist')
      return false
    }
  }, [])

  // Remove item from wishlist
  const removeFromWishlist = useCallback(async (productId: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/wishlist', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ productId }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to remove from wishlist')
      }

      // Remove the item from local state
      setItems(prev => prev.filter(item => item.productId !== productId))
      
      toast.success('Removed from wishlist')
      return true
    } catch (err) {
      console.error('Error removing from wishlist:', err)
      toast.error(err instanceof Error ? err.message : 'Failed to remove from wishlist')
      return false
    }
  }, [])

  // Check if product is in wishlist
  const isInWishlist = useCallback((productId: string): boolean => {
    return items.some(item => item.productId === productId)
  }, [items])

  // Clear entire wishlist
  const clearWishlist = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/wishlist/clear', {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to clear wishlist')
      }

      setItems([])
      toast.success('Wishlist cleared')
      return true
    } catch (err) {
      console.error('Error clearing wishlist:', err)
      toast.error(err instanceof Error ? err.message : 'Failed to clear wishlist')
      return false
    }
  }, [])

  // Refresh wishlist
  const refreshWishlist = useCallback(async () => {
    await fetchWishlist()
  }, [fetchWishlist])

  // Load wishlist on mount
  useEffect(() => {
    fetchWishlist()
  }, [fetchWishlist])

  return {
    items,
    isLoading,
    error,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist,
    refreshWishlist,
    totalItems: items.length,
  }
}

// Wishlist context for global state management
import { createContext, useContext, ReactNode } from 'react'

const WishlistContext = createContext<UseWishlistReturn | undefined>(undefined)

export function WishlistProvider({ children }: { children: ReactNode }) {
  const wishlist = useWishlist()

  return (
    <WishlistContext.Provider value={wishlist}>
      {children}
    </WishlistContext.Provider>
  )
}

export function useWishlistContext() {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error('useWishlistContext must be used within a WishlistProvider')
  }
  return context
}

// Wishlist button component
import { Button } from '@/components/ui/button'
import { Heart } from 'lucide-react'
import { cn } from '@/lib/utils'

interface WishlistButtonProps {
  productId: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showText?: boolean
}

export function WishlistButton({
  productId,
  variant = 'outline',
  size = 'md',
  className,
  showText = false,
}: WishlistButtonProps) {
  const { addToWishlist, removeFromWishlist, isInWishlist, isLoading } = useWishlistContext()
  
  const inWishlist = isInWishlist(productId)

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (inWishlist) {
      await removeFromWishlist(productId)
    } else {
      await addToWishlist(productId)
    }
  }

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
  }

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  }

  return (
    <Button
      variant={variant}
      size={showText ? 'default' : 'icon'}
      className={cn(
        !showText && sizeClasses[size],
        'transition-colors',
        inWishlist && 'text-red-500 hover:text-red-600',
        className
      )}
      onClick={handleClick}
      disabled={isLoading}
      title={inWishlist ? 'Remove from wishlist' : 'Add to wishlist'}
    >
      <Heart
        className={cn(
          iconSizes[size],
          inWishlist && 'fill-current'
        )}
      />
      {showText && (
        <span className="ml-2">
          {inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
        </span>
      )}
    </Button>
  )
}
