Okay, let's refine this into a more granular, step-by-step plan with highly specific prompts designed to maximize your 50 monthly interactions with Augment Code AI Coder. We'll structure this around your Jira Epics and Tickets, focusing on building out the Minimum Viable Product (MVP) features first, as outlined in your PRD (Section 16.1).

**Guiding Principles for Your Prompts:**

* **One Action, One Prompt:** Each prompt should ask for a single, specific piece of code, configuration, or design.
* **Specify Technology and Files:** Always mention the language, framework, library, and if applicable, the intended file name or service.
* **Reference Your Documents:** Use ticket numbers (e.g., AUTH-1) and PRD sections (e.g., PRD 4.1) to give the AI precise context.
* **Define Inputs & Outputs:** Clearly state what the code should expect (parameters, request body) and what it should produce (return values, API response, UI elements).
* **Prioritize Core Functionality:** Get the basics working before adding complex features.
* **Localization:** Remind the AI about Rupees (₹) and Indian context for relevant features.

---

### Detailed Plan & Refined Prompts

#### **Phase 1: MVP Launch (Focus for initial ~30-35 prompts)**

This phase covers: User authentication, basic product listing/management, core search/filter, simplified checkout/payment, essential seller tools (PRD 16.1).

**Epic 1: User Authentication System (Corresponds to PRD Section 4)**

* **Ticket AUTH-1: Set up basic authentication infrastructure (Node.js/Express.js Backend)**
    1.  **Prompt 1 (Database Schema - User Model):**
        "Generate the Mongoose schema for a `User` model in a Node.js application. The schema must include fields for `email` (String, unique, required, with regex validation for email format), `password` (String, required), `role` (String, enum: \['buyer', 'seller', 'admin'], default: 'buyer'), `firstName` (String, optional), `lastName` (String, optional), `createdAt` (Date, default: Date.now). This is for the User Service microservice using MongoDB, as per Ticket AUTH-1 and PRD 4.1."
    2.  **Prompt 2 (Registration Route & Logic):**
        "Create the Node.js/Express.js API route handler for user registration (`POST /api/auth/register`). It should:
        1.  Validate request body for `email` and `password`.
        2.  Check if a user with the given `email` already exists using the Mongoose `User` model.
        3.  Hash the password using `bcrypt` (ensure a salt round of 10).
        4.  Create and save the new user to MongoDB.
        5.  Return a 201 status with user details (excluding password) or appropriate error messages. Reference Ticket AUTH-1."
    3.  **Prompt 3 (Login Route & JWT Generation):**
        "Create the Node.js/Express.js API route handler for user login (`POST /api/auth/login`). It should:
        1.  Validate request body for `email` and `password`.
        2.  Find the user by `email` using the Mongoose `User` model.
        3.  If user exists, compare the provided password with the stored hashed password using `bcrypt.compare()`.
        4.  If passwords match, generate a JWT (using `jsonwebtoken` library) containing `userId`, `email`, and `role` as payload. The JWT should have an expiration of 1 hour.
        5.  Return the JWT and user details (excluding password) or appropriate error messages. Reference Ticket AUTH-1."
    4.  **Prompt 4 (JWT Verification Middleware):**
        "Create an Express.js middleware function named `verifyToken` for JWT verification. It should:
        1.  Extract the JWT from the `Authorization` header (Bearer token).
        2.  Verify the token using `jsonwebtoken` and the same secret key used for generation.
        3.  If valid, attach the decoded user payload (containing `userId`, `email`, `role`) to the `req.user` object.
        4.  If invalid or not provided, return a 401 or 403 error. This is for protecting routes."

* **Ticket AUTH-2: Implement role-based access control (Node.js/Express.js Backend)**
    5.  **Prompt 5 (RBAC Middleware):**
        "Create an Express.js middleware function named `authorizeRoles` that accepts an array of allowed roles (e.g., \['admin'] or \['seller', 'admin']). This middleware should:
        1.  Be used *after* the `verifyToken` middleware.
        2.  Check if `req.user.role` is included in the array of allowed roles.
        3.  If authorized, call `next()`.
        4.  If not authorized, return a 403 Forbidden error. This supports Ticket AUTH-2."

* **Ticket AUTH-3: Create login and registration UI components (React/Next.js Frontend)**
    6.  **Prompt 6 (Registration Form Component - React/Next.js):**
        "Generate the React functional component code for a user registration form using Next.js (`pages/register.js`). It should:
        1.  Include input fields for `email` and `password`.
        2.  Implement client-side validation for email format and password (min 8 characters).
        3.  On submit, make a `POST` request to `/api/auth/register` with the form data.
        4.  Handle API responses: on success, redirect to login; on error, display error messages.
        5.  Use basic HTML elements for structure and styling (no specific UI library needed yet). Reference Ticket AUTH-3."
    7.  **Prompt 7 (Login Form Component - React/Next.js):**
        "Generate the React functional component code for a user login form using Next.js (`pages/login.js`). It should:
        1.  Include input fields for `email` and `password`.
        2.  On submit, make a `POST` request to `/api/auth/login` with the form data.
        3.  Handle API responses: on success, store the JWT in localStorage/sessionStorage and redirect to a dashboard/homepage; on error, display error messages.
        4.  Use basic HTML elements for structure. Reference Ticket AUTH-3."

**Epic 2: Product Catalog Management (Corresponds to PRD Section 5)**

* **Ticket PROD-1: Design product database schema (MongoDB)**
    8.  **Prompt 8 (Product Schema - Mongoose):**
        "Generate the Mongoose schema for a `Product` model for the Product Catalog Service using MongoDB, as per Ticket PROD-1 and PRD 5.1. Fields must include:
        `name` (String, required, max 100 chars), `description` (String, required, max 2000 chars), `price` (Number, required, representing Rupees ₹), `quantity` (Number, required, default: 1), `category` (mongoose.Schema.Types.ObjectId, ref: 'Category', required), `seller` (mongoose.Schema.Types.ObjectId, ref: 'User', required), `images` ([String] - array of image URLs, min 3, max 10 as per PRD 5.1), `condition` (String, enum: \['new', 'used-like-new', 'used-good', 'used-fair'], required), `specificAttributes` (Mixed - for category-specific fields), `isSecondHand` (Boolean, derived from condition), `usageHistory` (String, optional, for second-hand), `defectsDisclosure` (String, optional, for second-hand), `sku` (String, optional, unique), `createdAt` (Date, default: Date.now).
        Note: Prices are in Rupees (₹)."

* **Ticket PROD-5: Implement product categorization system (Backend first)**
    9.  **Prompt 9 (Category Schema - Mongoose):**
        "Generate the Mongoose schema for a `Category` model. It should include `name` (String, required, unique), `slug` (String, required, unique), `description` (String, optional), `parentCategory` (mongoose.Schema.Types.ObjectId, ref: 'Category', optional, for subcategories), `attributes` ([{ name: String, type: String, required: Boolean }]) for category-specific attribute definitions. This supports Ticket PROD-5."

* **Ticket PROD-2: Implement product creation API (Node.js/Express.js Backend for Sellers)**
    10. **Prompt 10 (Product Creation API Endpoint - Seller):**
        "Create the Node.js/Express.js API route handler (`POST /api/products`) for sellers to create a new product. This endpoint should:
        1.  Be protected by `verifyToken` and `authorizeRoles(['seller'])` middlewares.
        2.  Validate the request body against the `Product` schema requirements (name, description, price in Rupees, quantity, categoryId, condition).
        3.  Assume image URLs are provided in the `images` array directly in the request body for now (actual upload handling will be a separate prompt/step).
        4.  Set `seller` field to `req.user.userId`.
        5.  Save the new product to MongoDB using the `Product` model.
        6.  Return the created product object or error messages. Reference Ticket PROD-2."
        *(Note: Image upload using Multer to cloud storage is complex. For an MVP, you might initially accept image URLs or handle uploads very simply. A full Multer/S3 setup would take multiple prompts.)*

* **Frontend Product Display (Basic - not a specific ticket but implied for MVP)**
    11. **Prompt 11 (Product Listing API - Public):**
        "Create a public Node.js/Express.js API route (`GET /api/products`) to fetch a list of all products. It should:
        1.  Support pagination (e.g., `?page=1&limit=10`).
        2.  Populate `category` and `seller` (only public seller info like name) details.
        3.  Return an array of product objects. Prices should be in Rupees ₹."
    12. **Prompt 12 (Product Detail API - Public):**
        "Create a public Node.js/Express.js API route (`GET /api/products/:productId`) to fetch details for a single product by its ID.
        1.  Populate `category` and `seller` (public seller info) details.
        2.  Return the product object or a 404 if not found. Price in Rupees ₹."
    13. **Prompt 13 (Basic Product Card Component - React/Next.js):**
        "Generate a React functional component `ProductCard` that takes a `product` object (with fields like name, price in Rupees ₹, first image URL, condition) as a prop. It should display:
        1.  Product image.
        2.  Product name.
        3.  Product price (formatted as ₹X,XXX.XX).
        4.  Product condition.
        Make it link to a product detail page (e.g., `/products/[productId]`)."
    14. **Prompt 14 (Product List Page - React/Next.js):**
        "Generate a Next.js page component (`pages/products/index.js`) that:
        1.  Fetches products from the `/api/products` endpoint (e.g., using `useEffect` and `Workspace`).
        2.  Displays a list of `ProductCard` components.
        3.  Includes basic pagination controls."
    15. **Prompt 15 (Product Detail Page - React/Next.js):**
        "Generate a Next.js dynamic page component (`pages/products/[productId].js`) that:
        1.  Fetches product details from `/api/products/:productId` using the ID from the route.
        2.  Displays all product details: name, multiple images (carousel/gallery if simple), description, price (Rupees ₹), condition, seller info, etc.
        3.  Includes an 'Add to Cart' button."

**Epic 3: Search and Filtering System (Core search for MVP - PRD Section 6)**

* **Ticket SRCH-1 & SRCH-2: Basic Search Setup (Elasticsearch & API)**
    *(This is a larger setup. For MVP and limited prompts, you might start with basic database querying if Elasticsearch is too complex initially).*
    *Alternative for MVP if Elasticsearch is too many prompts:*
    16. **Prompt 16 (Basic Product Search API - Database Query):**
        "Modify the `GET /api/products` Node.js/Express.js endpoint to accept a `searchTerm` query parameter.
        1.  If `searchTerm` is provided, filter products where the name or description (case-insensitive) contains the `searchTerm` using MongoDB's `$regex`.
        2.  Continue to support pagination.
        3.  Return the filtered and paginated list of products. Prices in Rupees ₹."
    17. **Prompt 17 (Search Bar UI - React/Next.js):**
        "Create a React functional component `SearchBar` that:
        1.  Has an input field and a search button.
        2.  When the search button is clicked or Enter is pressed, it navigates to the product list page (`/products`) appending the search term as a query parameter (e.g., `/products?searchTerm=query`).
        The product list page (from prompt 14) should be modified to read this `searchTerm` and pass it to its API call."

**Epic 4: Shopping Cart and Checkout (Simplified for MVP - PRD Section 8)**

* **Ticket CART-1 & CART-2: Cart Data Model & Basic UI (Frontend Focus for MVP if backend cart is complex)**
    *For an MVP with limited prompts, a client-side cart can be a starting point.*
    18. **Prompt 18 (Cart Context - React):**
        "Create a React Context (`CartContext.js`) for managing shopping cart state (items, total quantity, total price in Rupees ₹). It should provide functions to:
        1.  `addToCart(product, quantity)`: Adds a product and quantity. If product exists, update quantity.
        2.  `removeFromCart(productId)`: Removes a product.
        3.  `updateQuantity(productId, newQuantity)`: Updates quantity.
        4.  `clearCart()`.
        Cart items should store product ID, name, price (Rupees ₹), quantity, and main image."
    19. **Prompt 19 (Add to Cart Button Logic - React):**
        "Update the 'Add to Cart' button in the `ProductDetail` page component (from prompt 15) and `ProductCard` component (from prompt 13). On click, it should call the `addToCart` function from `CartContext` with the product details and a default quantity of 1. Provide visual feedback (e.g., 'Added to cart!')."
    20. **Prompt 20 (Cart Page UI - React/Next.js):**
        "Generate a Next.js page component (`pages/cart.js`) that:
        1.  Consumes `CartContext` to display cart items (image, name, price in Rupees ₹, quantity, item total).
        2.  Allows users to update quantity or remove items from the cart (calling context functions).
        3.  Displays the cart subtotal in Rupees ₹.
        4.  Includes a 'Proceed to Checkout' button (links to a checkout page)."

* **Ticket CART-3: Checkout flow foundation (Address & Summary)**
    21. **Prompt 21 (Basic Checkout Page UI - React/Next.js):**
        "Generate a Next.js page component (`pages/checkout.js`) for the first step of checkout:
        1.  Requires user to be logged in (redirect to login if not).
        2.  Displays a form for shipping address (Full Name, Address Line 1, Address Line 2, City, State, Pincode - all relevant for India, Pincode validation for 6 digits).
        3.  Shows an order summary (items from cart, subtotal, placeholder for shipping fees, placeholder for 3% platform commission, and total amount in Rupees ₹).
        4.  A 'Continue to Payment' button (for now, this can just log the address and cart details to console)."
        *(Actual payment integration (CART-4) is complex and would take many prompts).*

**Epic 5: Seller Management and Onboarding (Essential Seller Tools - PRD Section 9)**

* **Ticket PROD-3: Create product listing UI for sellers (React/Next.js Frontend)**
    22. **Prompt 22 (Seller Product Listing Form - React/Next.js):**
        "Generate a Next.js page component (`pages/seller/products/new.js`) for sellers to create new product listings. This page should:
        1.  Be accessible only to 'seller' role users (implement client-side or Next.js middleware route protection).
        2.  Contain a form with fields for: `name`, `description` (use a `<textarea>`), `price` (input type number, in Rupees ₹), `quantity`, `category` (dropdown - for now, assume categories are hardcoded or fetched simply), `condition` (dropdown), `images` (input type text for up to 3 image URLs, as per PRD 5.1 allowing min 3).
        3.  On submit, make a `POST` request to `/api/products` (from prompt 10) with the product data and the seller's JWT.
        4.  Handle success (e.g., redirect to seller product list) and errors."

* **Ticket PROD-4: Seller dashboard for product management (Basic view)**
    23. **Prompt 23 (Seller Product List API - Seller Specific):**
        "Create a Node.js/Express.js API route (`GET /api/seller/products`) that:
        1.  Is protected by `verifyToken` and `authorizeRoles(['seller'])`.
        2.  Fetches and returns only the products listed by the currently logged-in seller (`req.user.userId`).
        3.  Supports pagination. Prices in Rupees ₹."
    24. **Prompt 24 (Seller Product Management Page - React/Next.js):**
        "Generate a Next.js page component (`pages/seller/products/index.js`) for sellers to view their listed products. It should:
        1.  Be accessible only to 'seller' role users.
        2.  Fetch products from `/api/seller/products` using the seller's JWT.
        3.  Display products in a table or list with key details (name, price in Rupees ₹, quantity, status - for now, all are 'active').
        4.  Include a link/button to 'Add New Product' (linking to prompt 22's page)."
        *(Quick edit/delete would be further prompts)*.

---

**Managing Your 50 Prompts:**

* The above list is around 24 prompts, covering the absolute core of an MVP. This gives you room for follow-ups, refinements, bug-fixing prompts, or starting on more advanced features from your tickets.
* **Prioritize Based on Impact:** If a feature above seems too complex for a single or few prompts (like full Elasticsearch or Stripe Connect integration), simplify its MVP version further or break it into even smaller pieces.
* **Example of a Follow-up/Refinement Prompt:**
    * "Regarding Prompt 10 (Product Creation API): Modify the image handling. Instead of direct URLs, configure Multer to accept up to 10 image file uploads for the `images` field. For now, just save them to a local `/uploads` directory on the server and store their local paths in the `Product` model. We will integrate cloud storage later."
* **Testing:** You might need prompts for generating basic Jest unit tests for critical backend logic (as mentioned in AUTH-1).
    * **Prompt 25 (Unit Tests for Auth API):** "Generate Jest unit tests for the `/api/auth/register` and `/api/auth/login` endpoints created in Prompts 2 and 3. Cover successful cases, user already exists, incorrect credentials, and JWT generation. Mock the Mongoose User model."

**Next Steps After These MVP Prompts (using remaining prompts):**

* **Deepen Features:** Expand on cart (persistent backend cart), checkout (actual payment gateway integration - CART-4, this will take several prompts for frontend and backend).
* **Seller Tools:** Basic order management for sellers (SELL tickets), edit/delete product functionality.
* **Ratings & Reviews:** Start with product review submission and display (REV-1, REV-2).
* **Security Enhancements:** Password recovery (AUTH-5), secure session management (AUTH-4 details).
* **Admin Basics:** A very simple user list for admins (ADMIN-1).

Remember to test the AI's output thoroughly after each prompt and be prepared to ask for clarification or corrections. This detailed, iterative approach will help you build your platform systematically.