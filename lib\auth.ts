import { jwtVerify, SignJWT } from 'jose';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies';

// Secret key for JWT signing and verification
if (!process.env.JWT_SECRET) {
  console.warn('JWT_SECRET is not defined in environment variables. Using fallback secret key.');
}

// Make sure the secret is at least 32 characters for security
const JWT_SECRET_STRING = process.env.JWT_SECRET || 'your-secret-key-at-least-32-characters-long-for-security-and-more';
const JWT_SECRET = new TextEncoder().encode(JWT_SECRET_STRING);

export interface UserJwtPayload {
  id: string;
  email: string;
  role: 'buyer' | 'seller' | 'admin';
}

// Sign a new JWT token
export async function signToken(payload: UserJwtPayload): Promise<string> {
  try {
    // Add jti (JWT ID) for uniqueness and iat (Issued At) for tracking
    const token = await new SignJWT({
      ...payload,
      // Add any additional claims here
    })
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setIssuedAt()
      .setExpirationTime('24h') // Token expires in 24 hours
      .setJti(crypto.randomUUID()) // Add a unique identifier
      .sign(JWT_SECRET);

    console.log(`Token generated for user: ${payload.email} with role: ${payload.role}`);
    return token;
  } catch (error) {
    console.error('Error signing token:', error);
    throw new Error('Failed to generate authentication token');
  }
}

// Verify a JWT token
export async function verifyToken(token: string): Promise<UserJwtPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);

    // Validate that the payload has the required fields
    if (!payload.id || !payload.email || !payload.role) {
      console.warn('Invalid token payload structure:', payload);
      return null;
    }

    return payload as UserJwtPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// Set the JWT token in cookies
export async function setTokenCookie(token: string) {
  try {
    const cookieStore = await cookies();

    // Set the main authentication token (httpOnly for security)
    cookieStore.set({
      name: 'token',
      value: token,
      httpOnly: true,
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24, // 24 hours
    });

    // Also set a non-httpOnly cookie for client-side auth state detection
    // This doesn't contain sensitive data, just helps the UI know auth state
    cookieStore.set({
      name: 'auth-state',
      value: 'authenticated',
      httpOnly: false,
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24, // 24 hours
    });

    console.log('Authentication cookies set successfully');
  } catch (error) {
    console.error('Error setting authentication cookies:', error);
    throw new Error('Failed to set authentication cookies');
  }
}

// Get the JWT token from cookies
export async function getTokenCookie() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      console.log('No authentication token found in cookies');
    }

    return token;
  } catch (error) {
    console.error('Error getting token from cookies:', error);
    return undefined;
  }
}

// Delete the JWT token cookie
export async function deleteTokenCookie() {
  try {
    const cookieStore = await cookies();

    // Delete the main token
    cookieStore.delete('token');

    // Delete the auth state indicator
    cookieStore.delete('auth-state');

    console.log('Authentication cookies deleted successfully');
  } catch (error) {
    console.error('Error deleting authentication cookies:', error);
    throw new Error('Failed to delete authentication cookies');
  }
}

// Get the authenticated user from the request
export async function getAuthUser(req?: NextRequest): Promise<UserJwtPayload | null> {
  try {
    let token: string | undefined;

    if (req) {
      // For middleware and API routes
      token = req.cookies.get('token')?.value;
      console.log('Getting auth user from request cookies');
    } else {
      // For server components
      token = await getTokenCookie();
      console.log('Getting auth user from server cookies');
    }

    if (!token) {
      console.log('No token found, user is not authenticated');
      return null;
    }

    const user = await verifyToken(token);

    if (user) {
      console.log(`Authenticated user: ${user.email} with role: ${user.role}`);
    } else {
      console.log('Token verification failed, user is not authenticated');
    }

    return user;
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

// Check if the user is authenticated
export async function isAuthenticated(req?: NextRequest): Promise<boolean> {
  try {
    const user = await getAuthUser(req);
    const isAuth = user !== null;

    if (req) {
      console.log(`Authentication check for request to ${req.nextUrl.pathname}: ${isAuth}`);
    } else {
      console.log(`Authentication check from server component: ${isAuth}`);
    }

    return isAuth;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
}

// Check if the user has a specific role
export async function hasRole(role: 'buyer' | 'seller' | 'admin', req?: NextRequest): Promise<boolean> {
  try {
    const user = await getAuthUser(req);

    if (!user) {
      console.log(`Role check failed: User not authenticated`);
      return false;
    }

    // Admin role has access to everything
    const hasRequiredRole = user.role === role || user.role === 'admin';

    console.log(`Role check for ${user.email}: Required role: ${role}, User role: ${user.role}, Result: ${hasRequiredRole}`);

    return hasRequiredRole;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
}

// Middleware to protect routes
export async function authMiddleware(req: NextRequest) {
  try {
    const path = req.nextUrl.pathname;

    // Debug log
    console.log(`Auth middleware processing: ${path}`);

    // Public routes that don't require authentication
    const publicRoutes = [
      '/',
      '/login',
      '/register',
      '/product',
      '/category',
      '/cart',
      '/search',
      '/api/products',
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/logout'
    ];

    // Check if the path starts with any of the public routes
    const isPublicRoute = publicRoutes.some(route => {
      const isMatch = path.startsWith(route);
      if (isMatch) console.log(`Public route match: ${route}`);
      return isMatch;
    });

    // Static files and API routes that don't need auth
    const staticRoutes = [
      '/favicon.ico',
      '/_next',
      '/images',
      '/icons',
      '/placeholder.svg',
      '/api/health'
    ];

    // Check if the path starts with any of the static routes
    const isStaticRoute = staticRoutes.some(route => {
      const isMatch = path.startsWith(route);
      if (isMatch) console.log(`Static route match: ${route}`);
      return isMatch;
    });

    // Skip auth check for public and static routes
    if (isPublicRoute || isStaticRoute) {
      console.log(`Skipping auth check for ${path} (public or static route)`);
      return NextResponse.next();
    }

    // For protected routes, check authentication
    console.log(`Checking authentication for protected route: ${path}`);
    const isAuth = await isAuthenticated(req);

    // If not authenticated, redirect to login
    if (!isAuth) {
      console.log(`Redirecting unauthenticated user from ${path} to login`);
      const url = new URL('/login', req.url);
      url.searchParams.set('callbackUrl', path);
      return NextResponse.redirect(url);
    }

    // Get the authenticated user
    const user = await getAuthUser(req);

    if (!user) {
      console.log(`User should be authenticated but getAuthUser returned null for ${path}`);
      const url = new URL('/login', req.url);
      url.searchParams.set('callbackUrl', path);
      return NextResponse.redirect(url);
    }

    console.log(`Authenticated user accessing ${path}: ${user.email} (${user.role})`);

    // Seller routes protection
    if (path.startsWith('/sell') || path.startsWith('/seller')) {
      if (user.role !== 'seller' && user.role !== 'admin') {
        console.log(`Redirecting non-seller user from ${path} to account`);
        return NextResponse.redirect(new URL('/account', req.url));
      }
    }

    // Admin routes protection
    if (path.startsWith('/admin')) {
      if (user.role !== 'admin') {
        console.log(`Redirecting non-admin user from ${path} to home`);
        return NextResponse.redirect(new URL('/', req.url));
      }

      console.log(`Admin access granted to ${path} for ${user.email}`);
    }

    // User passed all checks, allow access
    return NextResponse.next();
  } catch (error) {
    console.error('Error in auth middleware:', error);

    // On error, redirect to login for safety
    const url = new URL('/login', req.url);
    return NextResponse.redirect(url);
  }
}
