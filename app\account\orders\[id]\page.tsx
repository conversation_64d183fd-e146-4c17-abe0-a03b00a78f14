"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/hooks/use-auth"
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  ChevronLeft,
  MapPin,
  Calendar,
  User,
  Phone,
  CreditCard,
  ArrowRight
} from "lucide-react"

// Mock order data
const mockOrders = [
  {
    id: "ORD123456",
    date: "2023-05-15T10:30:00Z",
    status: "delivered",
    total: 54999,
    paymentMethod: "Credit Card",
    shippingAddress: {
      name: "<PERSON>",
      street: "123 Main Street",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      phone: "+91 9876543210"
    },
    items: [
      {
        id: "1",
        name: "NVIDIA GeForce RTX 3070 8GB Graphics Card",
        price: 42999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      },
      {
        id: "5",
        name: "Corsair Vengeance LPX 32GB (2x16GB) DDR4 3200MHz RAM",
        price: 9999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ],
    timeline: [
      {
        status: "ordered",
        date: "2023-05-15T10:30:00Z",
        description: "Order placed successfully"
      },
      {
        status: "processing",
        date: "2023-05-15T14:45:00Z",
        description: "Payment confirmed, order is being processed"
      },
      {
        status: "shipped",
        date: "2023-05-16T11:20:00Z",
        description: "Order has been shipped via Express Delivery",
        trackingNumber: "TRACK123456789"
      },
      {
        status: "out_for_delivery",
        date: "2023-05-18T09:15:00Z",
        description: "Order is out for delivery"
      },
      {
        status: "delivered",
        date: "2023-05-18T16:30:00Z",
        description: "Order has been delivered successfully"
      }
    ]
  },
  {
    id: "ORD789012",
    date: "2023-05-20T14:15:00Z",
    status: "processing",
    total: 24999,
    paymentMethod: "UPI",
    shippingAddress: {
      name: "John Doe",
      street: "123 Main Street",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      phone: "+91 9876543210"
    },
    items: [
      {
        id: "2",
        name: "AMD Ryzen 7 5800X Desktop Processor",
        price: 24999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ],
    timeline: [
      {
        status: "ordered",
        date: "2023-05-20T14:15:00Z",
        description: "Order placed successfully"
      },
      {
        status: "processing",
        date: "2023-05-20T16:30:00Z",
        description: "Payment confirmed, order is being processed"
      }
    ]
  }
]

// Status configuration
const statusConfig = {
  ordered: {
    label: "Ordered",
    color: "bg-slate-500",
    icon: Clock
  },
  processing: {
    label: "Processing",
    color: "bg-blue-500",
    icon: Package
  },
  shipped: {
    label: "Shipped",
    color: "bg-amber-500",
    icon: Truck
  },
  out_for_delivery: {
    label: "Out for Delivery",
    color: "bg-purple-500",
    icon: Truck
  },
  delivered: {
    label: "Delivered",
    color: "bg-green-500",
    icon: CheckCircle
  },
  cancelled: {
    label: "Cancelled",
    color: "bg-red-500",
    icon: AlertCircle
  }
}

// Format date
function formatDate(dateString: string) {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
  return new Date(dateString).toLocaleDateString('en-IN', options)
}

export default function OrderDetailPage({ params }: { params: { id: string } }) {
  const { id } = params
  const { user } = useAuth()
  const router = useRouter()
  const { toast } = useToast()

  const [order, setOrder] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!user) {
      router.push("/login")
      return
    }

    // Fetch order details
    const fetchOrder = async () => {
      try {
        // In a real app, you would fetch from your API
        // For now, we'll use mock data
        await new Promise(resolve => setTimeout(resolve, 1000))

        const foundOrder = mockOrders.find(order => order.id === id)

        if (foundOrder) {
          setOrder(foundOrder)
        } else {
          toast({
            title: "Order Not Found",
            description: "The requested order could not be found.",
            variant: "destructive"
          })
          router.push("/account/orders")
        }
      } catch (error) {
        console.error("Error fetching order:", error)
        toast({
          title: "Error",
          description: "Failed to load order details. Please try again later.",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrder()
  }, [id, user, router, toast])

  if (isLoading) {
    return (
      <div className="container px-4 py-8 md:py-12">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-slate-200 rounded w-1/3"></div>
            <div className="h-64 bg-slate-200 rounded"></div>
            <div className="h-32 bg-slate-200 rounded"></div>
            <div className="h-48 bg-slate-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!order) return null

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" asChild className="mr-4">
            <Link href="/account/orders">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back to Orders
            </Link>
          </Button>
          <h1 className="text-2xl md:text-3xl font-bold">Order #{order.id}</h1>
        </div>

        {/* Order Status */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className={`${statusConfig[order.status as keyof typeof statusConfig].color} text-white`}>
                    {statusConfig[order.status as keyof typeof statusConfig].label}
                  </Badge>
                  <span className="text-sm text-slate-500">
                    Placed on {formatDate(order.date)}
                  </span>
                </div>
                <p className="text-slate-600">
                  {order.status === "delivered"
                    ? "Your order has been delivered. Thank you for shopping with us!"
                    : order.status === "processing"
                    ? "Your order is being processed. We'll update you when it ships."
                    : order.status === "shipped"
                    ? "Your order is on its way! Track your shipment below."
                    : ""}
                </p>
              </div>

              {order.status === "shipped" && order.timeline.find((t: any) => t.trackingNumber) && (
                <Button asChild className="bg-teal-600 hover:bg-teal-700">
                  <Link href="#" target="_blank">
                    Track Shipment
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Timeline */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Order Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-3.5 top-0 bottom-0 w-0.5 bg-slate-200"></div>

              {/* Timeline events */}
              <div className="space-y-6">
                {order.timeline.map((event: any, index: number) => {
                  const StatusIcon = statusConfig[event.status as keyof typeof statusConfig].icon

                  return (
                    <div key={index} className="relative pl-10">
                      {/* Status icon */}
                      <div className={`absolute left-0 top-0 w-7 h-7 rounded-full ${statusConfig[event.status as keyof typeof statusConfig].color} flex items-center justify-center text-white`}>
                        <StatusIcon className="h-4 w-4" />
                      </div>

                      <div>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                          <h3 className="font-medium">
                            {statusConfig[event.status as keyof typeof statusConfig].label}
                          </h3>
                          <span className="text-sm text-slate-500">
                            {formatDate(event.date)}
                          </span>
                        </div>
                        <p className="text-slate-600 mt-1">{event.description}</p>

                        {event.trackingNumber && (
                          <div className="mt-2">
                            <span className="text-sm font-medium">Tracking Number: </span>
                            <span className="text-sm">{event.trackingNumber}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Shipping Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-4 w-4 text-slate-500" />
                Shipping Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 text-sm">
                <p className="font-medium">{order.shippingAddress.name}</p>
                <p>{order.shippingAddress.street}</p>
                <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.pincode}</p>
                <p className="pt-2">{order.shippingAddress.phone}</p>
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-slate-500" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Method:</span> {order.paymentMethod}</p>
                <p><span className="font-medium">Total:</span> ₹{order.total.toLocaleString("en-IN")}</p>
                <p><span className="font-medium">Status:</span> Paid</p>
              </div>
            </CardContent>
          </Card>

          {/* Order Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-4 w-4 text-slate-500" />
                Order Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Order ID:</span> {order.id}</p>
                <p><span className="font-medium">Date:</span> {new Date(order.date).toLocaleDateString("en-IN")}</p>
                <p><span className="font-medium">Items:</span> {order.items.reduce((acc: number, item: any) => acc + item.quantity, 0)}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Items */}
        <Card>
          <CardHeader>
            <CardTitle>Order Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {order.items.map((item: any) => (
                <div key={item.id} className="flex flex-col sm:flex-row gap-4">
                  <div className="relative w-full sm:w-24 h-24">
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      className="object-cover rounded-md"
                    />
                  </div>
                  <div className="flex-1 flex flex-col sm:flex-row justify-between">
                    <div>
                      <h3 className="font-medium">{item.name}</h3>
                      <p className="text-sm text-slate-500">Quantity: {item.quantity}</p>
                    </div>
                    <div className="text-right mt-2 sm:mt-0">
                      <p className="font-medium">₹{item.price.toLocaleString("en-IN")}</p>
                    </div>
                  </div>
                </div>
              ))}

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>₹{order.total.toLocaleString("en-IN")}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>
                <div className="flex justify-between font-medium text-lg pt-2">
                  <span>Total</span>
                  <span>₹{order.total.toLocaleString("en-IN")}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
