"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { 
  Home, 
  Search, 
  ShoppingCart, 
  Heart, 
  User,
  Bell,
  Menu,
  LucideIcon
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/use-auth"
import { useCart } from "@/hooks/use-cart"
import { useNotifications } from "@/hooks/use-notifications"

interface NavItem {
  href: string
  label: string
  icon: LucideIcon
  badge?: number
  activePattern?: RegExp
}

export function MobileBottomNav() {
  const pathname = usePathname()
  const { user } = useAuth()
  const { itemCount } = useCart()
  const { unreadCount } = useNotifications()
  
  // Define navigation items
  const navItems: NavItem[] = [
    {
      href: "/",
      label: "Home",
      icon: Home,
      activePattern: /^\/$/, // Only match exact home path
    },
    {
      href: "/search",
      label: "Search",
      icon: Search,
      activePattern: /^\/search/,
    },
    {
      href: "/cart",
      label: "Cart",
      icon: ShoppingCart,
      badge: itemCount > 0 ? itemCount : undefined,
      activePattern: /^\/cart/,
    }
  ]
  
  // Add conditional items based on authentication
  if (user) {
    navItems.push(
      {
        href: "/account/notifications",
        label: "Notifications",
        icon: Bell,
        badge: unreadCount > 0 ? unreadCount : undefined,
        activePattern: /^\/account\/notifications/,
      },
      {
        href: "/account",
        label: "Account",
        icon: User,
        activePattern: /^\/account/,
      }
    )
  } else {
    navItems.push(
      {
        href: "/wishlist",
        label: "Wishlist",
        icon: Heart,
        activePattern: /^\/wishlist/,
      },
      {
        href: "/login",
        label: "Login",
        icon: User,
        activePattern: /^\/login|^\/register/,
      }
    )
  }
  
  return (
    <nav className="h-16 px-2">
      <div className="grid h-full grid-cols-5 gap-1">
        {navItems.map((item) => {
          const isActive = item.activePattern 
            ? item.activePattern.test(pathname)
            : pathname === item.href
            
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center gap-1 text-xs",
                isActive 
                  ? "text-primary font-medium" 
                  : "text-muted-foreground"
              )}
            >
              <div className="relative">
                <item.icon className="h-5 w-5" />
                {item.badge && (
                  <Badge 
                    className="absolute -top-1.5 -right-1.5 h-4 w-4 p-0 flex items-center justify-center text-[10px]"
                    variant={item.href === "/account/notifications" ? "destructive" : "default"}
                  >
                    {item.badge > 99 ? "99+" : item.badge}
                  </Badge>
                )}
              </div>
              <span>{item.label}</span>
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
