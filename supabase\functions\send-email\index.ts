// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// Import the SMTP client
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    name: string;
    content: string;
    contentType: string;
  }>;
}

serve(async (req) => {
  try {
    // Get the email options from the request body
    const emailOptions: EmailOptions = await req.json();

    // Validate required fields
    if (!emailOptions.to || !emailOptions.subject || (!emailOptions.text && !emailOptions.html)) {
      return new Response(
        JSON.stringify({
          error: "Missing required fields: to, subject, and either text or html content",
        }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get SMTP configuration from environment variables
    const SMTP_HOST = Deno.env.get("SMTP_HOST") || "smtp.gmail.com";
    const SMTP_PORT = parseInt(Deno.env.get("SMTP_PORT") || "587");
    const SMTP_USERNAME = Deno.env.get("SMTP_USERNAME") || "";
    const SMTP_PASSWORD = Deno.env.get("SMTP_PASSWORD") || "";
    const DEFAULT_FROM = Deno.env.get("DEFAULT_FROM") || "<EMAIL>";

    // Check if SMTP credentials are available
    if (!SMTP_USERNAME || !SMTP_PASSWORD) {
      console.error("SMTP credentials not configured");
      return new Response(
        JSON.stringify({
          error: "Email service not configured",
        }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    // Create SMTP client
    const client = new SmtpClient();

    // Connect to SMTP server
    await client.connectTLS({
      hostname: SMTP_HOST,
      port: SMTP_PORT,
      username: SMTP_USERNAME,
      password: SMTP_PASSWORD,
    });

    // Prepare email
    const email = {
      from: emailOptions.from || DEFAULT_FROM,
      to: emailOptions.to,
      subject: emailOptions.subject,
      content: emailOptions.html || "",
      html: emailOptions.html || "",
      text: emailOptions.text || "",
    };

    // Add reply-to if provided
    if (emailOptions.replyTo) {
      email.replyTo = emailOptions.replyTo;
    }

    // Send email
    await client.send(email);
    await client.close();

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: "Email sent successfully",
      }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error sending email:", error);
    
    // Return error response
    return new Response(
      JSON.stringify({
        error: error.message || "Failed to send email",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
})

/* To invoke:
curl -i --location --request POST 'http://localhost:54321/functions/v1/send-email' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
  --header 'Content-Type: application/json' \
  --data '{"to":"<EMAIL>","subject":"Test Email","html":"<p>This is a test email</p>"}'
*/
