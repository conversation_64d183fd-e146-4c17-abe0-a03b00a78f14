# PASSDOWN - Multi-Vendor Marketplace for Second-Hand Products

PASSDOWN is a modern e-commerce platform for buying and selling second-hand products, with a focus on transparency and sustainability. The platform allows users to register as buyers or sellers, list products with detailed condition information, browse listings, add items to cart, and complete purchases.

## Features

- **User Authentication**
  - Secure login and registration
  - Role-based access control (buyer, seller, admin)
  - Protected routes

- **Product Listings**
  - Detailed product pages with images, descriptions, and specifications
  - Product categorization
  - Condition indicators (New, Like New, Used)

- **Shopping Experience**
  - Cart functionality
  - Wishlist
  - Checkout process

- **Seller Tools**
  - Product listing creation
  - Sales management
  - Seller profile

- **Responsive Design**
  - Mobile-friendly interface
  - Accessible components

## Tech Stack

- **Frontend**
  - Next.js (React framework)
  - TypeScript
  - Tailwind CSS
  - shadcn/ui components

- **Backend**
  - Next.js API routes
  - Supabase (PostgreSQL database)
  - Row Level Security (RLS) policies

- **Authentication**
  - JWT (JSON Web Tokens)
  - HTTP-only cookies
  - Supabase Auth

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm, yarn, or pnpm
- Supabase account

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/passdown.git
   cd passdown
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. Set up Supabase:
   - Create a new project on [Supabase](https://supabase.com/)
   - Go to the SQL Editor and run the SQL scripts from the `supabase/schema.sql` file
   - Run the seed data script from `supabase/seed.sql`
   - Get your project URL and anon key from the API settings

4. Set up environment variables:
   Create a `.env` file in the root directory using the `.env.example` template:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   JWT_SECRET="your-secret-key-at-least-32-characters-long"
   NEXT_PUBLIC_API_URL="http://localhost:3000/api"
   ```

5. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

   Alternatively, you can use the provided batch file (Windows):
   ```bash
   start-dev-server.bat
   ```

### Test Users

The following test users are created automatically when you initialize the database:

#### Admin
- Email: <EMAIL>
- Password: admin123

#### Seller
- Email: <EMAIL>
- Password: seller123

#### Buyer
- Email: <EMAIL>
- Password: buyer123

### Troubleshooting

If you encounter issues starting the development server:

1. **Environment Variables**: Make sure your `.env.local` file contains all the required variables:
   ```
   # Environment variables
   DATABASE_URL="file:./dev.db"
   JWT_SECRET="your-secret-key-at-least-32-characters-long-for-security"
   NEXT_PUBLIC_API_URL="http://localhost:3000/api"

   # Supabase Configuration (optional)
   NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
   NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
   ```

2. **Node.js Version**: Ensure you're using Node.js 18.x or higher:
   ```bash
   node --version
   ```

3. **Database Initialization**: If you're having database issues, try resetting it:
   ```bash
   npx prisma migrate reset --force
   ```

4. **Clear Cache**: Try clearing the Next.js cache:
   ```bash
   npm run clean
   ```

5. **Port Conflict**: If port 3000 is already in use, you can specify a different port:
   ```bash
   npm run dev -- -p 3001
   ```

6. **Dependencies**: Make sure all dependencies are installed correctly:
   ```bash
   rm -rf node_modules
   npm install
   ```

7. **Authentication Issues**: If you're having authentication problems, try using the test script:
   ```bash
   node test-auth.js
   ```

## Project Structure

```
passdown/
├── app/                  # Next.js app directory
│   ├── api/              # API routes
│   │   ├── auth/         # Authentication endpoints
│   │   ├── products/     # Product endpoints
│   │   └── ...           # Other API endpoints
│   ├── (routes)/         # Page routes
│   └── ...               # Layout components
├── components/           # Shared UI components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   └── ...               # Other components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
│   ├── auth.ts           # Authentication utilities
│   ├── db.ts             # Prisma client
│   ├── supabase.ts       # Supabase client
│   └── repositories/     # Data access layer
├── prisma/               # Prisma configuration
│   ├── schema.prisma     # Database schema
│   ├── migrations/       # Database migrations
│   └── seed.ts           # Seed data
├── scripts/              # Utility scripts
├── public/               # Static assets
└── ...
```

## Database Schema

The database schema includes the following tables:

- **users**: User accounts with role-based access control
- **seller_profiles**: Extended information for seller accounts
- **categories**: Product categories with hierarchical structure
- **products**: Product listings with detailed condition information
- **product_images**: Images associated with products
- **orders**: Customer orders
- **order_items**: Individual items within orders
- **wishlist**: User wishlist items

The application supports both SQLite (local development) and Supabase (PostgreSQL) databases with the same schema structure.

## Implemented Features

- **Order Management System**
  - Comprehensive order tracking with real-time status updates
  - Email and in-app notifications for order status changes
  - Detailed order history pages showing past purchases
  - Order filtering and search functionality

- **Performance Optimization**
  - Next.js server-side rendering for product listing and detail pages
  - Image optimization with next/image and responsive sizing
  - Database query caching and optimization
  - Code splitting and lazy loading for non-critical components

- **Mobile Experience Enhancement**
  - Refined responsive design for all screen sizes (320px to 2560px)
  - Optimized touch targets and interactions for mobile users
  - Progressive web app (PWA) capabilities
  - Mobile-specific features like swipe navigation and bottom sheets

## Future Enhancements

- **Payment Integration**
  - Integrate with Razorpay or other payment gateways
  - Implement secure payment handling

- **User Reviews**
  - Product and seller review system
  - Rating system

- **Advanced Analytics**
  - Seller dashboard with sales analytics
  - User behavior tracking
  - Inventory management

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
