"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart3, 
  Package, 
  ShoppingCart, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Plus, 
  Settings, 
  AlertCircle, 
  Loader2,
  ArrowUpRight,
  ArrowDownRight,
  Clock
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { allProducts } from "@/app/data/enhanced-products"

export default function SellerDashboardPage() {
  const router = useRouter()
  const { user, isLoading: authLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  
  // Mock data for seller dashboard
  const dashboardData = {
    totalSales: 124500,
    totalOrders: 38,
    totalProducts: 16,
    totalCustomers: 29,
    recentOrders: [
      {
        id: "ORD-001",
        customer: "Rahul Sharma",
        date: "2023-05-10",
        amount: 12999,
        status: "Delivered",
        items: 2
      },
      {
        id: "ORD-002",
        customer: "Priya Patel",
        date: "2023-05-08",
        amount: 8499,
        status: "Shipped",
        items: 1
      },
      {
        id: "ORD-003",
        customer: "Amit Kumar",
        date: "2023-05-05",
        amount: 24999,
        status: "Processing",
        items: 3
      },
      {
        id: "ORD-004",
        customer: "Sneha Gupta",
        date: "2023-05-03",
        amount: 5999,
        status: "Delivered",
        items: 1
      },
      {
        id: "ORD-005",
        customer: "Vikram Singh",
        date: "2023-05-01",
        amount: 36999,
        status: "Delivered",
        items: 2
      }
    ],
    salesData: {
      thisMonth: 42500,
      lastMonth: 38000,
      growth: 11.8
    },
    pendingActions: [
      {
        type: "order",
        id: "ORD-003",
        description: "Order needs to be processed",
        time: "2 days ago"
      },
      {
        type: "return",
        id: "RET-001",
        description: "Return request needs approval",
        time: "1 day ago"
      },
      {
        type: "inventory",
        id: "PRD-005",
        description: "Low stock alert (2 items remaining)",
        time: "3 hours ago"
      },
      {
        type: "message",
        id: "MSG-012",
        description: "Customer inquiry about product",
        time: "5 hours ago"
      }
    ]
  }
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?callbackUrl=/seller/dashboard")
    } else if (!authLoading && user && user.role !== "seller") {
      router.push("/account")
    } else {
      // Simulate loading data
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [user, authLoading, router])
  
  // Show loading or redirect if not authenticated or not a seller
  if (authLoading || isLoading || !user || user.role !== "seller") {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-teal-600" />
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your seller dashboard.</p>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Seller Dashboard</h1>
          <p className="text-slate-500 mt-1">
            Manage your products, orders, and sales
          </p>
        </div>
        <div className="flex gap-3">
          <Button asChild className="bg-teal-600 hover:bg-teal-700">
            <Link href="/seller/products/new">
              <Plus className="h-4 w-4 mr-2" />
              Add New Product
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/seller/settings">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 md:grid-cols-5 mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="analytics" className="hidden md:block">Analytics</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Sales</p>
                    <h3 className="text-2xl font-bold mt-1">₹{dashboardData.totalSales.toLocaleString("en-IN")}</h3>
                  </div>
                  <div className="bg-teal-100 p-3 rounded-full">
                    <DollarSign className="h-5 w-5 text-teal-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>12.5%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Orders</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalOrders}</h3>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <ShoppingCart className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>8.2%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Products</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalProducts}</h3>
                  </div>
                  <div className="bg-purple-100 p-3 rounded-full">
                    <Package className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-green-500">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>4.3%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500">Total Customers</p>
                    <h3 className="text-2xl font-bold mt-1">{dashboardData.totalCustomers}</h3>
                  </div>
                  <div className="bg-amber-100 p-3 rounded-full">
                    <Users className="h-5 w-5 text-amber-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <div className="flex items-center text-red-500">
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                    <span>2.1%</span>
                  </div>
                  <span className="text-slate-500 ml-2">from last month</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Recent Orders and Pending Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Latest orders from your customers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b text-xs text-slate-500">
                        <th className="text-left py-3 px-2">Order ID</th>
                        <th className="text-left py-3 px-2">Customer</th>
                        <th className="text-left py-3 px-2">Date</th>
                        <th className="text-left py-3 px-2">Amount</th>
                        <th className="text-left py-3 px-2">Status</th>
                        <th className="text-left py-3 px-2">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dashboardData.recentOrders.map((order) => (
                        <tr key={order.id} className="border-b">
                          <td className="py-3 px-2 font-medium">{order.id}</td>
                          <td className="py-3 px-2">{order.customer}</td>
                          <td className="py-3 px-2">{order.date}</td>
                          <td className="py-3 px-2">₹{order.amount.toLocaleString("en-IN")}</td>
                          <td className="py-3 px-2">
                            <Badge variant={
                              order.status === "Delivered" ? "success" :
                              order.status === "Shipped" ? "default" :
                              "secondary"
                            }>
                              {order.status}
                            </Badge>
                          </td>
                          <td className="py-3 px-2">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={`/seller/orders/${order.id}`}>View</Link>
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="mt-4 text-center">
                  <Button variant="outline" asChild>
                    <Link href="/seller/orders">View All Orders</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Pending Actions</CardTitle>
                <CardDescription>Items that need your attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.pendingActions.map((action, index) => (
                    <div key={index} className="flex items-start gap-3 pb-4 border-b last:border-0 last:pb-0">
                      <div className={`p-2 rounded-full ${
                        action.type === "order" ? "bg-blue-100" :
                        action.type === "return" ? "bg-amber-100" :
                        action.type === "inventory" ? "bg-red-100" :
                        "bg-purple-100"
                      }`}>
                        {action.type === "order" ? (
                          <ShoppingCart className="h-4 w-4 text-blue-600" />
                        ) : action.type === "return" ? (
                          <TrendingUp className="h-4 w-4 text-amber-600" />
                        ) : action.type === "inventory" ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <Users className="h-4 w-4 text-purple-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium">{action.id}</h4>
                          <span className="text-xs text-slate-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {action.time}
                          </span>
                        </div>
                        <p className="text-sm text-slate-600 mt-1">{action.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Monthly Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Sales</CardTitle>
              <CardDescription>Sales performance over the last 6 months</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Sales Analytics</h3>
                  <p className="text-slate-500 max-w-md">
                    Detailed sales analytics will be available here. For now, your sales this month are ₹{dashboardData.salesData.thisMonth.toLocaleString("en-IN")}, which is {dashboardData.salesData.growth}% higher than last month.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Your Products</CardTitle>
                  <CardDescription>Manage your product listings</CardDescription>
                </div>
                <Button asChild className="bg-teal-600 hover:bg-teal-700">
                  <Link href="/seller/products/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Product
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {allProducts.slice(0, 6).map((product) => (
                  <Card key={product.id} className="overflow-hidden">
                    <div className="relative aspect-square">
                      <Image
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-medium line-clamp-2">{product.name}</h3>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</span>
                        <Badge variant="outline" className="text-xs">
                          {product.condition}
                        </Badge>
                      </div>
                      <div className="mt-4 flex gap-2">
                        <Button variant="outline" size="sm" className="flex-1" asChild>
                          <Link href={`/seller/products/${product.id}`}>Edit</Link>
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1">Delete</Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline" asChild>
                  <Link href="/seller/products">View All Products</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Orders Tab */}
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Order Management</CardTitle>
              <CardDescription>View and manage all your orders</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Package className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Order Management</h3>
                <p className="text-slate-500 max-w-md mx-auto mb-6">
                  The complete order management interface will be available here. You'll be able to view, filter, and manage all your orders in one place.
                </p>
                <Button variant="outline" asChild>
                  <Link href="/seller/orders">Go to Order Management</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Customers Tab */}
        <TabsContent value="customers">
          <Card>
            <CardHeader>
              <CardTitle>Customer Management</CardTitle>
              <CardDescription>View and manage your customer relationships</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Customer Management</h3>
                <p className="text-slate-500 max-w-md mx-auto mb-6">
                  The complete customer management interface will be available here. You'll be able to view customer profiles, purchase history, and manage communications.
                </p>
                <Button variant="outline" asChild>
                  <Link href="/seller/customers">Go to Customer Management</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Sales Analytics</CardTitle>
              <CardDescription>Detailed insights into your sales performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-slate-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Sales Analytics</h3>
                <p className="text-slate-500 max-w-md mx-auto mb-6">
                  The complete analytics dashboard will be available here. You'll be able to view detailed reports, charts, and insights about your sales performance.
                </p>
                <Button variant="outline" asChild>
                  <Link href="/seller/analytics">Go to Analytics Dashboard</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
