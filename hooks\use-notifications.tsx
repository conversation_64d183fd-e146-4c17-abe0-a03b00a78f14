"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "./use-auth"

export type NotificationType = "order_status" | "payment" | "delivery" | "system" | "promotion"

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  createdAt: string
  link?: string
  data?: Record<string, any>
}

interface NotificationsContextType {
  notifications: Notification[]
  unreadCount: number
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearNotifications: () => void
  addNotification: (notification: Omit<Notification, "id" | "createdAt" | "read">) => void
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined)

const NOTIFICATIONS_STORAGE_KEY = "passdown-notifications"

// Mock notifications for development
const mockNotifications: Notification[] = [
  {
    id: "notif-1",
    type: "order_status",
    title: "Order Shipped",
    message: "Your order #ORD123456 has been shipped and is on its way!",
    read: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    link: "/account/orders/ORD123456",
    data: { orderId: "ORD123456", status: "shipped" }
  },
  {
    id: "notif-2",
    type: "payment",
    title: "Payment Successful",
    message: "Your payment of ₹24,999 for order #ORD789012 was successful.",
    read: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    link: "/account/orders/ORD789012",
    data: { orderId: "ORD789012", amount: 24999 }
  },
  {
    id: "notif-3",
    type: "system",
    title: "Welcome to PASSDOWN",
    message: "Thank you for joining PASSDOWN! Start exploring quality tech products.",
    read: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
    link: "/"
  },
  {
    id: "notif-4",
    type: "promotion",
    title: "Weekend Sale",
    message: "Enjoy up to 30% off on selected products this weekend!",
    read: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    link: "/deals"
  }
]

export function NotificationsProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const { toast } = useToast()
  const { user } = useAuth()

  // Load notifications from localStorage on mount
  useEffect(() => {
    if (user) {
      try {
        const savedNotifications = localStorage.getItem(`${NOTIFICATIONS_STORAGE_KEY}-${user.id}`)
        if (savedNotifications) {
          setNotifications(JSON.parse(savedNotifications))
        } else {
          // Use mock notifications for development
          setNotifications(mockNotifications)
        }
      } catch (error) {
        console.error("Failed to load notifications from localStorage:", error)
      }
    }
  }, [user])

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (user && notifications.length > 0) {
      try {
        localStorage.setItem(`${NOTIFICATIONS_STORAGE_KEY}-${user.id}`, JSON.stringify(notifications))
      } catch (error) {
        console.error("Failed to save notifications to localStorage:", error)
      }
    }
  }, [notifications, user])

  const unreadCount = notifications.filter(notification => !notification.read).length

  const markAsRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, read: true }))
    )
  }

  const clearNotifications = () => {
    setNotifications([])
  }

  const addNotification = (notification: Omit<Notification, "id" | "createdAt" | "read">) => {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      createdAt: new Date().toISOString(),
      read: false
    }

    setNotifications(prevNotifications => [newNotification, ...prevNotifications])

    // Show toast for new notification
    toast({
      title: notification.title,
      description: notification.message,
      action: notification.link ? {
        label: "View",
        onClick: () => {
          window.location.href = notification.link || "/"
          markAsRead(newNotification.id)
        }
      } : undefined
    })
  }

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        addNotification
      }}
    >
      {children}
    </NotificationsContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationsContext)
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationsProvider")
  }
  return context
}
