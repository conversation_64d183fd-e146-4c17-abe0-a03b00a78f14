import { DbClient } from '../supabase-client';

export interface ProductCreateInput {
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  mainImage: string;
  condition: string;
  categoryId: string;
  sellerId: string;
  location?: string;
  isSecondHand: boolean;
  usageHistory?: string;
  defectsDisclosure?: string;
  quantity: number;
  status?: 'active' | 'inactive' | 'sold' | 'deleted';
  featured?: boolean;
  featuredOrder?: number;
  images?: {
    url: string;
  }[];
}

export interface ProductUpdateInput {
  name?: string;
  description?: string;
  price?: number;
  originalPrice?: number;
  mainImage?: string;
  condition?: string;
  categoryId?: string;
  location?: string;
  isSecondHand?: boolean;
  usageHistory?: string;
  defectsDisclosure?: string;
  quantity?: number;
  status?: 'active' | 'inactive' | 'sold' | 'deleted';
  featured?: boolean;
  featuredOrder?: number;
}

export class ProductRepository {
  /**
   * Find a product by ID
   */
  static async findById(id: string) {
    return DbClient.findById('products', id, {
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Find all products with optional filtering
   */
  static async findAll(options: any = {}) {
    return DbClient.findMany('products', {
      ...options,
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Find featured products
   */
  static async findFeatured(limit: number = 10) {
    return DbClient.findMany('products', {
      where: {
        featured: true,
        status: 'active',
      },
      orderBy: {
        featuredOrder: 'asc',
      },
      take: limit,
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Find products by category
   */
  static async findByCategory(categoryId: string, options: any = {}) {
    return DbClient.findMany('products', {
      ...options,
      where: {
        ...options.where,
        categoryId,
        status: 'active',
      },
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Find products by seller
   */
  static async findBySeller(sellerId: string, options: any = {}) {
    return DbClient.findMany('products', {
      ...options,
      where: {
        ...options.where,
        sellerId,
      },
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Search products
   */
  static async search(query: string, options: any = {}) {
    return DbClient.findMany('products', {
      ...options,
      where: {
        ...options.where,
        OR: [
          { name: { contains: query } },
          { description: { contains: query } },
        ],
        status: 'active',
      },
      include: {
        category: true,
        seller: {
          include: {
            sellerProfile: true,
          },
        },
        images: true,
      },
    });
  }

  /**
   * Create a new product
   */
  static async create(data: ProductCreateInput) {
    // Extract images if present
    const { images, ...productData } = data;

    // Create product
    const product = await DbClient.create('products', {
      ...productData,
      status: productData.status || 'active',
      views: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create images if provided
    if (images && images.length > 0) {
      for (const image of images) {
        await DbClient.create('product_images', {
          productId: product.id,
          url: image.url,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    }

    // Fetch the product again with all relations
    return this.findById(product.id);
  }

  /**
   * Update an existing product
   */
  static async update(id: string, data: ProductUpdateInput) {
    // Update product
    const product = await DbClient.update('products', id, {
      ...data,
      updatedAt: new Date(),
    });

    // Fetch the product again with all relations
    return this.findById(product.id);
  }

  /**
   * Delete a product
   */
  static async delete(id: string) {
    return DbClient.delete('products', id);
  }

  /**
   * Count products with optional filtering
   */
  static async count(where: any = {}) {
    return DbClient.count('products', where);
  }

  /**
   * Increment product views
   */
  static async incrementViews(id: string) {
    const product = await this.findById(id);
    
    if (!product) {
      throw new Error('Product not found');
    }

    return DbClient.update('products', id, {
      views: (product.views || 0) + 1,
      updatedAt: new Date(),
    });
  }

  /**
   * Add an image to a product
   */
  static async addImage(productId: string, imageUrl: string) {
    return DbClient.create('product_images', {
      productId,
      url: imageUrl,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  /**
   * Remove an image from a product
   */
  static async removeImage(imageId: string) {
    return DbClient.delete('product_images', imageId);
  }

  /**
   * Set product as featured
   */
  static async setFeatured(id: string, featured: boolean, order?: number) {
    return DbClient.update('products', id, {
      featured,
      featuredOrder: order,
      updatedAt: new Date(),
    });
  }
}
