"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  X,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  UserCheck,
  UserX,
  Edit,
  Trash2,
  Filter,
  Download,
  Mail,
  Eye,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface User {
  id: string
  name: string
  email: string
  role: "buyer" | "seller" | "admin"
  avatar: string | null
  createdAt: string
  status: "active" | "pending" | "suspended"
  sellerProfile?: {
    storeName: string | null
    verified: boolean
    rating: number | null
  }
}

export default function UsersPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [users, setUsers] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  const usersPerPage = 10

  // Fetch users
  useEffect(() => {
    // Simulate API call with mock data
    setTimeout(() => {
      const mockUsers: User[] = [
        {
          id: "user-1",
          name: "Admin User",
          email: "<EMAIL>",
          role: "admin",
          avatar: null,
          createdAt: "2023-01-01T00:00:00Z",
          status: "active",
        },
        {
          id: "user-2",
          name: "Rahul Sharma",
          email: "<EMAIL>",
          role: "seller",
          avatar: null,
          createdAt: "2023-02-15T10:30:00Z",
          status: "active",
          sellerProfile: {
            storeName: "Tech Treasures",
            verified: true,
            rating: 4.8,
          },
        },
        {
          id: "user-3",
          name: "Priya Patel",
          email: "<EMAIL>",
          role: "seller",
          avatar: null,
          createdAt: "2023-03-10T14:45:00Z",
          status: "pending",
          sellerProfile: {
            storeName: "Gadget Galaxy",
            verified: false,
            rating: null,
          },
        },
        {
          id: "user-4",
          name: "Amit Kumar",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-04-05T09:15:00Z",
          status: "active",
        },
        {
          id: "user-5",
          name: "Sneha Gupta",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-04-20T16:30:00Z",
          status: "active",
        },
        {
          id: "user-6",
          name: "Vikram Singh",
          email: "<EMAIL>",
          role: "seller",
          avatar: null,
          createdAt: "2023-05-01T11:45:00Z",
          status: "suspended",
          sellerProfile: {
            storeName: "PC Components",
            verified: true,
            rating: 3.2,
          },
        },
        {
          id: "user-7",
          name: "Neha Sharma",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-05-10T08:20:00Z",
          status: "active",
        },
        {
          id: "user-8",
          name: "Rajesh Verma",
          email: "<EMAIL>",
          role: "seller",
          avatar: null,
          createdAt: "2023-05-15T13:10:00Z",
          status: "pending",
          sellerProfile: {
            storeName: "Memory Masters",
            verified: false,
            rating: null,
          },
        },
        {
          id: "user-9",
          name: "Ananya Desai",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-05-18T15:30:00Z",
          status: "active",
        },
        {
          id: "user-10",
          name: "Karan Malhotra",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-05-20T10:45:00Z",
          status: "active",
        },
        {
          id: "user-11",
          name: "Divya Reddy",
          email: "<EMAIL>",
          role: "seller",
          avatar: null,
          createdAt: "2023-05-22T09:15:00Z",
          status: "active",
          sellerProfile: {
            storeName: "Storage Solutions",
            verified: true,
            rating: 4.5,
          },
        },
        {
          id: "user-12",
          name: "Arjun Nair",
          email: "<EMAIL>",
          role: "buyer",
          avatar: null,
          createdAt: "2023-05-25T14:20:00Z",
          status: "active",
        },
      ]

      setUsers(mockUsers)
      setFilteredUsers(mockUsers)
      setTotalPages(Math.ceil(mockUsers.length / usersPerPage))
      setIsLoading(false)
    }, 1000)
  }, [])

  // Apply filters
  useEffect(() => {
    let filtered = users

    // Apply search filter
    if (searchQuery.trim() !== "") {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply role filter
    if (roleFilter !== "all") {
      filtered = filtered.filter((user) => user.role === roleFilter)
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((user) => user.status === statusFilter)
    }

    setFilteredUsers(filtered)
    setTotalPages(Math.ceil(filtered.length / usersPerPage))
    setCurrentPage(1)
  }, [searchQuery, roleFilter, statusFilter, users])

  // Get current users for pagination
  const getCurrentUsers = () => {
    const startIndex = (currentPage - 1) * usersPerPage
    const endIndex = startIndex + usersPerPage
    return filteredUsers.slice(startIndex, endIndex)
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy")
    } catch (error) {
      return "Invalid date"
    }
  }

  // Verify seller
  const verifySeller = (userId: string) => {
    setUsers(
      users.map((user) => {
        if (user.id === userId && user.sellerProfile) {
          return {
            ...user,
            sellerProfile: {
              ...user.sellerProfile,
              verified: true,
            },
            status: "active" as "active",
          }
        }
        return user
      })
    )
  }

  // Suspend user
  const suspendUser = (userId: string) => {
    if (confirm("Are you sure you want to suspend this user?")) {
      setUsers(
        users.map((user) => {
          if (user.id === userId) {
            return {
              ...user,
              status: "suspended" as "suspended",
            }
          }
          return user
        })
      )
    }
  }

  // Activate user
  const activateUser = (userId: string) => {
    setUsers(
      users.map((user) => {
        if (user.id === userId) {
          return {
            ...user,
            status: "active" as "active",
          }
        }
        return user
      })
    )
  }

  // Delete user
  const deleteUser = (userId: string) => {
    if (confirm("Are you sure you want to delete this user? This action cannot be undone.")) {
      setUsers(users.filter((user) => user.id !== userId))
      setFilteredUsers(filteredUsers.filter((user) => user.id !== userId))
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading users...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">User Management</h1>
          <p className="text-slate-500 mt-1">Manage users, sellers, and admin accounts</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Users
          </Button>
          <Button asChild className="bg-teal-600 hover:bg-teal-700">
            <Link href="/admin/users/new">
              <UserCheck className="h-4 w-4 mr-2" />
              Add New User
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
          <CardDescription>View and manage all user accounts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <Input
                placeholder="Search users by name or email..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear search</span>
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="buyer">Buyers</SelectItem>
                  <SelectItem value="seller">Sellers</SelectItem>
                  <SelectItem value="admin">Admins</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left font-medium p-2 pl-0">User</th>
                  <th className="text-left font-medium p-2">Role</th>
                  <th className="text-left font-medium p-2">Status</th>
                  <th className="text-left font-medium p-2">Joined</th>
                  <th className="text-right font-medium p-2 pr-0">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentUsers().map((user) => (
                  <tr key={user.id} className="border-b">
                    <td className="p-2 pl-0">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-slate-200 flex items-center justify-center text-slate-600 flex-shrink-0">
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-xs text-slate-500">{user.email}</p>
                        </div>
                      </div>
                    </td>
                    <td className="p-2">
                      <Badge
                        className={
                          user.role === "admin"
                            ? "bg-purple-100 text-purple-800 hover:bg-purple-100"
                            : user.role === "seller"
                            ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                            : "bg-slate-100 text-slate-800 hover:bg-slate-100"
                        }
                      >
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        {user.role === "seller" && user.sellerProfile?.verified && (
                          <span className="ml-1">✓</span>
                        )}
                      </Badge>
                      {user.role === "seller" && user.sellerProfile?.storeName && (
                        <p className="text-xs text-slate-500 mt-1">{user.sellerProfile.storeName}</p>
                      )}
                    </td>
                    <td className="p-2">
                      <Badge
                        className={
                          user.status === "active"
                            ? "bg-green-100 text-green-800 hover:bg-green-100"
                            : user.status === "pending"
                            ? "bg-amber-100 text-amber-800 hover:bg-amber-100"
                            : "bg-red-100 text-red-800 hover:bg-red-100"
                        }
                      >
                        {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="p-2">{formatDate(user.createdAt)}</td>
                    <td className="p-2 pr-0 text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/users/${user.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/users/${user.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit User
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`mailto:${user.email}`}>
                              <Mail className="h-4 w-4 mr-2" />
                              Contact User
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {user.role === "seller" && user.status === "pending" && (
                            <DropdownMenuItem onClick={() => verifySeller(user.id)}>
                              <UserCheck className="h-4 w-4 mr-2 text-green-600" />
                              <span className="text-green-600">Verify Seller</span>
                            </DropdownMenuItem>
                          )}
                          {user.status === "active" && (
                            <DropdownMenuItem onClick={() => suspendUser(user.id)}>
                              <UserX className="h-4 w-4 mr-2 text-amber-600" />
                              <span className="text-amber-600">Suspend User</span>
                            </DropdownMenuItem>
                          )}
                          {user.status === "suspended" && (
                            <DropdownMenuItem onClick={() => activateUser(user.id)}>
                              <UserCheck className="h-4 w-4 mr-2 text-green-600" />
                              <span className="text-green-600">Activate User</span>
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            onClick={() => deleteUser(user.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-slate-500">
                Showing {(currentPage - 1) * usersPerPage + 1} to{" "}
                {Math.min(currentPage * usersPerPage, filteredUsers.length)} of {filteredUsers.length} users
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8"
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Previous page</span>
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8"
                >
                  <ChevronRight className="h-4 w-4" />
                  <span className="sr-only">Next page</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
