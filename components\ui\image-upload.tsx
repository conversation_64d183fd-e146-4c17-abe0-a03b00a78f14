"use client"

import { useState, useRef } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Trash2, Upload, X, Image as ImageIcon, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { uploadImage } from "@/lib/image-upload"

interface ImageUploadProps {
  value?: string
  onChange: (value: string) => void
  onRemove?: () => void
  disabled?: boolean
  className?: string
  bucket?: string
  folder?: string
  maxSizeMB?: number
  accept?: string
  aspectRatio?: number
  width?: number
  height?: number
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled = false,
  className,
  bucket = "product-images",
  folder,
  maxSizeMB = 5,
  accept = "image/*",
  aspectRatio,
  width = 300,
  height = 300,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    if (file.size > maxSizeBytes) {
      setError(`File size exceeds ${maxSizeMB}MB limit`)
      return
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      setError("Only image files are allowed")
      return
    }

    // Create a preview
    const objectUrl = URL.createObjectURL(file)
    setPreview(objectUrl)

    // Upload the file
    setIsUploading(true)
    setError(null)

    try {
      const imageUrl = await uploadImage(file, bucket, folder)
      onChange(imageUrl)
      setIsUploading(false)
    } catch (err) {
      console.error("Upload error:", err)
      setError("Failed to upload image. Please try again.")
      setIsUploading(false)
      // Clean up the preview
      URL.revokeObjectURL(objectUrl)
      setPreview(null)
    }
  }

  const handleRemove = () => {
    if (preview) {
      URL.revokeObjectURL(preview)
      setPreview(null)
    }
    
    if (onRemove) {
      onRemove()
    } else {
      onChange("")
    }
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <Input
        type="file"
        ref={fileInputRef}
        accept={accept}
        onChange={handleFileChange}
        disabled={disabled || isUploading}
        className="hidden"
      />

      {error && (
        <div className="bg-red-50 text-red-600 p-2 rounded-md text-sm flex items-center">
          <X className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}

      {value || preview ? (
        <div className="relative rounded-md overflow-hidden border border-slate-200">
          <div
            className="relative"
            style={{
              aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
              width: width,
              height: aspectRatio ? undefined : height,
            }}
          >
            <Image
              src={preview || value || ""}
              alt="Uploaded image"
              fill
              className="object-cover"
            />
          </div>
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={handleRemove}
            disabled={disabled || isUploading}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Remove image</span>
          </Button>
        </div>
      ) : (
        <div
          onClick={handleClick}
          className={cn(
            "border-2 border-dashed border-slate-200 rounded-md flex flex-col items-center justify-center cursor-pointer p-4 transition-colors hover:border-slate-300",
            disabled && "opacity-50 cursor-not-allowed",
            isUploading && "opacity-70 cursor-wait"
          )}
          style={{
            aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
            width: width,
            height: aspectRatio ? undefined : height,
          }}
        >
          {isUploading ? (
            <div className="flex flex-col items-center justify-center text-slate-500">
              <Loader2 className="h-10 w-10 animate-spin mb-2" />
              <p className="text-sm">Uploading...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-slate-500">
              <ImageIcon className="h-10 w-10 mb-2" />
              <p className="text-sm font-medium mb-1">Click to upload image</p>
              <p className="text-xs text-slate-400">
                {accept.replace("image/", "").replace("*", "any image")} up to {maxSizeMB}MB
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

interface MultipleImageUploadProps extends Omit<ImageUploadProps, "value" | "onChange" | "onRemove"> {
  value: string[]
  onChange: (value: string[]) => void
  onRemove?: (index: number) => void
  maxImages?: number
}

export function MultipleImageUpload({
  value = [],
  onChange,
  onRemove,
  disabled = false,
  className,
  bucket = "product-images",
  folder,
  maxSizeMB = 5,
  accept = "image/*",
  aspectRatio,
  width = 150,
  height = 150,
  maxImages = 5,
}: MultipleImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    // Check if adding these files would exceed the maximum
    if (value.length + files.length > maxImages) {
      setError(`You can upload a maximum of ${maxImages} images`)
      return
    }

    // Validate each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      // Validate file size
      const maxSizeBytes = maxSizeMB * 1024 * 1024
      if (file.size > maxSizeBytes) {
        setError(`File "${file.name}" exceeds ${maxSizeMB}MB limit`)
        return
      }

      // Validate file type
      if (!file.type.startsWith("image/")) {
        setError(`File "${file.name}" is not an image`)
        return
      }
    }

    // Upload each file
    setIsUploading(true)
    setError(null)

    try {
      const newUrls: string[] = []
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const imageUrl = await uploadImage(file, bucket, folder)
        newUrls.push(imageUrl)
      }
      
      onChange([...value, ...newUrls])
      setIsUploading(false)
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (err) {
      console.error("Upload error:", err)
      setError("Failed to upload one or more images. Please try again.")
      setIsUploading(false)
    }
  }

  const handleRemove = (index: number) => {
    const newValue = [...value]
    newValue.splice(index, 1)
    
    if (onRemove) {
      onRemove(index)
    } else {
      onChange(newValue)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <Input
        type="file"
        ref={fileInputRef}
        accept={accept}
        onChange={handleFileChange}
        disabled={disabled || isUploading || value.length >= maxImages}
        className="hidden"
        multiple
      />

      {error && (
        <div className="bg-red-50 text-red-600 p-2 rounded-md text-sm flex items-center">
          <X className="h-4 w-4 mr-2" />
          {error}
        </div>
      )}

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {value.map((url, index) => (
          <div key={index} className="relative rounded-md overflow-hidden border border-slate-200">
            <div
              className="relative"
              style={{
                aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
                width: width,
                height: aspectRatio ? undefined : height,
              }}
            >
              <Image
                src={url}
                alt={`Uploaded image ${index + 1}`}
                fill
                className="object-cover"
              />
            </div>
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-6 w-6 rounded-full"
              onClick={() => handleRemove(index)}
              disabled={disabled || isUploading}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove image</span>
            </Button>
          </div>
        ))}

        {value.length < maxImages && (
          <div
            onClick={handleClick}
            className={cn(
              "border-2 border-dashed border-slate-200 rounded-md flex flex-col items-center justify-center cursor-pointer p-4 transition-colors hover:border-slate-300",
              disabled && "opacity-50 cursor-not-allowed",
              isUploading && "opacity-70 cursor-wait"
            )}
            style={{
              aspectRatio: aspectRatio ? String(aspectRatio) : undefined,
              width: width,
              height: aspectRatio ? undefined : height,
            }}
          >
            {isUploading ? (
              <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
            ) : (
              <Upload className="h-6 w-6 text-slate-500" />
            )}
            <span className="sr-only">Add image</span>
          </div>
        )}
      </div>
      
      <p className="text-xs text-slate-500">
        {value.length} of {maxImages} images
      </p>
    </div>
  )
}
