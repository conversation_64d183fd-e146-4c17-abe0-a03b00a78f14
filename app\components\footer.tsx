import Link from "next/link"
import { Facebook, Instagram, Twitter, Youtube } from "lucide-react"

export default function Footer() {
  return (
    <footer className="border-t bg-slate-50">
      <div className="container px-4 py-12 md:px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="text-xl font-bold text-teal-600">PASSDOWN</div>
            <p className="text-sm text-slate-500">Your trusted marketplace for quality tech components.</p>
            <div className="flex gap-4">
              <Link href="#" className="text-slate-500 hover:text-teal-600">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-slate-500 hover:text-teal-600">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-slate-500 hover:text-teal-600">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-slate-500 hover:text-teal-600">
                <Youtube className="h-5 w-5" />
                <span className="sr-only">YouTube</span>
              </Link>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Shop Categories</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/category/graphics-cards" className="text-slate-500 hover:text-teal-600">
                  Graphics Cards
                </Link>
              </li>
              <li>
                <Link href="/category/processors" className="text-slate-500 hover:text-teal-600">
                  Processors
                </Link>
              </li>
              <li>
                <Link href="/category/laptops" className="text-slate-500 hover:text-teal-600">
                  Laptops
                </Link>
              </li>
              <li>
                <Link href="/category/motherboards" className="text-slate-500 hover:text-teal-600">
                  Motherboards
                </Link>
              </li>
              <li>
                <Link href="/category/ram" className="text-slate-500 hover:text-teal-600">
                  RAM
                </Link>
              </li>
              <li>
                <Link href="/category/storage" className="text-slate-500 hover:text-teal-600">
                  Storage
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Account</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/account" className="text-slate-500 hover:text-teal-600">
                  My Account
                </Link>
              </li>
              <li>
                <Link href="/account/orders" className="text-slate-500 hover:text-teal-600">
                  Orders
                </Link>
              </li>
              <li>
                <Link href="/wishlist" className="text-slate-500 hover:text-teal-600">
                  Wishlist
                </Link>
              </li>
              <li>
                <Link href="/compare" className="text-slate-500 hover:text-teal-600">
                  Compare Products
                </Link>
              </li>
              <li>
                <Link href="/seller/dashboard" className="text-slate-500 hover:text-teal-600">
                  Seller Dashboard
                </Link>
              </li>
              <li>
                <Link href="/account/listings" className="text-slate-500 hover:text-teal-600">
                  My Listings
                </Link>
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Help</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/contact" className="text-slate-500 hover:text-teal-600">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="text-slate-500 hover:text-teal-600">
                  Shipping Policy
                </Link>
              </li>
              <li>
                <Link href="/returns" className="text-slate-500 hover:text-teal-600">
                  Returns & Refunds
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-slate-500 hover:text-teal-600">
                  FAQs
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-slate-500 hover:text-teal-600">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-slate-500 hover:text-teal-600">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 pt-8 border-t text-center text-sm text-slate-500">
          <p>© {new Date().getFullYear()} PASSDOWN. All rights reserved.</p>
          <p className="mt-2">Payment methods: UPI, Credit Card, Debit Card, Net Banking, Wallet</p>
        </div>
      </div>
    </footer>
  )
}

