import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowRight, Shield, Truck, RotateCcw, Star } from "lucide-react"
import FeaturedCategories from "./components/featured-categories"
import ProductCard from "./components/product-card"
import { getProducts as getSupabaseProducts } from "@/lib/db-utils"
import { featuredExpandedProducts as mockFeaturedProducts } from "./data/expanded-products"
import type { Product } from "./types"

async function getProducts(): Promise<Product[]> {
  try {
    // Try to get products from Supabase
    const { products } = await getSupabaseProducts({
      limit: 4,
      sortBy: 'created_at',
      sortOrder: 'desc',
    });

    if (products.length === 0) {
      console.log("No products found in database, using mock data");
      return mockFeaturedProducts;
    }

    return products;
  } catch (error) {
    console.error("Error fetching products:", error);
    console.log("Using mock data due to database error");
    return mockFeaturedProducts;
  }
}

export default async function Home() {
  const products = await getProducts();
  return (
    <div className="min-h-screen">
      <main>
        {/* Hero Section */}
        <section className="relative py-20 md:py-28 overflow-hidden bg-gradient-to-b from-slate-900 to-slate-800 text-white">
          <div className="absolute inset-0 bg-[url('/placeholder.svg?height=1080&width=1920')] bg-cover bg-center opacity-10"></div>
          <div className="container mx-auto px-4 md:px-6 relative z-10">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-teal-500/20 px-3 py-1 text-sm text-teal-300">
                  Tech That Lasts
                </div>
                <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
                  Buy & Sell <span className="text-teal-400">Responsibly</span>
                </h1>
                <p className="max-w-[600px] text-slate-300 md:text-xl">
                  PASSDOWN is your trusted marketplace for quality second-hand and new items at affordable
                  prices. From tech to fashion, home goods to collectibles.
                </p>
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button size="lg" className="bg-teal-500 hover:bg-teal-600 text-white">
                    Shop Now
                  </Button>
                  <Button size="lg" variant="outline" className="border-teal-500 text-teal-300 hover:bg-teal-500/10">
                    Sell Your Tech
                  </Button>
                </div>
              </div>
              <div className="relative lg:ml-10">
                <div className="absolute -top-12 -left-12 h-64 w-64 rounded-full bg-teal-500/20 blur-3xl opacity-70"></div>
                <Image
                  src="/placeholder.svg?height=600&width=600"
                  width={600}
                  height={600}
                  alt="Computer components and hardware"
                  className="mx-auto aspect-square rounded-xl object-cover relative z-10"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Featured Categories */}
        <FeaturedCategories />

        {/* Featured Products */}
        <section className="py-12 md:py-24">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Featured Products</h2>
                <p className="max-w-[700px] text-slate-500 md:text-xl">
                  Discover our handpicked selection of quality tech components
                </p>
              </div>
            </div>

            {/* Featured Product Highlight */}
            {products.length > 0 && (
              <div className="mt-12 mb-8">
                <ProductCard
                  product={products[0]}
                  variant="featured"
                />
              </div>
            )}

            {/* More Featured Products */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
              {products.slice(1).map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            <div className="mt-12 text-center">
              <Link href="/products">
                <Button variant="outline" size="lg" className="gap-2">
                  View All Products <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Why Choose Us */}
        <section className="py-12 md:py-24 bg-slate-50">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Why Choose PASSDOWN</h2>
                <p className="max-w-[700px] text-slate-500 md:text-xl">
                  We ensure quality, reliability, and excellent service
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              {[
                {
                  title: "Quality Assurance",
                  description: "All products are thoroughly tested and verified before listing.",
                  icon: <Shield className="h-10 w-10 text-teal-600" />,
                },
                {
                  title: "Fast Delivery",
                  description: "Quick and secure shipping across India with real-time tracking.",
                  icon: <Truck className="h-10 w-10 text-teal-600" />,
                },
                {
                  title: "Easy Returns",
                  description: "Hassle-free 7-day return policy if you're not satisfied.",
                  icon: <RotateCcw className="h-10 w-10 text-teal-600" />,
                },
              ].map((feature, index) => (
                <div key={index} className="flex flex-col items-center text-center p-6 rounded-xl bg-white shadow-sm">
                  <div className="mb-4 rounded-full bg-teal-50 p-3">{feature.icon}</div>
                  <h3 className="text-xl font-bold">{feature.title}</h3>
                  <p className="mt-2 text-slate-500">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Recent Reviews */}
        <section className="py-12 md:py-24">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Customer Reviews</h2>
                <p className="max-w-[700px] text-slate-500 md:text-xl">
                  See what our customers have to say about their experience
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              {[
                {
                  quote:
                    "Found a great deal on a graphics card that was almost new. The verification process gave me confidence in my purchase.",
                  author: "Rahul M.",
                  location: "Bangalore",
                },
                {
                  quote:
                    "Selling my old laptop components was so easy. Got a fair price and the transaction was smooth.",
                  author: "Priya S.",
                  location: "Mumbai",
                },
                {
                  quote: "The RAM I bought works perfectly with my system. Saved almost 40% compared to buying new.",
                  author: "Vikram J.",
                  location: "Delhi",
                },
              ].map((review, index) => (
                <div key={index} className="flex flex-col p-6 rounded-xl bg-white shadow-sm">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-current text-yellow-400" />
                    ))}
                  </div>
                  <p className="flex-1 text-slate-600 italic">"{review.quote}"</p>
                  <div className="mt-4 pt-4 border-t">
                    <p className="font-medium">{review.author}</p>
                    <p className="text-sm text-slate-500">{review.location}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA */}
        <section className="py-12 md:py-24 bg-teal-600 text-white">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Ready to Buy or Sell?</h2>
                <p className="max-w-[700px] text-teal-50 md:text-xl">
                  Join thousands of tech enthusiasts on PASSDOWN today
                </p>
              </div>
              <div className="w-full max-w-sm space-y-2">
                <form className="flex flex-col sm:flex-row gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                  />
                  <Button className="bg-white text-teal-600 hover:bg-teal-50">Get Started</Button>
                </form>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}


