import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import prisma from '@/lib/db';
import { signToken, setTokenCookie } from '@/lib/auth';
import { z } from 'zod';

// Validation schema for login
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

export async function POST(req: NextRequest) {
  try {
    console.log("Login API route called");

    // Parse request body
    let body;
    try {
      body = await req.json();
    } catch (parseError) {
      console.error("Failed to parse request body:", parseError);
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    // Validate request body
    const result = loginSchema.safeParse(body);
    if (!result.success) {
      console.log("Login validation failed:", result.error.format());
      return NextResponse.json(
        { error: 'Validation failed', details: result.error.format() },
        { status: 400 }
      );
    }

    const { email, password } = result.data;
    console.log("Login attempt for email:", email);

    // Find user by email
    const user = await prisma.user.findFirst({
      where: {
        email: email.toLowerCase()
      },
      include: {
        sellerProfile: true,
      },
    });

    if (!user) {
      console.log("User not found:", email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Compare passwords
    let passwordMatch = false;
    try {
      passwordMatch = await bcrypt.compare(password, user.password);
    } catch (bcryptError) {
      console.error("Error comparing passwords:", bcryptError);
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      );
    }

    if (!passwordMatch) {
      console.log("Password mismatch for user:", email);
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    console.log("Login successful for user:", email, "with role:", user.role);

    // Check if the user role is valid
    if (!['buyer', 'seller', 'admin'].includes(user.role)) {
      console.error("Invalid user role:", user.role);
      return NextResponse.json(
        { error: 'Invalid user account type' },
        { status: 500 }
      );
    }

    // Generate JWT token
    try {
      const token = await signToken({
        id: user.id,
        email: user.email,
        role: user.role as 'buyer' | 'seller' | 'admin',
      });

      // Set token in cookie
      await setTokenCookie(token);
    } catch (tokenError) {
      console.error("Error generating or setting token:", tokenError);
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      );
    }

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;

    // Create a response with the user data
    const response = NextResponse.json({
      message: 'Login successful',
      user: userWithoutPassword,
    });

    // Add a debug header to check if cookies are being set
    response.headers.set('X-Auth-Debug', 'Cookies-Set');

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
