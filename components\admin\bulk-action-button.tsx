"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Loader2, Check, X, ChevronDown } from "lucide-react"
import { toast } from "sonner"

interface BulkActionButtonProps {
  selectedIds: string[]
  entityType: "products" | "users" | "orders"
  onActionComplete?: () => void
  disabled?: boolean
}

export function BulkActionButton({
  selectedIds,
  entityType,
  onActionComplete,
  disabled = false,
}: BulkActionButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [currentAction, setCurrentAction] = useState<string | null>(null)

  // Define available actions based on entity type
  const getActions = () => {
    switch (entityType) {
      case "products":
        return [
          { id: "feature", label: "Feature Products", destructive: false },
          { id: "unfeature", label: "Unfeature Products", destructive: false },
          { id: "activate", label: "Activate Products", destructive: false },
          { id: "deactivate", label: "Deactivate Products", destructive: false },
          { id: "delete", label: "Delete Products", destructive: true },
        ]
      case "users":
        return [
          { id: "activate", label: "Activate Users", destructive: false },
          { id: "suspend", label: "Suspend Users", destructive: true },
          { id: "verify", label: "Verify Sellers", destructive: false },
          { id: "delete", label: "Delete Users", destructive: true },
        ]
      case "orders":
        return [
          { id: "process", label: "Mark as Processing", destructive: false },
          { id: "ship", label: "Mark as Shipped", destructive: false },
          { id: "deliver", label: "Mark as Delivered", destructive: false },
          { id: "cancel", label: "Cancel Orders", destructive: true },
        ]
      default:
        return []
    }
  }

  const actions = getActions()

  const handleActionClick = (actionId: string) => {
    setCurrentAction(actionId)
    
    // Show confirmation dialog for destructive actions
    const action = actions.find(a => a.id === actionId)
    if (action?.destructive) {
      setShowConfirmDialog(true)
    } else {
      executeAction(actionId)
    }
  }

  const executeAction = async (actionId: string) => {
    if (selectedIds.length === 0) {
      toast.error("No items selected")
      return
    }

    setIsLoading(true)

    try {
      // Prepare request body based on entity type
      let requestBody = {}
      
      switch (entityType) {
        case "products":
          requestBody = {
            action: actionId,
            productIds: selectedIds,
          }
          break
        case "users":
          requestBody = {
            action: actionId,
            userIds: selectedIds,
          }
          break
        case "orders":
          requestBody = {
            action: actionId,
            orderIds: selectedIds,
            notifyCustomers: true,
          }
          break
      }

      // Send request to API
      const response = await fetch("/api/admin/bulk-actions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Failed to perform bulk action")
      }

      // Show success message
      toast.success(result.message || `Bulk action completed successfully (${result.count} items)`)
      
      // Call the onActionComplete callback if provided
      if (onActionComplete) {
        onActionComplete()
      }
    } catch (error) {
      console.error("Bulk action error:", error)
      toast.error(error instanceof Error ? error.message : "An error occurred")
    } finally {
      setIsLoading(false)
      setShowConfirmDialog(false)
      setCurrentAction(null)
    }
  }

  // Get the current action details
  const currentActionDetails = actions.find(a => a.id === currentAction)

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            disabled={disabled || selectedIds.length === 0 || isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Check className="h-4 w-4 mr-1" />
            )}
            Bulk Actions
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>
            {selectedIds.length} {entityType.slice(0, -1)}
            {selectedIds.length !== 1 ? "s" : ""} selected
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {actions.map((action) => (
            <DropdownMenuItem
              key={action.id}
              onClick={() => handleActionClick(action.id)}
              className={action.destructive ? "text-red-600 focus:text-red-600" : ""}
            >
              {action.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {currentActionDetails?.label}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {currentActionDetails?.label.toLowerCase()} the selected {selectedIds.length} {entityType.slice(0, -1)}
              {selectedIds.length !== 1 ? "s" : ""}? This action {currentActionDetails?.destructive ? "cannot" : "can"} be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault()
                if (currentAction) {
                  executeAction(currentAction)
                }
              }}
              disabled={isLoading}
              className={currentActionDetails?.destructive ? "bg-red-600 hover:bg-red-700" : ""}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>Confirm</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
