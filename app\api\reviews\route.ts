import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import prisma from '@/lib/db'
import { getAuthUser } from '@/lib/auth'

const createReviewSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  content: z.string().min(10, 'Review must be at least 10 characters').max(1000, 'Review must be less than 1000 characters'),
})

const getReviewsSchema = z.object({
  productId: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
  sortBy: z.enum(['newest', 'oldest', 'highest', 'lowest', 'helpful']).optional(),
  rating: z.string().optional(),
})

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const authUser = await getAuthUser()
    if (!authUser) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await req.json()
    const result = createReviewSchema.safeParse(body)
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: result.error.format() },
        { status: 400 }
      )
    }

    const { productId, rating, title, content } = result.data

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check if user already reviewed this product
    const existingReview = await prisma.review.findFirst({
      where: {
        productId,
        userId: authUser.id,
      },
    })

    if (existingReview) {
      return NextResponse.json(
        { error: 'You have already reviewed this product' },
        { status: 409 }
      )
    }

    // Check if user has purchased this product (optional verification)
    const hasPurchased = await prisma.orderItem.findFirst({
      where: {
        productId,
        order: {
          userId: authUser.id,
          status: 'delivered',
        },
      },
    })

    // Create the review
    const review = await prisma.review.create({
      data: {
        productId,
        userId: authUser.id,
        rating,
        title,
        content,
        status: 'pending', // Reviews need moderation
        isVerifiedPurchase: !!hasPurchased,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
    })

    return NextResponse.json({
      message: 'Review submitted successfully',
      review,
    })

  } catch (error) {
    console.error('Review creation error:', error)
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const query = Object.fromEntries(searchParams.entries())
    
    const result = getReviewsSchema.safeParse(query)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: result.error.format() },
        { status: 400 }
      )
    }

    const {
      productId,
      page = '1',
      limit = '10',
      sortBy = 'newest',
      rating,
    } = result.data

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const skip = (pageNum - 1) * limitNum

    // Build where clause
    const where: any = {
      status: 'approved', // Only show approved reviews
    }

    if (productId) {
      where.productId = productId
    }

    if (rating) {
      where.rating = parseInt(rating)
    }

    // Build order clause
    let orderBy: any = { createdAt: 'desc' }
    
    switch (sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' }
        break
      case 'highest':
        orderBy = { rating: 'desc' }
        break
      case 'lowest':
        orderBy = { rating: 'asc' }
        break
      case 'helpful':
        orderBy = { helpful: 'desc' }
        break
      default:
        orderBy = { createdAt: 'desc' }
    }

    // Get reviews with pagination
    const [reviews, totalCount] = await Promise.all([
      prisma.review.findMany({
        where,
        orderBy,
        skip,
        take: limitNum,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      }),
      prisma.review.count({ where }),
    ])

    // Get rating statistics if productId is provided
    let ratingStats = null
    if (productId) {
      const ratingCounts = await prisma.review.groupBy({
        by: ['rating'],
        where: {
          productId,
          status: 'approved',
        },
        _count: {
          rating: true,
        },
      })

      const totalReviews = await prisma.review.count({
        where: {
          productId,
          status: 'approved',
        },
      })

      const averageRating = await prisma.review.aggregate({
        where: {
          productId,
          status: 'approved',
        },
        _avg: {
          rating: true,
        },
      })

      const ratingDistribution = ratingCounts.reduce((acc, item) => {
        acc[item.rating] = item._count.rating
        return acc
      }, {} as { [key: number]: number })

      // Fill in missing ratings with 0
      for (let i = 1; i <= 5; i++) {
        if (!ratingDistribution[i]) {
          ratingDistribution[i] = 0
        }
      }

      ratingStats = {
        totalReviews,
        averageRating: averageRating._avg.rating || 0,
        ratingDistribution,
      }
    }

    return NextResponse.json({
      reviews,
      totalCount,
      currentPage: pageNum,
      totalPages: Math.ceil(totalCount / limitNum),
      hasMore: pageNum * limitNum < totalCount,
      ratingStats,
    })

  } catch (error) {
    console.error('Reviews fetch error:', error)
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    )
  }
}
