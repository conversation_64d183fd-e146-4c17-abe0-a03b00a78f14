"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { 
  Package, 
  Search, 
  Filter, 
  ChevronDown, 
  Clock, 
  CheckCircle2, 
  Truck, 
  XCircle,
  AlertCircle,
  Eye,
  Download,
  MoreHorizontal
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Pagination,
  PaginationContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pa<PERSON>ationLink,
  <PERSON><PERSON>ation<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Previous,
} from "@/components/ui/pagination"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"

// Mock order data
const mockOrders = [
  {
    id: "ORD-2023-1001",
    date: "2023-05-15",
    total: 24999,
    status: "delivered",
    items: [
      { id: "1", name: "AMD Ryzen 7 5800X", price: 24999, quantity: 1 }
    ],
    tracking: "IND123456789",
    paymentMethod: "Credit Card",
    deliveryAddress: "123 Main St, Bangalore, Karnataka, 560001",
  },
  {
    id: "ORD-2023-1002",
    date: "2023-05-20",
    total: 15499,
    status: "shipped",
    items: [
      { id: "2", name: "Corsair Vengeance RGB Pro 32GB", price: 15499, quantity: 1 }
    ],
    tracking: "IND987654321",
    paymentMethod: "UPI",
    deliveryAddress: "456 Park Ave, Mumbai, Maharashtra, 400001",
  },
  {
    id: "ORD-2023-1003",
    date: "2023-05-25",
    total: 8999,
    status: "processing",
    items: [
      { id: "3", name: "Samsung 970 EVO Plus 1TB SSD", price: 8999, quantity: 1 }
    ],
    tracking: null,
    paymentMethod: "Net Banking",
    deliveryAddress: "789 Lake View, Delhi, Delhi, 110001",
  },
  {
    id: "ORD-2023-1004",
    date: "2023-05-28",
    total: 4599,
    status: "cancelled",
    items: [
      { id: "4", name: "Logitech G502 HERO Gaming Mouse", price: 4599, quantity: 1 }
    ],
    tracking: null,
    paymentMethod: "Credit Card",
    deliveryAddress: "101 Hill Road, Chennai, Tamil Nadu, 600001",
  },
  {
    id: "ORD-2023-1005",
    date: "2023-06-01",
    total: 32999,
    status: "delivered",
    items: [
      { id: "5", name: "NVIDIA GeForce RTX 3060", price: 32999, quantity: 1 }
    ],
    tracking: "IND567891234",
    paymentMethod: "UPI",
    deliveryAddress: "202 River View, Kolkata, West Bengal, 700001",
  },
  {
    id: "ORD-2023-1006",
    date: "2023-06-05",
    total: 12499,
    status: "processing",
    items: [
      { id: "6", name: "ASUS ROG Strix B550-F Gaming Motherboard", price: 12499, quantity: 1 }
    ],
    tracking: null,
    paymentMethod: "Net Banking",
    deliveryAddress: "303 Mountain Road, Hyderabad, Telangana, 500001",
  },
];

// Status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  const statusConfig = {
    processing: { color: "bg-blue-500", icon: <Clock className="h-3 w-3 mr-1" /> },
    shipped: { color: "bg-amber-500", icon: <Truck className="h-3 w-3 mr-1" /> },
    delivered: { color: "bg-green-500", icon: <CheckCircle2 className="h-3 w-3 mr-1" /> },
    cancelled: { color: "bg-red-500", icon: <XCircle className="h-3 w-3 mr-1" /> },
    refunded: { color: "bg-purple-500", icon: <AlertCircle className="h-3 w-3 mr-1" /> },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.processing;

  return (
    <Badge className={`${config.color} hover:${config.color}`}>
      {config.icon}
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

export default function OrderManagement() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Filter orders based on search query and status filter
  const filteredOrders = mockOrders.filter((order) => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.items.some(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleOrderClick = (order: any) => {
    setSelectedOrder(order);
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-teal-600" />
          <h1 className="text-2xl font-bold">Order Management</h1>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-500" />
            <Input
              type="search"
              placeholder="Search orders..."
              className="pl-8 w-full sm:w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="refunded">Refunded</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="processing">Processing</TabsTrigger>
          <TabsTrigger value="shipped">Shipped</TabsTrigger>
          <TabsTrigger value="delivered">Delivered</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-6">
          <Card>
            <CardHeader className="px-6 py-4">
              <CardTitle className="text-base font-medium">Order History</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.length > 0 ? (
                    filteredOrders.map((order) => (
                      <TableRow key={order.id} className="cursor-pointer hover:bg-slate-50" onClick={() => handleOrderClick(order)}>
                        <TableCell className="font-medium">{order.id}</TableCell>
                        <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                        <TableCell>₹{order.total.toLocaleString("en-IN")}</TableCell>
                        <TableCell>
                          <OrderStatusBadge status={order.status} />
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                handleOrderClick(order);
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="h-4 w-4 mr-2" />
                                Download Invoice
                              </DropdownMenuItem>
                              {order.status === "processing" && (
                                <DropdownMenuItem className="text-red-600">
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Cancel Order
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-slate-500">
                        No orders found matching your criteria
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
          
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious href="#" />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" isActive>1</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
              <PaginationItem>
                <PaginationNext href="#" />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </TabsContent>
        
        {/* Other tab contents would be similar but filtered by status */}
        <TabsContent value="processing" className="mt-6">
          {/* Similar content but filtered for processing orders */}
        </TabsContent>
      </Tabs>
      
      {/* Order Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Order Details</DialogTitle>
            <DialogDescription>
              {selectedOrder?.id} - Placed on {selectedOrder && new Date(selectedOrder.date).toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-slate-500 mb-1">Order Status</h3>
                  <OrderStatusBadge status={selectedOrder.status} />
                  
                  {selectedOrder.tracking && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-slate-500 mb-1">Tracking Number</h3>
                      <p className="text-sm">{selectedOrder.tracking}</p>
                    </div>
                  )}
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-slate-500 mb-1">Shipping Address</h3>
                  <p className="text-sm">{selectedOrder.deliveryAddress}</p>
                  
                  <div className="mt-4">
                    <h3 className="text-sm font-medium text-slate-500 mb-1">Payment Method</h3>
                    <p className="text-sm">{selectedOrder.paymentMethod}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-slate-500 mb-2">Order Items</h3>
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead className="text-right">Price</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedOrder.items.map((item: any) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.name}</TableCell>
                          <TableCell className="text-right">₹{item.price.toLocaleString("en-IN")}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">₹{(item.price * item.quantity).toLocaleString("en-IN")}</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Total</TableCell>
                        <TableCell className="text-right font-bold">₹{selectedOrder.total.toLocaleString("en-IN")}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>Close</Button>
            <Button>Download Invoice</Button>
            {selectedOrder?.status === "processing" && (
              <Button variant="destructive">Cancel Order</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
