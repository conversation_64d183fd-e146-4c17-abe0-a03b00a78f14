import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Check if environment variables are set
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  console.warn('NEXT_PUBLIC_SUPABASE_URL is not defined in environment variables');
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn('NEXT_PUBLIC_SUPABASE_ANON_KEY is not defined in environment variables');
}

// Create a single supabase client for interacting with your database
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'passdown-auth',
  },
});

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  const configured = Boolean(supabaseUrl && supabaseAnonKey);
  if (!configured) {
    console.log('Supabase is not configured, falling back to local database');
  }
  return configured;
};

// Helper function to test Supabase connection
export const testSupabaseConnection = async (): Promise<boolean> => {
  try {
    if (!isSupabaseConfigured()) {
      return false;
    }

    // Try to fetch a record from the health_check table
    const { data, error } = await supabase
      .from('health_check')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Supabase connection test failed:', error);
      return false;
    }

    console.log('Supabase connection test successful');
    return true;
  } catch (error) {
    console.error('Supabase connection test error:', error);
    return false;
  }
};

// Helper functions for database operations

// Users
export const getUser = async (id: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*, seller_profiles(*)')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data;
};

export const getUserByEmail = async (email: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*, seller_profiles(*)')
    .eq('email', email)
    .single();

  if (error && error.code !== 'PGSQL_ERROR_NO_DATA') throw error;
  return data;
};

export const createUser = async (userData: any) => {
  const { data, error } = await supabase
    .from('users')
    .insert([userData])
    .select()
    .single();

  if (error) throw error;
  return data;
};

// Products
export const getProducts = async (options: any = {}) => {
  const {
    page = 1,
    limit = 12,
    category,
    search,
    minPrice,
    maxPrice,
    condition,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = options;

  let query = supabase
    .from('products')
    .select(`
      *,
      categories(*),
      users!seller_id(*),
      product_images(*)
    `);

  // Apply filters
  if (category) {
    query = query.eq('categories.slug', category);
  }

  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
  }

  if (minPrice) {
    query = query.gte('price', minPrice);
  }

  if (maxPrice) {
    query = query.lte('price', maxPrice);
  }

  if (condition) {
    query = query.eq('condition', condition);
  }

  // Apply pagination
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Execute query with pagination
  const { data, error, count } = await query
    .range(from, to)
    .limit(limit);

  if (error) throw error;

  return {
    products: data,
    totalCount: count || 0,
  };
};

export const getProductById = async (id: string) => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      categories(*),
      users!seller_id(*),
      product_images(*)
    `)
    .eq('id', id)
    .single();

  if (error) throw error;
  return data;
};

export const createProduct = async (productData: any) => {
  const { data, error } = await supabase
    .from('products')
    .insert([productData])
    .select()
    .single();

  if (error) throw error;
  return data;
};

// Categories
export const getCategories = async (parentSlug?: string) => {
  let query = supabase.from('categories').select('*, parent:parent_id(*)');

  if (parentSlug) {
    // Get parent category first
    const { data: parentCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('slug', parentSlug)
      .single();

    if (parentCategory) {
      query = query.eq('parent_id', parentCategory.id);
    }
  } else {
    // Get top-level categories (no parent)
    query = query.is('parent_id', null);
  }

  const { data, error } = await query;

  if (error) throw error;
  return data;
};
