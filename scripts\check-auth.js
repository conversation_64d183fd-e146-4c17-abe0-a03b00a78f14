// Authentication System Check Script
// Run with: node scripts/check-auth.js

require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const fetch = require('node-fetch');

const API_URL = 'http://localhost:3000/api';
let serverProcess = null;

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper function to print colored messages
function print(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Helper function to print section headers
function printSection(title) {
  console.log('\n');
  console.log(`${colors.bright}${colors.cyan}=== ${title} ===${colors.reset}`);
  console.log('');
}

// Helper function to print success/failure messages
function printResult(test, success, message) {
  const icon = success ? '✅' : '❌';
  const color = success ? 'green' : 'red';
  console.log(`${colors[color]}${icon} ${test}${colors.reset}: ${message}`);
}

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', body = null, cookies = null) {
  const headers = {
    'Content-Type': 'application/json',
  };

  if (cookies) {
    headers['Cookie'] = cookies;
  }

  const options = {
    method,
    headers,
    credentials: 'include',
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${API_URL}${endpoint}`, options);
    
    // Get response data
    const data = await response.json();
    
    return {
      status: response.status,
      data,
      headers: Object.fromEntries(response.headers.entries()),
      cookies: response.headers.get('set-cookie'),
    };
  } catch (error) {
    console.error('Request error:', error);
    return {
      status: 500,
      data: { error: error.message },
    };
  }
}

// Check if the JWT_SECRET is properly set
function checkJwtSecret() {
  printSection('Checking JWT Secret');
  
  const jwtSecret = process.env.JWT_SECRET;
  
  if (!jwtSecret) {
    printResult('JWT_SECRET', false, 'Not set in .env.local file');
    return false;
  }
  
  if (jwtSecret.length < 32) {
    printResult('JWT_SECRET', false, `Too short (${jwtSecret.length} chars). Should be at least 32 characters.`);
    return false;
  }
  
  if (jwtSecret === 'your-secret-key-at-least-32-characters-long-for-security') {
    printResult('JWT_SECRET', false, 'Using default value. Should be changed for production.');
    return true; // Still works for testing
  }
  
  printResult('JWT_SECRET', true, 'Properly configured');
  return true;
}

// Check if the auth.ts file exists and has the required functions
function checkAuthLibrary() {
  printSection('Checking Authentication Library');
  
  const authPath = path.join(process.cwd(), 'lib', 'auth.ts');
  
  if (!fs.existsSync(authPath)) {
    printResult('auth.ts', false, 'File not found');
    return false;
  }
  
  const authContent = fs.readFileSync(authPath, 'utf8');
  
  const requiredFunctions = [
    'signToken',
    'verifyToken',
    'setTokenCookie',
    'getTokenCookie',
    'deleteTokenCookie',
    'getAuthUser',
    'isAuthenticated',
    'hasRole',
    'authMiddleware',
  ];
  
  const missingFunctions = [];
  
  for (const func of requiredFunctions) {
    if (!authContent.includes(`export async function ${func}`)) {
      missingFunctions.push(func);
    }
  }
  
  if (missingFunctions.length > 0) {
    printResult('auth.ts', false, `Missing required functions: ${missingFunctions.join(', ')}`);
    return false;
  }
  
  printResult('auth.ts', true, 'Contains all required functions');
  return true;
}

// Check if the auth API routes exist
function checkAuthRoutes() {
  printSection('Checking Authentication API Routes');
  
  const routesPath = path.join(process.cwd(), 'app', 'api', 'auth');
  
  if (!fs.existsSync(routesPath)) {
    printResult('API Routes', false, 'auth directory not found');
    return false;
  }
  
  const requiredRoutes = ['login', 'logout', 'me', 'register'];
  const missingRoutes = [];
  
  for (const route of requiredRoutes) {
    const routePath = path.join(routesPath, route, 'route.ts');
    if (!fs.existsSync(routePath)) {
      missingRoutes.push(route);
    }
  }
  
  if (missingRoutes.length > 0) {
    printResult('API Routes', false, `Missing required routes: ${missingRoutes.join(', ')}`);
    return false;
  }
  
  printResult('API Routes', true, 'All required routes exist');
  return true;
}

// Check if the database has test users
async function checkTestUsers() {
  printSection('Checking Test Users in Database');
  
  try {
    // Check if Prisma client is generated
    const prismaClientPath = path.join(process.cwd(), 'node_modules', '.prisma', 'client');
    if (!fs.existsSync(prismaClientPath)) {
      printResult('Prisma Client', false, 'Not generated. Run npx prisma generate');
      return false;
    }
    
    // Use Prisma directly to check for users
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    try {
      // Check for admin user
      const admin = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      
      printResult('Admin User', !!admin, admin ? 'Found in database' : 'Not found in database');
      
      // Check for seller user
      const seller = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      
      printResult('Seller User', !!seller, seller ? 'Found in database' : 'Not found in database');
      
      // Check for buyer user
      const buyer = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      
      printResult('Buyer User', !!buyer, buyer ? 'Found in database' : 'Not found in database');
      
      await prisma.$disconnect();
      
      return !!(admin && seller && buyer);
    } catch (error) {
      console.error('Database query error:', error);
      printResult('Database Query', false, `Error: ${error.message}`);
      await prisma.$disconnect();
      return false;
    }
  } catch (error) {
    console.error('Prisma import error:', error);
    printResult('Prisma Import', false, `Error: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  print('PASSDOWN Authentication System Check', 'bright');
  print('This script checks your authentication system configuration and helps diagnose issues.', 'dim');
  console.log('');
  
  // Check JWT secret
  const jwtSecretOk = checkJwtSecret();
  
  // Check auth library
  const authLibraryOk = checkAuthLibrary();
  
  // Check auth routes
  const authRoutesOk = checkAuthRoutes();
  
  // Check test users
  const testUsersOk = await checkTestUsers();
  
  // Print summary
  printSection('Summary');
  
  printResult('JWT Secret', jwtSecretOk, jwtSecretOk ? 'Properly configured' : 'Configuration issues detected');
  printResult('Auth Library', authLibraryOk, authLibraryOk ? 'All required functions present' : 'Missing required functions');
  printResult('Auth Routes', authRoutesOk, authRoutesOk ? 'All required routes exist' : 'Missing required routes');
  printResult('Test Users', testUsersOk, testUsersOk ? 'All test users found in database' : 'Some test users missing');
  
  const allChecksOk = jwtSecretOk && authLibraryOk && authRoutesOk && testUsersOk;
  
  console.log('');
  if (allChecksOk) {
    print('✅ All authentication checks passed!', 'green');
    print('Your authentication system appears to be properly configured.', 'green');
  } else {
    print('❌ Some authentication checks failed.', 'red');
    print('Please fix the issues highlighted above.', 'red');
    
    console.log('');
    print('Suggested actions:', 'yellow');
    
    if (!jwtSecretOk) {
      print('- Set a strong JWT_SECRET in your .env.local file (at least 32 characters)', 'yellow');
    }
    
    if (!authLibraryOk) {
      print('- Check your lib/auth.ts file for missing functions', 'yellow');
    }
    
    if (!authRoutesOk) {
      print('- Create the missing API routes in app/api/auth/', 'yellow');
    }
    
    if (!testUsersOk) {
      print('- Run database migrations and seed script: npx prisma migrate reset --force', 'yellow');
    }
  }
  
  console.log('');
  print('For more detailed testing, run: node test-auth.js', 'cyan');
}

// Run the main function
main()
  .catch(console.error)
  .finally(() => {
    if (serverProcess) {
      serverProcess.kill();
    }
  });
