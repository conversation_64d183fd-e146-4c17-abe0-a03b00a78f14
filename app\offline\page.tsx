"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { WifiOff, RefreshCw, Home } from "lucide-react"

export default function OfflinePage() {
  return (
    <div className="container flex flex-col items-center justify-center min-h-[calc(100vh-8rem)] py-8 text-center">
      <WifiOff className="h-16 w-16 text-slate-400 mb-6" />

      <h1 className="text-3xl font-bold mb-2">You're offline</h1>

      <p className="text-slate-600 max-w-md mb-8">
        It looks like you're not connected to the internet. Check your connection and try again.
      </p>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          variant="outline"
          size="lg"
          className="flex items-center gap-2"
          onClick={() => window.location.reload()}
        >
          <RefreshCw className="h-4 w-4" />
          Retry Connection
        </Button>

        <Button
          size="lg"
          className="flex items-center gap-2 bg-teal-600 hover:bg-teal-700"
          asChild
        >
          <Link href="/">
            <Home className="h-4 w-4" />
            Go to Homepage
          </Link>
        </Button>
      </div>

      <div className="mt-12 p-6 bg-slate-50 rounded-lg max-w-lg">
        <h2 className="text-lg font-medium mb-3">While you're offline, you can still:</h2>
        <ul className="text-left space-y-2 text-slate-700">
          <li>• View previously visited product pages</li>
          <li>• Access your shopping cart</li>
          <li>• Browse categories you've visited before</li>
          <li>• View your order history (if previously loaded)</li>
        </ul>
      </div>
    </div>
  )
}
