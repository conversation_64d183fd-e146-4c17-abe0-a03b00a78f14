import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const ids = searchParams.get('ids')?.split(',') || [];
    
    if (ids.length === 0) {
      return NextResponse.json(
        { error: 'No product IDs provided' },
        { status: 400 }
      );
    }
    
    // Limit to maximum 4 products for comparison
    const limitedIds = ids.slice(0, 4);
    
    // Get products by IDs
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: limitedIds,
        },
      },
      include: {
        category: true,
        seller: {
          select: {
            id: true,
            name: true,
            avatar: true,
            sellerProfile: {
              select: {
                rating: true,
                verified: true,
              },
            },
          },
        },
        images: true,
      },
    });
    
    // Transform products for response
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.mainImage || product.images[0]?.url || '/placeholder.svg?height=400&width=400',
      condition: product.condition,
      category: product.category.slug,
      seller: product.seller.name || 'Unknown Seller',
      location: product.location,
      isSecondHand: product.isSecondHand,
      usageHistory: product.usageHistory,
      defectsDisclosure: product.defectsDisclosure,
      quantity: product.quantity,
      status: product.status,
      views: product.views,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      sellerInfo: {
        id: product.seller.id,
        name: product.seller.name,
        avatar: product.seller.avatar,
        rating: product.seller.sellerProfile?.rating,
        verified: product.seller.sellerProfile?.verified,
      },
    }));
    
    return NextResponse.json(transformedProducts);
  } catch (error) {
    console.error('Compare products error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
