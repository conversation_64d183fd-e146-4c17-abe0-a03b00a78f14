// Simple test script to verify website functionality
const http = require('http');

const testUrls = [
  'http://localhost:3001/',
  'http://localhost:3001/about',
  'http://localhost:3001/contact',
  'http://localhost:3001/login',
  'http://localhost:3001/register'
];

async function testUrl(url) {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      console.log(`✓ ${url} - Status: ${res.statusCode}`);
      resolve({ url, status: res.statusCode, success: res.statusCode === 200 });
    });
    
    req.on('error', (err) => {
      console.log(`✗ ${url} - Error: ${err.message}`);
      resolve({ url, status: 'ERROR', success: false, error: err.message });
    });
    
    req.setTimeout(10000, () => {
      console.log(`✗ ${url} - Timeout`);
      req.destroy();
      resolve({ url, status: 'TIMEOUT', success: false });
    });
  });
}

async function runTests() {
  console.log('🧪 Testing PASSDOWN website functionality...\n');
  
  const results = [];
  for (const url of testUrls) {
    const result = await testUrl(url);
    results.push(result);
  }
  
  console.log('\n📊 Test Results Summary:');
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);
  
  if (successful === total) {
    console.log('\n🎉 All tests passed! Website is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
}

runTests().catch(console.error);
