import { NextResponse, NextRequest } from 'next/server';
import prisma from '@/lib/db';
import { getAuthUser, verifyToken } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    console.log("ME API route called");

    // Get token directly from request cookies for more reliable authentication
    const token = req.cookies.get('token')?.value;

    if (!token) {
      console.log("ME API: No token found in cookies");
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Verify the token
    const authUser = await verifyToken(token);

    if (!authUser) {
      console.log("ME API: Invalid or expired token");
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    console.log("ME API: Auth user found:", authUser);

    // Get user from database with appropriate relations
    const user = await prisma.user.findUnique({
      where: { id: authUser.id },
      include: {
        sellerProfile: authUser.role === 'seller' || authUser.role === 'admin',
      },
    });

    if (!user) {
      console.log("ME API: User not found in database:", authUser.id);

      // Clear invalid cookies
      const response = NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );

      response.cookies.delete('token');
      response.cookies.delete('auth-state');

      return response;
    }

    console.log("ME API: User found:", user.email, "with role:", user.role);

    // Remove password from response
    const { password, ...userWithoutPassword } = user;

    // Create response with user data
    const response = NextResponse.json({
      user: userWithoutPassword,
      // Include auth info for debugging
      auth: {
        isAuthenticated: true,
        role: authUser.role
      }
    });

    // Add debug header
    response.headers.set('X-Auth-Debug', 'User-Found');

    return response;
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
