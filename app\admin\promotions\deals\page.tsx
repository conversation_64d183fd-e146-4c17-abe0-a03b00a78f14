"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { OptimizedImage } from "@/components/ui/optimized-image"
import {
  Search,
  Plus,
  X,
  Edit,
  Trash2,
  Calendar,
  Percent,
  Tag,
  AlertCircle,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface Deal {
  id: string
  name: string
  description: string
  discountType: "percentage" | "fixed"
  discountValue: number
  startDate: string
  endDate: string
  isActive: boolean
  products: string[]
  categories: string[]
  createdAt: string
  updatedAt: string
}

export default function DealsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [deals, setDeals] = useState<Deal[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([])

  // Fetch deals
  useEffect(() => {
    // Simulate API call with mock data
    setTimeout(() => {
      const mockDeals = [
        {
          id: "deal-1",
          name: "Summer Sale",
          description: "Get up to 20% off on selected products",
          discountType: "percentage" as "percentage",
          discountValue: 20,
          startDate: "2023-06-01T00:00:00Z",
          endDate: "2023-06-30T23:59:59Z",
          isActive: true,
          products: ["1", "2", "3"],
          categories: ["graphics-cards", "processors"],
          createdAt: "2023-05-15T10:30:00Z",
          updatedAt: "2023-05-15T10:30:00Z",
        },
        {
          id: "deal-2",
          name: "Clearance Sale",
          description: "Flat ₹5000 off on selected products",
          discountType: "fixed" as "fixed",
          discountValue: 5000,
          startDate: "2023-07-01T00:00:00Z",
          endDate: "2023-07-15T23:59:59Z",
          isActive: false,
          products: ["4", "5"],
          categories: ["memory", "motherboards"],
          createdAt: "2023-05-20T14:45:00Z",
          updatedAt: "2023-05-20T14:45:00Z",
        },
        {
          id: "deal-3",
          name: "Flash Sale",
          description: "Get 15% off on all storage devices",
          discountType: "percentage" as "percentage",
          discountValue: 15,
          startDate: "2023-05-25T00:00:00Z",
          endDate: "2023-05-27T23:59:59Z",
          isActive: false,
          products: [],
          categories: ["storage"],
          createdAt: "2023-05-18T09:15:00Z",
          updatedAt: "2023-05-18T09:15:00Z",
        },
      ]

      setDeals(mockDeals)
      setFilteredDeals(mockDeals)
      setIsLoading(false)
    }, 1000)
  }, [])

  // Filter deals based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredDeals(deals)
    } else {
      const filtered = deals.filter(
        (deal) =>
          deal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          deal.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDeals(filtered)
    }
  }, [searchQuery, deals])

  // Toggle deal active status
  const toggleDealStatus = (dealId: string) => {
    setDeals(
      deals.map((deal) => {
        if (deal.id === dealId) {
          return {
            ...deal,
            isActive: !deal.isActive,
          }
        }
        return deal
      })
    )
  }

  // Delete deal
  const deleteDeal = (dealId: string) => {
    if (confirm("Are you sure you want to delete this deal?")) {
      setDeals(deals.filter((deal) => deal.id !== dealId))
      setFilteredDeals(filteredDeals.filter((deal) => deal.id !== dealId))
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy")
    } catch (error) {
      return "Invalid date"
    }
  }

  // Check if deal is expired
  const isDealExpired = (endDate: string) => {
    try {
      return new Date(endDate) < new Date()
    } catch (error) {
      return false
    }
  }

  // Check if deal is upcoming
  const isDealUpcoming = (startDate: string) => {
    try {
      return new Date(startDate) > new Date()
    } catch (error) {
      return false
    }
  }

  // Get deal status
  const getDealStatus = (deal: Deal) => {
    if (!deal.isActive) {
      return "inactive"
    }
    if (isDealExpired(deal.endDate)) {
      return "expired"
    }
    if (isDealUpcoming(deal.startDate)) {
      return "upcoming"
    }
    return "active"
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading deals...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Deals & Promotions</h1>
          <p className="text-slate-500 mt-1">Manage special deals and promotions for your products</p>
        </div>
        <Button asChild className="bg-teal-600 hover:bg-teal-700">
          <Link href="/admin/promotions/deals/new">
            <Plus className="h-4 w-4 mr-2" />
            Create New Deal
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Deals</CardTitle>
          <CardDescription>View and manage all promotional deals</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <Input
                placeholder="Search deals..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear search</span>
                </Button>
              )}
            </div>
          </div>

          {filteredDeals.length === 0 ? (
            <div className="text-center py-8">
              <Tag className="h-12 w-12 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No deals found</h3>
              <p className="text-slate-500 max-w-md mx-auto mb-6">
                {searchQuery
                  ? `No deals match your search for "${searchQuery}"`
                  : "You haven't created any deals yet. Create your first deal to start offering promotions."}
              </p>
              {!searchQuery && (
                <Button asChild>
                  <Link href="/admin/promotions/deals/new">Create New Deal</Link>
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredDeals.map((deal) => {
                const status = getDealStatus(deal)
                return (
                  <Card key={deal.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{deal.name}</CardTitle>
                        <Badge
                          className={cn(
                            status === "active"
                              ? "bg-green-100 text-green-800 hover:bg-green-100"
                              : status === "upcoming"
                              ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                              : status === "expired"
                              ? "bg-red-100 text-red-800 hover:bg-red-100"
                              : "bg-slate-100 text-slate-800 hover:bg-slate-100"
                          )}
                        >
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </Badge>
                      </div>
                      <CardDescription>{deal.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="space-y-3">
                        <div className="flex items-center text-sm">
                          <Percent className="h-4 w-4 mr-2 text-slate-500" />
                          <span>
                            {deal.discountType === "percentage"
                              ? `${deal.discountValue}% off`
                              : `₹${deal.discountValue.toLocaleString("en-IN")} off`}
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Calendar className="h-4 w-4 mr-2 text-slate-500" />
                          <span>
                            {formatDate(deal.startDate)} - {formatDate(deal.endDate)}
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {deal.categories.map((category) => (
                            <Badge key={category} variant="outline" className="text-xs">
                              {category}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between pt-2">
                      <div className="flex items-center">
                        <Label htmlFor={`deal-status-${deal.id}`} className="mr-2 text-sm">
                          Active
                        </Label>
                        <Switch
                          id={`deal-status-${deal.id}`}
                          checked={deal.isActive}
                          onCheckedChange={() => toggleDealStatus(deal.id)}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" asChild>
                          <Link href={`/admin/promotions/deals/${deal.id}`}>
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="text-red-500 hover:text-red-600"
                          onClick={() => deleteDeal(deal.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
