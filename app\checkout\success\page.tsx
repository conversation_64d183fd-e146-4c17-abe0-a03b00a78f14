"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import Image from "next/image"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  CheckCircle,
  Home,
  Package,
  Truck,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Clock,
  Download,
  Printer
} from "lucide-react"
import { useCart } from "@/hooks/use-cart"

export default function CheckoutSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId") || "ORD-" + Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  const { clearCart, items } = useCart()
  const [mounted, setMounted] = useState(false)

  // Mock order data
  const orderData = {
    id: orderId,
    date: new Date().toLocaleDateString("en-IN", {
      year: "numeric",
      month: "long",
      day: "numeric"
    }),
    status: "Confirmed",
    paymentMethod: "Credit Card",
    paymentId: "PAY-" + Math.floor(Math.random() * 1000000).toString().padStart(6, '0'),
    shippingAddress: {
      name: "Rahul Sharma",
      street: "123 Main Street, Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      country: "India",
      phone: "+91 9876543210"
    },
    estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "long",
      day: "numeric"
    })
  }

  // Calculate order totals
  const subtotal = items.reduce((total, item) => total + item.price * item.quantity, 0)
  const shipping = subtotal > 5000 ? 0 : 250
  const tax = Math.round(subtotal * 0.18) // 18% GST
  const total = subtotal + shipping + tax

  // Clear cart on page load
  useEffect(() => {
    setMounted(true)
    clearCart()
  }, [clearCart])

  if (!mounted) {
    return null
  }

  return (
    <div className="container px-4 py-12 md:py-24">
      <div className="max-w-4xl mx-auto">
        {/* Order Confirmation Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Order Confirmed!</h1>
          <p className="text-slate-600 max-w-2xl mx-auto">
            Thank you for your purchase. Your order has been confirmed and will be shipped soon.
            We've sent a confirmation email to your registered email address.
          </p>
        </div>

        {/* Order Details */}
        <div className="bg-white p-6 md:p-8 rounded-lg border mb-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h2 className="text-xl font-bold">Order #{orderData.id}</h2>
              <p className="text-slate-500">Placed on {orderData.date}</p>
            </div>
            <div className="mt-4 md:mt-0 flex gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Download className="h-4 w-4" />
                <span>Invoice</span>
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Printer className="h-4 w-4" />
                <span>Print</span>
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-slate-500">
                <Clock className="h-4 w-4" />
                <span>ORDER STATUS</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="font-medium">{orderData.status}</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-slate-500">
                <Truck className="h-4 w-4" />
                <span>ESTIMATED DELIVERY</span>
              </div>
              <p className="font-medium">{orderData.estimatedDelivery}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-slate-500">
                <Calendar className="h-4 w-4" />
                <span>PAYMENT METHOD</span>
              </div>
              <p className="font-medium">{orderData.paymentMethod}</p>
              <p className="text-sm text-slate-500">Transaction ID: {orderData.paymentId}</p>
            </div>
          </div>

          <Separator className="mb-8" />

          {/* Shipping Address */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2">
                <MapPin className="h-4 w-4 text-slate-500" />
                Shipping Address
              </h3>
              <div className="space-y-1">
                <p className="font-medium">{orderData.shippingAddress.name}</p>
                <p>{orderData.shippingAddress.street}</p>
                <p>
                  {orderData.shippingAddress.city}, {orderData.shippingAddress.state} {orderData.shippingAddress.pincode}
                </p>
                <p>{orderData.shippingAddress.country}</p>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2">
                <Phone className="h-4 w-4 text-slate-500" />
                Contact Information
              </h3>
              <div className="space-y-1">
                <p className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-slate-500" />
                  <span>{orderData.shippingAddress.phone}</span>
                </p>
                <p className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-slate-500" />
                  <span><EMAIL></span>
                </p>
              </div>
            </div>
          </div>

          <Separator className="mb-8" />

          {/* Order Items */}
          <h3 className="font-medium mb-4">Order Items</h3>
          <div className="space-y-6 mb-8">
            {items.length > 0 ? (
              items.map((item) => (
                <div key={item.id} className="flex items-start gap-4">
                  <div className="relative w-16 h-16 rounded-md overflow-hidden border shrink-0">
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium truncate">{item.name}</h4>
                    <p className="text-sm text-slate-500">
                      Seller: {item.seller} • Condition: {item.condition}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm">Qty: {item.quantity}</span>
                      <span className="text-sm text-slate-500">×</span>
                      <span className="text-sm">₹{item.price.toLocaleString("en-IN")}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="font-medium">₹{(item.price * item.quantity).toLocaleString("en-IN")}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Package className="h-8 w-8 text-slate-300 mx-auto mb-2" />
                <p className="text-slate-500">No items in this order</p>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="bg-slate-50 p-6 rounded-lg">
            <h3 className="font-medium mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-600">Subtotal</span>
                <span>₹{subtotal.toLocaleString("en-IN")}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600">Shipping</span>
                <span>{shipping === 0 ? "Free" : `₹${shipping.toLocaleString("en-IN")}`}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600">Tax (18% GST)</span>
                <span>₹{tax.toLocaleString("en-IN")}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>₹{total.toLocaleString("en-IN")}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="text-center">
          <h2 className="text-xl font-bold mb-4">What's Next?</h2>
          <p className="text-slate-600 mb-6">
            You'll receive an email confirmation shortly. We'll notify you when your order ships.
            You can also track your order status in your account dashboard.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-teal-600 hover:bg-teal-700">
              <Link href="/account/orders">
                <Package className="h-4 w-4 mr-2" />
                Track Your Order
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Continue Shopping
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
