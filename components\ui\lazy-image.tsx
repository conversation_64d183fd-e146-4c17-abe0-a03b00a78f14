"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface LazyImageProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
  containerClassName?: string
  priority?: boolean
  quality?: number
  placeholder?: "blur" | "empty" | "data:image/..."
  blurDataURL?: string
  sizes?: string
  onLoad?: () => void
  fallbackSrc?: string
}

export function LazyImage({
  src,
  alt,
  width,
  height,
  className,
  containerClassName,
  priority = false,
  quality = 80,
  placeholder = "empty",
  blurDataURL,
  sizes,
  onLoad,
  fallbackSrc = "/placeholder.svg",
  ...props
}: LazyImageProps) {
  const [isLoading, setIsLoading] = useState(!priority)
  const [error, setError] = useState(false)
  const [imageSrc, setImageSrc] = useState(src)
  
  // Reset loading state when src changes
  useEffect(() => {
    setIsLoading(!priority)
    setError(false)
    setImageSrc(src)
  }, [src, priority])
  
  const handleLoad = () => {
    setIsLoading(false)
    if (onLoad) onLoad()
  }
  
  const handleError = () => {
    setError(true)
    setIsLoading(false)
    if (fallbackSrc) {
      setImageSrc(fallbackSrc)
    }
  }
  
  return (
    <div 
      className={cn(
        "relative overflow-hidden",
        containerClassName
      )}
      style={{ aspectRatio: `${width} / ${height}` }}
    >
      {isLoading && (
        <div className="absolute inset-0 bg-slate-100 animate-pulse" />
      )}
      
      <Image
        src={imageSrc}
        alt={alt}
        width={width}
        height={height}
        quality={quality}
        priority={priority}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        sizes={sizes}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          className
        )}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  )
}
