// Test script for authentication system
// Run with: node test-auth.js

const fetch = require('node-fetch');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const API_URL = 'http://localhost:3000/api';
let authCookies = null;

// Helper function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', body = null, cookies = null) {
  const headers = {
    'Content-Type': 'application/json',
  };

  if (cookies) {
    headers['Cookie'] = cookies;
  }

  const options = {
    method,
    headers,
    credentials: 'include',
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${API_URL}${endpoint}`, options);
    
    // Extract cookies from response
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      authCookies = setCookieHeader;
      console.log('Cookies received:', authCookies);
    }
    
    // Get response data
    const data = await response.json();
    
    return {
      status: response.status,
      data,
      headers: Object.fromEntries(response.headers.entries()),
    };
  } catch (error) {
    console.error('Request error:', error);
    return {
      status: 500,
      data: { error: error.message },
    };
  }
}

// Test health endpoint
async function testHealth() {
  console.log('\n--- Testing Health Endpoint ---');
  const response = await makeRequest('/health');
  console.log(`Status: ${response.status}`);
  console.log('Response:', JSON.stringify(response.data, null, 2));
  return response;
}

// Test login
async function testLogin(email, password) {
  console.log('\n--- Testing Login ---');
  const response = await makeRequest('/auth/login', 'POST', { email, password });
  console.log(`Status: ${response.status}`);
  console.log('Response:', JSON.stringify(response.data, null, 2));
  return response;
}

// Test get current user
async function testMe() {
  console.log('\n--- Testing Get Current User ---');
  const response = await makeRequest('/auth/me', 'GET', null, authCookies);
  console.log(`Status: ${response.status}`);
  console.log('Response:', JSON.stringify(response.data, null, 2));
  return response;
}

// Test logout
async function testLogout() {
  console.log('\n--- Testing Logout ---');
  const response = await makeRequest('/auth/logout', 'POST', null, authCookies);
  console.log(`Status: ${response.status}`);
  console.log('Response:', JSON.stringify(response.data, null, 2));
  
  // Clear cookies
  authCookies = null;
  
  return response;
}

// Main test function
async function runTests() {
  console.log('=== PASSDOWN Authentication System Test ===');
  console.log('Make sure the development server is running on http://localhost:3000');
  
  try {
    // Test health endpoint
    await testHealth();
    
    // Test login with admin credentials
    console.log('\nTesting admin login...');
    await testLogin('<EMAIL>', 'admin123');
    
    // Test get current user
    await testMe();
    
    // Test logout
    await testLogout();
    
    // Test login with seller credentials
    console.log('\nTesting seller login...');
    await testLogin('<EMAIL>', 'seller123');
    
    // Test get current user
    await testMe();
    
    // Test logout
    await testLogout();
    
    // Test login with buyer credentials
    console.log('\nTesting buyer login...');
    await testLogin('<EMAIL>', 'buyer123');
    
    // Test get current user
    await testMe();
    
    // Test logout
    await testLogout();
    
    // Test login with invalid credentials
    console.log('\nTesting invalid login...');
    await testLogin('<EMAIL>', 'wrongpassword');
    
    console.log('\n=== All tests completed ===');
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    rl.close();
  }
}

// Run the tests
runTests();
