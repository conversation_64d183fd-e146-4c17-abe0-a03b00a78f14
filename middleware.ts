import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createSupabaseMiddlewareClient } from '@/lib/supabase-ssr'

export async function middleware(request: NextRequest) {
  // Log the request path for debugging
  console.log(`Middleware processing: ${request.nextUrl.pathname}`);

  // Skip middleware for public assets
  if (
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.startsWith('/images') ||
    request.nextUrl.pathname.startsWith('/icons') ||
    request.nextUrl.pathname.includes('favicon.ico') ||
    request.nextUrl.pathname.includes('.svg')
  ) {
    return NextResponse.next();
  }

  // Create a Supabase client configured to use cookies
  const { supabase, supabaseResponse } = createSupabaseMiddlewareClient(request);

  try {
    // Refresh session if expired - required for Server Components
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // Check protected routes
    const isAdminRoute = request.nextUrl.pathname.startsWith('/admin');
    const isSellerRoute = request.nextUrl.pathname.startsWith('/sell') ||
                          request.nextUrl.pathname.startsWith('/seller');
    const isAccountRoute = request.nextUrl.pathname.startsWith('/account');
    const isCheckoutRoute = request.nextUrl.pathname.startsWith('/checkout');
    const isAuthRoute = request.nextUrl.pathname.startsWith('/login') ||
                        request.nextUrl.pathname.startsWith('/register') ||
                        request.nextUrl.pathname.startsWith('/reset-password');

    // If accessing protected routes without a user, redirect to login
    if ((isAdminRoute || isSellerRoute || isAccountRoute || isCheckoutRoute) && !user) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('callbackUrl', request.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // If accessing admin routes without admin role, redirect to home
    if (isAdminRoute && user?.user_metadata?.role !== 'admin') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // If accessing seller routes without seller or admin role, redirect to home
    if (isSellerRoute &&
        user?.user_metadata?.role !== 'seller' &&
        user?.user_metadata?.role !== 'admin') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // If already logged in and trying to access auth routes, redirect to home
    if (isAuthRoute && user) {
      return NextResponse.redirect(new URL('/', request.url));
    }

    return supabaseResponse;
  } catch (error) {
    console.error('Middleware error:', error);

    // On error, redirect to login for safety
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }
}

export const config = {
  matcher: [
    // Protected routes
    '/admin/:path*',
    '/sell/:path*',
    '/seller/:path*',
    '/account/:path*',
    '/checkout/:path*',
    // Auth routes
    '/login',
    '/register',
    '/reset-password',
  ],
};
