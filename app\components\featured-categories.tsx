import Link from "next/link"
import Image from "next/image"

const categories = [
  {
    name: "Electronics",
    image: "https://images.unsplash.com/photo-1550009158-9ebf69173e03?q=80&w=1901&auto=format&fit=crop",
    slug: "electronics",
    description: "Smartphones, laptops, tablets, and more",
  },
  {
    name: "Computer Parts",
    image: "https://images.unsplash.com/photo-1591488320449-011701bb6704?q=80&w=2070&auto=format&fit=crop",
    slug: "computer-parts",
    description: "Graphics cards, processors, motherboards, and more",
  },
  {
    name: "Home & Kitchen",
    image: "https://images.unsplash.com/photo-1556911220-bda9f7b24446?q=80&w=2070&auto=format&fit=crop",
    slug: "home-kitchen",
    description: "Furniture, appliances, decor, and more",
  },
  {
    name: "Books & Media",
    image: "https://images.unsplash.com/photo-1495446815901-a7297e633e8d?q=80&w=2070&auto=format&fit=crop",
    slug: "books-media",
    description: "Books, movies, music, and video games",
  },
  {
    name: "Toys & Games",
    image: "https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?q=80&w=2070&auto=format&fit=crop",
    slug: "toys-games",
    description: "Board games, puzzles, collectibles, and more",
  },
  {
    name: "Clothing & Fashion",
    image: "https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?q=80&w=2070&auto=format&fit=crop",
    slug: "clothing-fashion",
    description: "Men's, women's, and children's clothing",
  },
]

export default function FeaturedCategories() {
  return (
    <section className="py-12 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Shop by Category</h2>
            <p className="max-w-[700px] text-slate-500 md:text-xl">
              Find exactly what you need from our wide range of computer components
            </p>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 md:gap-6 mt-12">
          {categories.map((category) => (
            <Link
              href={`/category/${category.slug}`}
              key={category.slug}
              className="group relative overflow-hidden rounded-xl bg-white shadow-sm transition-all hover:shadow-md"
            >
              <div className="aspect-square overflow-hidden">
                <Image
                  src={category.image || "/placeholder.svg"}
                  alt={category.name}
                  width={300}
                  height={300}
                  className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="p-3 text-center">
                <h3 className="font-medium">{category.name}</h3>
                <p className="text-xs text-slate-500 mt-1">{category.description}</p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}

