import prisma from '@/lib/db'

interface RecommendationOptions {
  userId?: string
  productId?: string
  categoryId?: string
  limit?: number
  excludeProductIds?: string[]
}

interface ProductRecommendation {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  mainImage?: string
  condition: string
  location: string
  category: {
    id: string
    name: string
    slug: string
  }
  seller: {
    id: string
    name: string
    avatar?: string
    sellerProfile?: {
      rating?: number
      verified: boolean
    }
  }
  images: Array<{ url: string }>
  score: number
  reason: string
}

export class RecommendationEngine {
  /**
   * Get personalized recommendations for a user
   */
  static async getPersonalizedRecommendations(
    userId: string,
    options: RecommendationOptions = {}
  ): Promise<ProductRecommendation[]> {
    const { limit = 10, excludeProductIds = [] } = options

    try {
      // Get user's purchase history
      const userOrders = await prisma.order.findMany({
        where: {
          userId,
          status: 'delivered',
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  category: true,
                },
              },
            },
          },
        },
      })

      // Get user's wishlist
      const userWishlist = await prisma.wishlist.findMany({
        where: { userId },
        include: {
          product: {
            include: {
              category: true,
            },
          },
        },
      })

      // Extract categories from purchase history and wishlist
      const purchasedCategories = new Set<string>()
      const wishlistCategories = new Set<string>()

      userOrders.forEach(order => {
        order.items.forEach(item => {
          if (item.product?.category) {
            purchasedCategories.add(item.product.category.id)
          }
        })
      })

      userWishlist.forEach(item => {
        if (item.product?.category) {
          wishlistCategories.add(item.product.category.id)
        }
      })

      // Get recommendations based on categories
      const categoryRecommendations = await this.getRecommendationsByCategories(
        Array.from(new Set([...purchasedCategories, ...wishlistCategories])),
        { limit: limit * 2, excludeProductIds }
      )

      // Get collaborative filtering recommendations
      const collaborativeRecommendations = await this.getCollaborativeRecommendations(
        userId,
        { limit: limit * 2, excludeProductIds }
      )

      // Combine and score recommendations
      const allRecommendations = new Map<string, ProductRecommendation>()

      // Add category-based recommendations
      categoryRecommendations.forEach(product => {
        const score = purchasedCategories.has(product.category.id) ? 0.8 : 0.6
        allRecommendations.set(product.id, {
          ...product,
          score,
          reason: 'Based on your interests',
        })
      })

      // Add collaborative recommendations with higher score
      collaborativeRecommendations.forEach(product => {
        const existing = allRecommendations.get(product.id)
        if (existing) {
          existing.score = Math.max(existing.score, 0.9)
          existing.reason = 'People like you also viewed'
        } else {
          allRecommendations.set(product.id, {
            ...product,
            score: 0.9,
            reason: 'People like you also viewed',
          })
        }
      })

      // Sort by score and return top recommendations
      return Array.from(allRecommendations.values())
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

    } catch (error) {
      console.error('Error getting personalized recommendations:', error)
      return this.getFallbackRecommendations(options)
    }
  }

  /**
   * Get recommendations based on a specific product (similar products)
   */
  static async getSimilarProducts(
    productId: string,
    options: RecommendationOptions = {}
  ): Promise<ProductRecommendation[]> {
    const { limit = 6, excludeProductIds = [] } = options

    try {
      // Get the source product
      const sourceProduct = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          category: true,
          seller: true,
        },
      })

      if (!sourceProduct) {
        return []
      }

      // Find similar products in the same category
      const similarProducts = await prisma.product.findMany({
        where: {
          categoryId: sourceProduct.categoryId,
          id: {
            not: productId,
            notIn: excludeProductIds,
          },
          status: 'active',
        },
        include: {
          category: true,
          seller: {
            select: {
              id: true,
              name: true,
              avatar: true,
              sellerProfile: {
                select: {
                  rating: true,
                  verified: true,
                },
              },
            },
          },
          images: true,
        },
        take: limit * 2,
      })

      // Score products based on similarity
      const scoredProducts = similarProducts.map(product => {
        let score = 0.5 // Base score for same category

        // Price similarity (higher score for similar price range)
        const priceDiff = Math.abs(product.price - sourceProduct.price)
        const priceScore = Math.max(0, 1 - (priceDiff / sourceProduct.price))
        score += priceScore * 0.3

        // Condition similarity
        if (product.condition === sourceProduct.condition) {
          score += 0.2
        }

        // Same seller bonus
        if (product.sellerId === sourceProduct.sellerId) {
          score += 0.1
        }

        return {
          ...product,
          score,
          reason: 'Similar to this product',
        }
      })

      return scoredProducts
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)

    } catch (error) {
      console.error('Error getting similar products:', error)
      return []
    }
  }

  /**
   * Get trending/popular products
   */
  static async getTrendingProducts(
    options: RecommendationOptions = {}
  ): Promise<ProductRecommendation[]> {
    const { limit = 10, excludeProductIds = [] } = options

    try {
      // Get products with high views and recent activity
      const trendingProducts = await prisma.product.findMany({
        where: {
          status: 'active',
          id: {
            notIn: excludeProductIds,
          },
        },
        include: {
          category: true,
          seller: {
            select: {
              id: true,
              name: true,
              avatar: true,
              sellerProfile: {
                select: {
                  rating: true,
                  verified: true,
                },
              },
            },
          },
          images: true,
          _count: {
            select: {
              wishlist: true,
            },
          },
        },
        orderBy: [
          { views: 'desc' },
          { createdAt: 'desc' },
        ],
        take: limit,
      })

      return trendingProducts.map(product => ({
        ...product,
        score: 1.0,
        reason: 'Trending now',
      }))

    } catch (error) {
      console.error('Error getting trending products:', error)
      return []
    }
  }

  /**
   * Get recommendations based on categories
   */
  private static async getRecommendationsByCategories(
    categoryIds: string[],
    options: RecommendationOptions = {}
  ): Promise<any[]> {
    const { limit = 10, excludeProductIds = [] } = options

    if (categoryIds.length === 0) {
      return []
    }

    return prisma.product.findMany({
      where: {
        categoryId: {
          in: categoryIds,
        },
        status: 'active',
        id: {
          notIn: excludeProductIds,
        },
      },
      include: {
        category: true,
        seller: {
          select: {
            id: true,
            name: true,
            avatar: true,
            sellerProfile: {
              select: {
                rating: true,
                verified: true,
              },
            },
          },
        },
        images: true,
      },
      orderBy: [
        { views: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    })
  }

  /**
   * Get collaborative filtering recommendations
   */
  private static async getCollaborativeRecommendations(
    userId: string,
    options: RecommendationOptions = {}
  ): Promise<any[]> {
    const { limit = 10, excludeProductIds = [] } = options

    try {
      // Find users with similar purchase patterns
      const userOrders = await prisma.order.findMany({
        where: {
          userId,
          status: 'delivered',
        },
        include: {
          items: {
            select: {
              productId: true,
            },
          },
        },
      })

      const userProductIds = new Set(
        userOrders.flatMap(order => order.items.map(item => item.productId))
      )

      if (userProductIds.size === 0) {
        return []
      }

      // Find other users who bought similar products
      const similarUsers = await prisma.order.findMany({
        where: {
          userId: {
            not: userId,
          },
          status: 'delivered',
          items: {
            some: {
              productId: {
                in: Array.from(userProductIds),
              },
            },
          },
        },
        include: {
          items: {
            select: {
              productId: true,
            },
          },
        },
        take: 50,
      })

      // Get products bought by similar users
      const recommendedProductIds = new Set<string>()
      similarUsers.forEach(order => {
        order.items.forEach(item => {
          if (!userProductIds.has(item.productId)) {
            recommendedProductIds.add(item.productId)
          }
        })
      })

      if (recommendedProductIds.size === 0) {
        return []
      }

      return prisma.product.findMany({
        where: {
          id: {
            in: Array.from(recommendedProductIds),
            notIn: excludeProductIds,
          },
          status: 'active',
        },
        include: {
          category: true,
          seller: {
            select: {
              id: true,
              name: true,
              avatar: true,
              sellerProfile: {
                select: {
                  rating: true,
                  verified: true,
                },
              },
            },
          },
          images: true,
        },
        take: limit,
      })

    } catch (error) {
      console.error('Error getting collaborative recommendations:', error)
      return []
    }
  }

  /**
   * Fallback recommendations when personalized recommendations fail
   */
  private static async getFallbackRecommendations(
    options: RecommendationOptions = {}
  ): Promise<ProductRecommendation[]> {
    const { limit = 10, excludeProductIds = [] } = options

    try {
      const fallbackProducts = await prisma.product.findMany({
        where: {
          status: 'active',
          id: {
            notIn: excludeProductIds,
          },
        },
        include: {
          category: true,
          seller: {
            select: {
              id: true,
              name: true,
              avatar: true,
              sellerProfile: {
                select: {
                  rating: true,
                  verified: true,
                },
              },
            },
          },
          images: true,
        },
        orderBy: [
          { views: 'desc' },
          { createdAt: 'desc' },
        ],
        take: limit,
      })

      return fallbackProducts.map(product => ({
        ...product,
        score: 0.3,
        reason: 'Popular products',
      }))

    } catch (error) {
      console.error('Error getting fallback recommendations:', error)
      return []
    }
  }
}
