export type ProductCategory =
  | "electronics" | "clothing-fashion" | "home-kitchen"
  | "books-media" | "toys-games" | "automotive"
  | "pet-supplies" | "jewelry-watches" | "tools-improvement"
  | "travel-luggage" | "musical-instruments" | "sports-equipment"
  | "art-supplies" | "vintage-collectibles" | "antique-furniture"
  // Legacy computer part categories
  | "graphics-cards" | "processors" | "laptops" | "motherboards" | "ram" | "storage";

// Electronics subcategories
export type ElectronicsSubcategory =
  | "smartphones" | "laptops-computers" | "tablets" | "smartwatches"
  | "headphones-earbuds" | "cameras" | "gaming-consoles" | "smart-home-devices"
  | "accessories";

// Clothing and Fashion subcategories
export type ClothingFashionSubcategory =
  | "womens-clothing" | "mens-clothing" | "childrens-clothing"
  | "shoes" | "accessories" | "activewear" | "vintage-clothing";

// Home and Kitchen subcategories
export type HomeKitchenSubcategory =
  | "furniture" | "kitchen-appliances" | "home-decor" | "bedding-linens"
  | "cookware" | "small-appliances" | "storage-organization" | "lighting"
  | "garden-outdoor";

// Books and Media subcategories
export type BooksMediaSubcategory =
  | "physical-books" | "used-books" | "comic-books" | "movies-tv"
  | "music" | "video-games";

// Toys and Games subcategories
export type ToysGamesSubcategory =
  | "board-games" | "puzzles" | "educational-toys" | "collectible-toys"
  | "action-figures" | "building-sets" | "outdoor-toys";

// Automotive subcategories
export type AutomotiveSubcategory =
  | "car-parts" | "tools-equipment" | "tires-wheels" | "performance-upgrades"
  | "electronics-navigation" | "safety-equipment";

// Computer Parts subcategories (legacy)
export type ComputerPartsSubcategory =
  | "graphics-cards" | "processors" | "motherboards" | "ram" | "storage"
  | "cooling" | "power-supplies" | "cases" | "peripherals";

export type ProductSubcategory =
  | ElectronicsSubcategory
  | ClothingFashionSubcategory
  | HomeKitchenSubcategory
  | BooksMediaSubcategory
  | ToysGamesSubcategory
  | AutomotiveSubcategory
  | ComputerPartsSubcategory;

export type Product = {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice: number | null;
  image: string;
  condition: "New" | "Like New" | "Good" | "Fair" | "Poor";
  category: ProductCategory;
  subcategory: ProductSubcategory;
  seller: string;
  location: string;
  isSecondHand?: boolean;
  usageHistory?: string;
  defectsDisclosure?: string;
  quantity?: number;
  status?: "active" | "sold" | "hidden";
  views?: number;
  createdAt?: string;
  updatedAt?: string;
};

export interface Category {
  name: string
  slug: string
  image: string
}

export interface CartItem extends Product {
  quantity: number
}

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
}

