"use client"

import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { allProducts } from "../data/enhanced-products"
import { User, Package, Heart, Settings, ShoppingBag, Edit, Eye, LogOut } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"

export default function AccountPage() {
  const router = useRouter()
  const { user, isLoading, logout } = useAuth()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/login?callbackUrl=/account")
    }
  }, [user, isLoading, router])

  // Show loading or redirect if not authenticated
  if (isLoading || !user) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your account information.</p>
        </div>
      </div>
    )
  }

  // Format join date
  const joinDate = new Date(user.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
  })

  // Mock orders
  const orders = [
    {
      id: "ORD12345",
      date: "May 10, 2023",
      status: "Delivered",
      total: 42999,
      items: [allProducts[0]],
    },
    {
      id: "ORD12346",
      date: "April 25, 2023",
      status: "Processing",
      total: 89999,
      items: [allProducts[2]],
    },
  ]

  // Mock listings
  const listings = [
    {
      ...allProducts[1],
      status: "Active",
      views: 45,
      likes: 12,
    },
    {
      ...allProducts[3],
      status: "Sold",
      views: 78,
      likes: 23,
    },
  ]

  // Mock wishlist
  const wishlist = [allProducts[0], allProducts[2]]

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar */}
        <div className="w-full md:w-64 shrink-0">
          <div className="sticky top-24 space-y-6">
            <div className="flex flex-col items-center text-center p-6 border rounded-lg">
              <div className="relative w-20 h-20 rounded-full overflow-hidden mb-4">
                <Image src={user.avatar || "/placeholder.svg"} alt={user.name || "User"} fill className="object-cover" />
              </div>
              <h2 className="font-bold text-lg">{user.name || "User"}</h2>
              <p className="text-sm text-slate-500">Member since {joinDate}</p>
              <div className="flex flex-col gap-2 w-full mt-4">
                <Button variant="outline" size="sm">
                  Edit Profile
                </Button>
                <Button variant="outline" size="sm" className="text-red-600" onClick={() => logout()}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>

            <div className="space-y-1">
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/account" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Account Overview
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/account/orders" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Orders
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/account/listings" className="flex items-center gap-2">
                  <ShoppingBag className="h-4 w-4" />
                  My Listings
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/account/wishlist" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Wishlist
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/account/settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <h1 className="text-2xl md:text-3xl font-bold mb-8">My Account</h1>

          <Tabs defaultValue="overview" className="space-y-8">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="listings">Listings</TabsTrigger>
              <TabsTrigger value="wishlist">Wishlist</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{orders.length}</div>
                    <p className="text-xs text-slate-500 mt-1">Last order on May 10, 2023</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Active Listings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">1</div>
                    <p className="text-xs text-slate-500 mt-1">1 item sold</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Wishlist Items</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{wishlist.length}</div>
                    <p className="text-xs text-slate-500 mt-1">Last added on May 5, 2023</p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>Your recent purchases on PASSDOWN</CardDescription>
                </CardHeader>
                <CardContent>
                  {orders.length > 0 ? (
                    <div className="space-y-4">
                      {orders.slice(0, 2).map((order) => (
                        <div key={order.id} className="flex flex-col sm:flex-row gap-4 p-4 border rounded-lg">
                          <div className="relative w-20 h-20 rounded-md overflow-hidden border shrink-0">
                            <Image
                              src={order.items[0].image || "/placeholder.svg"}
                              alt={order.items[0].name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                              <div>
                                <div className="font-medium">{order.id}</div>
                                <div className="text-sm text-slate-500">{order.date}</div>
                              </div>
                              <Badge className={order.status === "Delivered" ? "bg-green-500" : "bg-amber-500"}>
                                {order.status}
                              </Badge>
                            </div>
                            <div className="mt-2">
                              <div className="text-sm line-clamp-1">{order.items[0].name}</div>
                              <div className="font-medium text-teal-600">₹{order.total.toLocaleString("en-IN")}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-slate-500">You haven't placed any orders yet.</p>
                      <Button asChild className="mt-4">
                        <Link href="/">Start Shopping</Link>
                      </Button>
                    </div>
                  )}

                  {orders.length > 0 && (
                    <div className="mt-4 text-center">
                      <Button variant="outline" asChild>
                        <Link href="/account/orders">View All Orders</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="orders" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Order History</CardTitle>
                  <CardDescription>View and manage your orders</CardDescription>
                </CardHeader>
                <CardContent>
                  {orders.length > 0 ? (
                    <div className="space-y-4">
                      {orders.map((order) => (
                        <div key={order.id} className="flex flex-col sm:flex-row gap-4 p-4 border rounded-lg">
                          <div className="relative w-20 h-20 rounded-md overflow-hidden border shrink-0">
                            <Image
                              src={order.items[0].image || "/placeholder.svg"}
                              alt={order.items[0].name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                              <div>
                                <div className="font-medium">{order.id}</div>
                                <div className="text-sm text-slate-500">{order.date}</div>
                              </div>
                              <Badge className={order.status === "Delivered" ? "bg-green-500" : "bg-amber-500"}>
                                {order.status}
                              </Badge>
                            </div>
                            <div className="mt-2">
                              <div className="text-sm line-clamp-1">{order.items[0].name}</div>
                              <div className="font-medium text-teal-600">₹{order.total.toLocaleString("en-IN")}</div>
                            </div>
                            <div className="mt-4 flex flex-wrap gap-2">
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                              {order.status === "Delivered" && (
                                <Button size="sm" variant="outline">
                                  Write Review
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-slate-500">You haven't placed any orders yet.</p>
                      <Button asChild className="mt-4">
                        <Link href="/">Start Shopping</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="listings" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">My Listings</h2>
                <Button asChild>
                  <Link href="/sell">Create New Listing</Link>
                </Button>
              </div>

              <Card>
                <CardContent className="p-0">
                  {listings.length > 0 ? (
                    <div className="divide-y">
                      {listings.map((listing) => (
                        <div key={listing.id} className="flex flex-col sm:flex-row gap-4 p-4">
                          <div className="relative w-20 h-20 rounded-md overflow-hidden border shrink-0">
                            <Image
                              src={listing.image || "/placeholder.svg"}
                              alt={listing.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                              <div className="font-medium">{listing.name}</div>
                              <Badge className={listing.status === "Active" ? "bg-green-500" : "bg-slate-500"}>
                                {listing.status}
                              </Badge>
                            </div>
                            <div className="mt-2 flex flex-wrap gap-4">
                              <div className="text-sm text-slate-500">
                                Price:{" "}
                                <span className="font-medium text-teal-600">
                                  ₹{listing.price.toLocaleString("en-IN")}
                                </span>
                              </div>
                              <div className="text-sm text-slate-500">
                                Views: <span className="font-medium">{listing.views}</span>
                              </div>
                              <div className="text-sm text-slate-500">
                                Likes: <span className="font-medium">{listing.likes}</span>
                              </div>
                            </div>
                            <div className="mt-4 flex flex-wrap gap-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              {listing.status === "Active" && (
                                <Button size="sm" variant="outline">
                                  <Edit className="h-4 w-4 mr-1" />
                                  Edit
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-slate-500">You haven't created any listings yet.</p>
                      <Button asChild className="mt-4">
                        <Link href="/sell">Create Listing</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="wishlist" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>My Wishlist</CardTitle>
                  <CardDescription>Items you've saved for later</CardDescription>
                </CardHeader>
                <CardContent>
                  {wishlist.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {wishlist.map((item) => (
                        <div key={item.id} className="flex gap-4 p-4 border rounded-lg">
                          <div className="relative w-20 h-20 rounded-md overflow-hidden border shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <Link href={`/product/${item.id}`} className="font-medium hover:text-teal-600">
                              {item.name}
                            </Link>
                            <div className="mt-1 font-medium text-teal-600">₹{item.price.toLocaleString("en-IN")}</div>
                            <div className="mt-4 flex gap-2">
                              <Button size="sm">Add to Cart</Button>
                              <Button size="sm" variant="outline">
                                Remove
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-slate-500">Your wishlist is empty.</p>
                      <Button asChild className="mt-4">
                        <Link href="/">Browse Products</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
