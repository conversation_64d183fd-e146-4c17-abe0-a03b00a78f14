import type React from "react"
import type { Metada<PERSON>, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Header from "./components/header"
import Footer from "./components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/hooks/use-auth"
import { SupabaseAuthProvider } from "@/hooks/use-supabase-auth"
import { CartProvider } from "@/hooks/use-cart"
import { NotificationsProvider } from "@/hooks/use-notifications"
import { MobileProvider } from "@/hooks/use-mobile"
import { Toaster } from "@/components/ui/toaster"
import { PWARegister } from "@/app/components/pwa-register"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "PASSDOWN - Buy & Sell Computer Components",
  description: "Your trusted marketplace for quality second-hand and new computer components at affordable prices.",
  generator: 'v0.dev',
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PASSDOWN",
  },
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: "#0d9488",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <SupabaseAuthProvider>
            <AuthProvider>
              <CartProvider>
                <NotificationsProvider>
                  <MobileProvider>
                    <Header />
                    {children}
                    <Footer />
                    <Toaster />
                    <PWARegister />
                  </MobileProvider>
                </NotificationsProvider>
              </CartProvider>
            </AuthProvider>
          </SupabaseAuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}

