import { NextResponse } from 'next/server';
import { deleteTokenCookie } from '@/lib/auth';

export async function POST() {
  try {
    console.log('Logout API route called');

    // Delete authentication cookies
    await deleteTokenCookie();

    // Create a response
    const response = NextResponse.json({
      message: 'Logged out successfully',
    });

    // Explicitly clear cookies in the response as well
    response.cookies.delete('token');
    response.cookies.delete('auth-state');

    // Add a debug header
    response.headers.set('X-Auth-Debug', 'Cookies-Cleared');

    console.log('Logout successful, cookies cleared');

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
