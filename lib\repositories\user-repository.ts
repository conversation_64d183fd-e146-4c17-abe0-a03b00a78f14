import { DbClient } from '../supabase-client';
import bcrypt from 'bcryptjs';

export interface UserCreateInput {
  email: string;
  name: string;
  password: string;
  role: 'buyer' | 'seller' | 'admin';
  avatar?: string;
  sellerProfile?: {
    storeName?: string;
    description?: string;
    location?: string;
  };
}

export interface UserUpdateInput {
  name?: string;
  email?: string;
  password?: string;
  role?: 'buyer' | 'seller' | 'admin';
  avatar?: string;
  sellerProfile?: {
    storeName?: string;
    description?: string;
    location?: string;
    verified?: boolean;
    rating?: number;
  };
}

export class UserRepository {
  /**
   * Find a user by ID
   */
  static async findById(id: string) {
    return DbClient.findById('users', id, {
      include: {
        sellerProfile: true,
      },
    });
  }

  /**
   * Find a user by email
   */
  static async findByEmail(email: string) {
    return DbClient.findByField('users', 'email', email, {
      include: {
        sellerProfile: true,
      },
    });
  }

  /**
   * Find all users with optional filtering
   */
  static async findAll(options: any = {}) {
    return DbClient.findMany('users', {
      ...options,
      include: {
        sellerProfile: true,
      },
    });
  }

  /**
   * Create a new user
   */
  static async create(data: UserCreateInput) {
    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 10);

    // Extract seller profile data if present
    const { sellerProfile, ...userData } = data;

    // Create user
    const user = await DbClient.create('users', {
      ...userData,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create seller profile if needed
    if (data.role === 'seller' && sellerProfile) {
      await DbClient.create('seller_profiles', {
        userId: user.id,
        storeName: sellerProfile.storeName || `${user.name}'s Store`,
        description: sellerProfile.description || '',
        location: sellerProfile.location || '',
        verified: false,
        rating: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Fetch the user again with the seller profile
      return this.findById(user.id);
    }

    return user;
  }

  /**
   * Update an existing user
   */
  static async update(id: string, data: UserUpdateInput) {
    // Hash password if provided
    let updateData = { ...data };
    if (data.password) {
      updateData.password = await bcrypt.hash(data.password, 10);
    }

    // Extract seller profile data if present
    const { sellerProfile, ...userData } = updateData;

    // Update user
    const user = await DbClient.update('users', id, {
      ...userData,
      updatedAt: new Date(),
    });

    // Update seller profile if needed
    if (sellerProfile) {
      const existingProfile = await DbClient.findByField('seller_profiles', 'userId', id);

      if (existingProfile) {
        await DbClient.update('seller_profiles', existingProfile.id, {
          ...sellerProfile,
          updatedAt: new Date(),
        });
      } else if (user.role === 'seller') {
        await DbClient.create('seller_profiles', {
          userId: id,
          storeName: sellerProfile.storeName || `${user.name}'s Store`,
          description: sellerProfile.description || '',
          location: sellerProfile.location || '',
          verified: sellerProfile.verified || false,
          rating: sellerProfile.rating || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      // Fetch the user again with the updated seller profile
      return this.findById(id);
    }

    return user;
  }

  /**
   * Delete a user
   */
  static async delete(id: string) {
    return DbClient.delete('users', id);
  }

  /**
   * Count users with optional filtering
   */
  static async count(where: any = {}) {
    return DbClient.count('users', where);
  }

  /**
   * Verify a user's password
   */
  static async verifyPassword(user: any, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  /**
   * Find sellers
   */
  static async findSellers(options: any = {}) {
    return DbClient.findMany('users', {
      ...options,
      where: {
        ...options.where,
        role: 'seller',
      },
      include: {
        sellerProfile: true,
      },
    });
  }

  /**
   * Find pending sellers (not verified)
   */
  static async findPendingSellers(options: any = {}) {
    return DbClient.findMany('users', {
      ...options,
      where: {
        ...options.where,
        role: 'seller',
        sellerProfile: {
          verified: false,
        },
      },
      include: {
        sellerProfile: true,
      },
    });
  }

  /**
   * Verify a seller
   */
  static async verifySeller(id: string) {
    const user = await this.findById(id);
    
    if (!user || user.role !== 'seller') {
      throw new Error('User is not a seller');
    }

    const sellerProfile = await DbClient.findByField('seller_profiles', 'userId', id);
    
    if (!sellerProfile) {
      throw new Error('Seller profile not found');
    }

    await DbClient.update('seller_profiles', sellerProfile.id, {
      verified: true,
      updatedAt: new Date(),
    });

    return this.findById(id);
  }
}
