"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Heart, ShoppingCart, Trash2, AlertCircle, Loader2 } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { useCart } from "@/hooks/use-cart"
import { allProducts } from "@/app/data/enhanced-products"
import type { Product } from "@/app/types"

export default function WishlistPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isLoading: authLoading } = useAuth()
  const { addItem } = useCart()
  const [wishlistItems, setWishlistItems] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [removingItems, setRemovingItems] = useState<Record<string, boolean>>({})
  const [addingToCart, setAddingToCart] = useState<Record<string, boolean>>({})

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?callbackUrl=/wishlist")
    }
  }, [user, authLoading, router])

  // Fetch wishlist items
  useEffect(() => {
    if (user) {
      setIsLoading(true)

      // In a real app, you would fetch the wishlist items from your API
      // For now, we'll use mock data
      const fetchWishlist = async () => {
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000))

          // For demo purposes, we'll use some products from allProducts
          const mockWishlistItems = allProducts.slice(0, 4)
          setWishlistItems(mockWishlistItems)
        } catch (error) {
          console.error("Error fetching wishlist:", error)
          toast({
            title: "Error",
            description: "Failed to load your wishlist. Please try again later.",
            variant: "destructive"
          })
        } finally {
          setIsLoading(false)
        }
      }

      fetchWishlist()
    }
  }, [user, toast])

  // Handle remove from wishlist
  const handleRemoveFromWishlist = async (productId: string) => {
    setRemovingItems(prev => ({ ...prev, [productId]: true }))

    try {
      // In a real app, you would call your API to remove the item from the wishlist
      // await fetch(`/api/wishlist/${productId}`, { method: 'DELETE' })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))

      // Update local state
      setWishlistItems(prev => prev.filter(item => item.id !== productId))

      toast({
        title: "Item Removed",
        description: "The item has been removed from your wishlist.",
      })
    } catch (error) {
      console.error("Error removing from wishlist:", error)
      toast({
        title: "Error",
        description: "Failed to remove item from wishlist. Please try again.",
        variant: "destructive"
      })
    } finally {
      setRemovingItems(prev => ({ ...prev, [productId]: false }))
    }
  }

  // Handle add to cart
  const handleAddToCart = async (product: Product) => {
    setAddingToCart(prev => ({ ...prev, [product.id]: true }))

    try {
      // Add to cart
      addItem(product, 1)

      // Simulate delay
      await new Promise(resolve => setTimeout(resolve, 800))

      toast({
        title: "Added to Cart",
        description: `${product.name} has been added to your cart.`,
      })
    } catch (error) {
      console.error("Error adding to cart:", error)
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive"
      })
    } finally {
      setAddingToCart(prev => ({ ...prev, [product.id]: false }))
    }
  }

  // Handle add all to cart
  const handleAddAllToCart = async () => {
    try {
      // Add all items to cart
      wishlistItems.forEach(item => {
        addItem(item, 1)
      })

      toast({
        title: "Added All to Cart",
        description: "All items have been added to your cart.",
      })

      // Redirect to cart
      router.push("/cart")
    } catch (error) {
      console.error("Error adding all to cart:", error)
      toast({
        title: "Error",
        description: "Failed to add all items to cart. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Show loading or redirect if not authenticated
  if (authLoading || !user) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-teal-600" />
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your wishlist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-5xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">My Wishlist</h1>
            <p className="text-slate-500 mt-1">
              {wishlistItems.length} {wishlistItems.length === 1 ? "item" : "items"} saved for later
            </p>
          </div>
          {wishlistItems.length > 0 && (
            <Button
              onClick={handleAddAllToCart}
              className="bg-teal-600 hover:bg-teal-700"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add All to Cart
            </Button>
          )}
        </div>

        {isLoading ? (
          <div className="space-y-4 animate-pulse">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-40 bg-slate-100 rounded-lg"></div>
            ))}
          </div>
        ) : wishlistItems.length > 0 ? (
          <div className="space-y-4">
            {wishlistItems.map((item) => (
              <Card key={item.id} className="overflow-hidden">
                <div className="flex flex-col sm:flex-row">
                  <div className="relative w-full sm:w-48 h-48">
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="flex-1 p-6">
                    <div className="flex flex-col h-full">
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <Link href={`/product/${item.id}`} className="text-lg font-semibold hover:text-teal-600">
                              {item.name}
                            </Link>
                            <p className="text-sm text-slate-500 mt-1">Seller: {item.seller}</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-slate-400 hover:text-red-500"
                            onClick={() => handleRemoveFromWishlist(item.id)}
                            disabled={removingItems[item.id]}
                          >
                            {removingItems[item.id] ? (
                              <Loader2 className="h-5 w-5 animate-spin" />
                            ) : (
                              <Trash2 className="h-5 w-5" />
                            )}
                            <span className="sr-only">Remove</span>
                          </Button>
                        </div>
                        <div className="mt-2">
                          <span className="text-lg font-bold text-teal-600">₹{item.price.toLocaleString("en-IN")}</span>
                          {item.originalPrice && (
                            <span className="text-sm text-slate-500 line-through ml-2">
                              ₹{item.originalPrice.toLocaleString("en-IN")}
                            </span>
                          )}
                        </div>
                        <div className="mt-2">
                          <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-slate-100">
                            {item.condition}
                          </span>
                        </div>
                      </div>
                      <div className="mt-4 flex flex-wrap gap-2">
                        <Button
                          className="bg-teal-600 hover:bg-teal-700"
                          onClick={() => handleAddToCart(item)}
                          disabled={addingToCart[item.id]}
                        >
                          {addingToCart[item.id] ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <ShoppingCart className="h-4 w-4 mr-2" />
                              Add to Cart
                            </>
                          )}
                        </Button>
                        <Button variant="outline" asChild>
                          <Link href={`/product/${item.id}`}>View Details</Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <div className="flex flex-col items-center">
                <Heart className="h-16 w-16 text-slate-200 mb-4" />
                <h2 className="text-xl font-semibold mb-2">Your wishlist is empty</h2>
                <p className="text-slate-500 mb-6 max-w-md mx-auto">
                  Items added to your wishlist will be saved here. Start browsing and add products you like to your wishlist.
                </p>
                <Button asChild className="bg-teal-600 hover:bg-teal-700">
                  <Link href="/">Browse Products</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommended Products */}
        {!isLoading && wishlistItems.length > 0 && (
          <div className="mt-16">
            <h2 className="text-xl font-bold mb-6">You Might Also Like</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {allProducts.slice(4, 8).map((product) => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="relative aspect-square">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-4">
                    <Link href={`/product/${product.id}`} className="font-medium line-clamp-2 hover:text-teal-600">
                      {product.name}
                    </Link>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="rounded-full hover:bg-teal-50 hover:text-teal-600"
                        onClick={() => {
                          // Add to wishlist logic
                          toast({
                            title: "Added to Wishlist",
                            description: `${product.name} has been added to your wishlist.`,
                          })
                        }}
                      >
                        <Heart className="h-4 w-4" />
                        <span className="sr-only">Add to wishlist</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
