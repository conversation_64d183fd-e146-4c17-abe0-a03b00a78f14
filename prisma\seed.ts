import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seed...');

  // Create admin user
  const adminPassword = await hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: adminPassword, // Update password in case it needs to be reset
    },
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'admin',
    },
  });
  console.log('Admin user created/updated:', admin.email);

  // Create seller user
  const sellerPassword = await hash('seller123', 10);
  const seller = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: sellerPassword, // Update password in case it needs to be reset
    },
    create: {
      email: '<EMAIL>',
      name: 'Seller User',
      password: sellerPassword,
      role: 'seller',
      sellerProfile: {
        create: {
          storeName: 'Tech Treasures',
          description: 'Quality second-hand tech products',
          location: 'Mumbai',
          rating: 4.8,
          verified: true,
        },
      },
    },
  });
  console.log('Seller user created/updated:', seller.email);

  // Create another seller user
  const seller2Password = await hash('seller456', 10);
  const seller2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: seller2Password, // Update password in case it needs to be reset
    },
    create: {
      email: '<EMAIL>',
      name: 'Another Seller',
      password: seller2Password,
      role: 'seller',
      sellerProfile: {
        create: {
          storeName: 'Gadget Galaxy',
          description: 'Quality second-hand gadgets at affordable prices',
          verified: true,
          rating: 4.2,
          location: 'Delhi',
        },
      },
    },
  });
  console.log('Second seller user created/updated:', seller2.email);

  // Create buyer user
  const buyerPassword = await hash('buyer123', 10);
  const buyer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: buyerPassword, // Update password in case it needs to be reset
    },
    create: {
      email: '<EMAIL>',
      name: 'Buyer User',
      password: buyerPassword,
      role: 'buyer',
    },
  });
  console.log('Buyer user created/updated:', buyer.email);

  // Create another buyer user
  const buyer2Password = await hash('buyer456', 10);
  const buyer2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: buyer2Password, // Update password in case it needs to be reset
    },
    create: {
      email: '<EMAIL>',
      name: 'Another Buyer',
      password: buyer2Password,
      role: 'buyer',
    },
  });
  console.log('Second buyer user created/updated:', buyer2.email);

  // Create categories
  const categories = [
    {
      name: 'Electronics',
      slug: 'electronics',
      image: 'https://images.unsplash.com/photo-1550009158-9ebf69173e03?w=500&h=500&fit=crop',
      description: 'Electronic devices and accessories'
    },
    {
      name: 'Clothing & Fashion',
      slug: 'clothing-fashion',
      image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=500&h=500&fit=crop',
      description: 'Clothes, shoes, and accessories'
    },
    {
      name: 'Home & Kitchen',
      slug: 'home-kitchen',
      image: 'https://images.unsplash.com/photo-1556911220-bda9f7f7597e?w=500&h=500&fit=crop',
      description: 'Home and kitchen appliances and accessories'
    },
    {
      name: 'Books & Media',
      slug: 'books-media',
      image: 'https://images.unsplash.com/photo-1495446815901-a7297e633e8d?w=500&h=500&fit=crop',
      description: 'Books, movies, music, and other media'
    },
    {
      name: 'Toys & Games',
      slug: 'toys-games',
      image: 'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=500&h=500&fit=crop',
      description: 'Toys, games, and entertainment items'
    },
    {
      name: 'Computer Parts',
      slug: 'computer-parts',
      image: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=500&h=500&fit=crop',
      description: 'Computer components and accessories'
    },
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {
        image: category.image,
        description: category.description
      },
      create: {
        name: category.name,
        slug: category.slug,
        image: category.image,
        description: category.description
      },
    });
  }
  console.log('Categories created/updated');

  // Create computer parts subcategories
  const computerPartsCategory = await prisma.category.findUnique({
    where: { slug: 'computer-parts' },
  });

  if (computerPartsCategory) {
    const computerPartsSubcategories = [
      {
        name: 'Graphics Cards',
        slug: 'graphics-cards',
        image: 'https://images.unsplash.com/photo-1591488320449-011701bb6704?w=500&h=500&fit=crop',
        description: 'Video cards and graphics processing units'
      },
      {
        name: 'Processors',
        slug: 'processors',
        image: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=500&h=500&fit=crop',
        description: 'CPUs and processors for desktops and laptops'
      },
      {
        name: 'Laptops',
        slug: 'laptops',
        image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop',
        description: 'Portable computers and accessories'
      },
      {
        name: 'Motherboards',
        slug: 'motherboards',
        image: 'https://images.unsplash.com/photo-1518770660439-4636190af475?w=500&h=500&fit=crop',
        description: 'Computer motherboards and components'
      },
      {
        name: 'RAM',
        slug: 'ram',
        image: 'https://images.unsplash.com/photo-**********-1502c2145186?w=500&h=500&fit=crop',
        description: 'Memory modules for computers'
      },
      {
        name: 'Storage',
        slug: 'storage',
        image: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?w=500&h=500&fit=crop',
        description: 'Hard drives, SSDs, and storage devices'
      },
      {
        name: 'Peripherals',
        slug: 'peripherals',
        image: 'https://images.unsplash.com/photo-**********-0686b7003af7?w=500&h=500&fit=crop',
        description: 'Keyboards, mice, and other computer accessories'
      },
      {
        name: 'Cooling',
        slug: 'cooling',
        image: 'https://images.unsplash.com/photo-1587202372775-e229f172b9d7?w=500&h=500&fit=crop',
        description: 'Fans, heatsinks, and cooling solutions'
      },
    ];

    for (const subcategory of computerPartsSubcategories) {
      await prisma.category.upsert({
        where: { slug: subcategory.slug },
        update: {
          image: subcategory.image,
          description: subcategory.description
        },
        create: {
          name: subcategory.name,
          slug: subcategory.slug,
          image: subcategory.image,
          description: subcategory.description,
          parentId: computerPartsCategory.id,
        },
      });
    }
    console.log('Computer parts subcategories created/updated');
  }

  // Create products
  const products = [
    {
      name: 'NVIDIA GeForce RTX 3070 8GB Graphics Card',
      description: 'High-performance graphics card for gaming and content creation. Used for 6 months, in excellent condition.',
      price: 42999,
      originalPrice: 54999,
      mainImage: 'https://images.unsplash.com/photo-1591405351990-4726e331f141?w=500&h=500&fit=crop',
      condition: 'Like New',
      categorySlug: 'graphics-cards',
      location: 'Mumbai',
      isSecondHand: true,
      usageHistory: 'Used for 6 months in a smoke-free environment',
      defectsDisclosure: 'No known defects',
      quantity: 1,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'AMD Ryzen 7 5800X Desktop Processor',
      description: '8-core, 16-thread processor with excellent multi-tasking performance. Barely used, like new condition.',
      price: 24999,
      originalPrice: 32999,
      mainImage: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=500&h=500&fit=crop',
      condition: 'Like New',
      categorySlug: 'processors',
      location: 'Bangalore',
      isSecondHand: true,
      usageHistory: 'Used for 2 months in a gaming PC',
      defectsDisclosure: 'No known defects',
      quantity: 1,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'Dell XPS 13 (2022) - i7, 16GB RAM, 512GB SSD',
      description: 'Premium ultrabook in excellent condition. Includes charger and original packaging.',
      price: 89999,
      originalPrice: 124999,
      mainImage: 'https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?w=500&h=500&fit=crop',
      condition: 'Used',
      categorySlug: 'laptops',
      location: 'Delhi',
      isSecondHand: true,
      usageHistory: 'Used for 1 year for work purposes',
      defectsDisclosure: 'Minor scratch on the bottom case',
      quantity: 1,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'ASUS ROG Strix B550-F Gaming Motherboard',
      description: 'High-end gaming motherboard with WiFi 6 and RGB lighting. New, sealed in box.',
      price: 15999,
      originalPrice: 19999,
      mainImage: 'https://images.unsplash.com/photo-1518770660439-4636190af475?w=500&h=500&fit=crop',
      condition: 'New',
      categorySlug: 'motherboards',
      location: 'Hyderabad',
      isSecondHand: false,
      quantity: 3,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'Corsair Vengeance LPX 32GB (2x16GB) DDR4 3200MHz RAM',
      description: 'High-performance memory kit for gaming and content creation. Used for 3 months.',
      price: 9999,
      originalPrice: 12999,
      mainImage: 'https://images.unsplash.com/photo-**********-1502c2145186?w=500&h=500&fit=crop',
      condition: 'Used',
      categorySlug: 'ram',
      location: 'Chennai',
      isSecondHand: true,
      usageHistory: 'Used for 3 months in a workstation',
      defectsDisclosure: 'No known defects',
      quantity: 1,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'Samsung 970 EVO Plus 1TB NVMe SSD',
      description: 'Fast NVMe SSD with read speeds up to 3,500 MB/s. Lightly used with good health status.',
      price: 8499,
      originalPrice: 10999,
      mainImage: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?w=500&h=500&fit=crop',
      condition: 'Used',
      categorySlug: 'storage',
      location: 'Pune',
      isSecondHand: true,
      usageHistory: 'Used for 6 months as a boot drive',
      defectsDisclosure: '98% health remaining',
      quantity: 2,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'Logitech G Pro X Mechanical Gaming Keyboard',
      description: 'Mechanical gaming keyboard with RGB lighting and swappable switches. Like new condition.',
      price: 7999,
      originalPrice: 11999,
      mainImage: 'https://images.unsplash.com/photo-**********-0686b7003af7?w=500&h=500&fit=crop',
      condition: 'Like New',
      categorySlug: 'peripherals',
      location: 'Kolkata',
      isSecondHand: true,
      usageHistory: 'Used for 2 months',
      defectsDisclosure: 'No known defects',
      quantity: 1,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
    {
      name: 'Cooler Master Hyper 212 RGB CPU Cooler',
      description: 'Efficient CPU cooler with RGB lighting. Compatible with most Intel and AMD sockets.',
      price: 2999,
      originalPrice: 3999,
      mainImage: 'https://images.unsplash.com/photo-1587202372775-e229f172b9d7?w=500&h=500&fit=crop',
      condition: 'New',
      categorySlug: 'cooling',
      location: 'Ahmedabad',
      isSecondHand: false,
      quantity: 3,
      status: 'active',
      sellerEmail: '<EMAIL>',
    },
  ];

  for (const product of products) {
    const { categorySlug, sellerEmail, ...productData } = product;

    // Find category
    const category = await prisma.category.findUnique({
      where: { slug: categorySlug },
    });

    // Find seller
    const seller = await prisma.user.findUnique({
      where: { email: sellerEmail },
    });

    if (category && seller) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: {
          name: productData.name,
          sellerId: seller.id,
        },
      });

      if (existingProduct) {
        // Update existing product
        await prisma.product.update({
          where: { id: existingProduct.id },
          data: {
            ...productData,
            categoryId: category.id,
            sellerId: seller.id,
          },
        });
        console.log(`Product updated: ${productData.name}`);
      } else {
        // Create new product with images
        const newProduct = await prisma.product.create({
          data: {
            ...productData,
            categoryId: category.id,
            sellerId: seller.id,
            images: {
              create: [
                { url: `https://picsum.photos/seed/${Math.random()}/500/500` },
                { url: `https://picsum.photos/seed/${Math.random()}/500/500` },
              ],
            },
          },
        });
        console.log(`Product created: ${newProduct.name}`);
      }
    }
  }
  console.log('Products created/updated');

  console.log('Seed completed successfully');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
