import { NextRequest, NextResponse } from 'next/server';
import { getAuthUser } from '@/lib/auth';
import prisma from '@/lib/db';
import { supabase } from '@/lib/supabase';

export async function GET(req: NextRequest) {
  try {
    const startTime = Date.now();
    
    // Check database connection
    let dbStatus = 'unknown';
    let dbError = null;
    try {
      // Simple query to check database connection
      await prisma.$queryRaw`SELECT 1`;
      dbStatus = 'connected';
    } catch (error) {
      dbStatus = 'error';
      dbError = error instanceof Error ? error.message : 'Unknown database error';
      console.error('Database health check error:', error);
    }
    
    // Check Supabase connection
    let supabaseStatus = 'unknown';
    let supabaseError = null;
    try {
      const { data, error } = await supabase.from('health_check').select('*').limit(1);
      if (error) throw error;
      supabaseStatus = 'connected';
    } catch (error) {
      supabaseStatus = 'error';
      supabaseError = error instanceof Error ? error.message : 'Unknown Supabase error';
      console.error('Supabase health check error:', error);
    }
    
    // Check authentication (if token is provided)
    let authStatus = 'not_checked';
    let authUser = null;
    const token = req.cookies.get('token')?.value;
    
    if (token) {
      try {
        const user = await getAuthUser(req);
        if (user) {
          authStatus = 'authenticated';
          authUser = {
            id: user.id,
            email: user.email,
            role: user.role,
          };
        } else {
          authStatus = 'invalid_token';
        }
      } catch (error) {
        authStatus = 'error';
        console.error('Auth health check error:', error);
      }
    }
    
    // Get environment info
    const environment = {
      nodeEnv: process.env.NODE_ENV || 'development',
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
      jwtSecret: process.env.JWT_SECRET ? 'configured' : 'missing',
    };
    
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`,
      services: {
        database: {
          status: dbStatus,
          error: dbError,
        },
        supabase: {
          status: supabaseStatus,
          error: supabaseError,
        },
        auth: {
          status: authStatus,
          user: authUser,
        },
      },
      environment,
      cookies: {
        token: token ? 'present' : 'missing',
        authState: req.cookies.get('auth-state')?.value ? 'present' : 'missing',
      },
    });
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
