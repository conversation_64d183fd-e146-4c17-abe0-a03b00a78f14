import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// Server-side Supabase client for App Router
export function createSupabaseServerClient() {
  const cookieStore = cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// Client-side Supabase client for App Router
export function createSupabaseClientComponent() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return document.cookie
            .split(';')
            .map(cookie => cookie.trim().split('='))
            .reduce((acc, [name, value]) => {
              if (name && value) {
                acc.push({ name, value })
              }
              return acc
            }, [] as { name: string; value: string }[])
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            const cookieString = `${name}=${value}; path=${options?.path || '/'}; ${
              options?.maxAge ? `max-age=${options.maxAge};` : ''
            } ${options?.secure ? 'secure;' : ''} ${
              options?.sameSite ? `samesite=${options.sameSite};` : ''
            }`
            document.cookie = cookieString
          })
        },
      },
    }
  )
}

// Middleware Supabase client
export function createSupabaseMiddlewareClient(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  return { supabase, supabaseResponse }
}

// Enhanced authentication helpers
export async function getServerUser() {
  const supabase = createSupabaseServerClient()
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }
    
    return user
  } catch (error) {
    console.error('Error getting server user:', error)
    return null
  }
}

export async function getServerSession() {
  const supabase = createSupabaseServerClient()
  
  try {
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error || !session) {
      return null
    }
    
    return session
  } catch (error) {
    console.error('Error getting server session:', error)
    return null
  }
}

// Enhanced database operations with caching
export async function getProductsSSR(options: {
  page?: number
  limit?: number
  category?: string
  search?: string
  minPrice?: number
  maxPrice?: number
  condition?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
} = {}) {
  const supabase = createSupabaseServerClient()
  
  const {
    page = 1,
    limit = 12,
    category,
    search,
    minPrice,
    maxPrice,
    condition,
    sortBy = 'created_at',
    sortOrder = 'desc',
  } = options

  let query = supabase
    .from('products')
    .select(`
      *,
      categories(*),
      users!seller_id(*),
      product_images(*)
    `)

  // Apply filters
  if (category) {
    query = query.eq('categories.slug', category)
  }

  if (search) {
    query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
  }

  if (minPrice) {
    query = query.gte('price', minPrice)
  }

  if (maxPrice) {
    query = query.lte('price', maxPrice)
  }

  if (condition) {
    query = query.eq('condition', condition)
  }

  // Apply pagination
  const from = (page - 1) * limit
  const to = from + limit - 1

  // Apply sorting
  query = query.order(sortBy, { ascending: sortOrder === 'asc' })

  // Execute query with pagination
  const { data, error, count } = await query
    .range(from, to)
    .limit(limit)

  if (error) {
    console.error('Error fetching products:', error)
    return { products: [], totalCount: 0 }
  }

  return {
    products: data || [],
    totalCount: count || 0,
  }
}

export async function getCategoriesSSR(parentSlug?: string) {
  const supabase = createSupabaseServerClient()
  
  let query = supabase.from('categories').select('*, parent:parent_id(*)')

  if (parentSlug) {
    // Get parent category first
    const { data: parentCategory } = await supabase
      .from('categories')
      .select('id')
      .eq('slug', parentSlug)
      .single()

    if (parentCategory) {
      query = query.eq('parent_id', parentCategory.id)
    }
  } else {
    // Get top-level categories (no parent)
    query = query.is('parent_id', null)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching categories:', error)
    return []
  }

  return data || []
}
