export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          name: string | null
          email: string
          role: 'buyer' | 'seller' | 'admin'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name?: string | null
          email: string
          role?: 'buyer' | 'seller' | 'admin'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string | null
          email?: string
          role?: 'buyer' | 'seller' | 'admin'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      seller_profiles: {
        Row: {
          id: string
          user_id: string
          store_name: string | null
          description: string | null
          location: string | null
          rating: number | null
          verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          store_name?: string | null
          description?: string | null
          location?: string | null
          rating?: number | null
          verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          store_name?: string | null
          description?: string | null
          location?: string | null
          rating?: number | null
          verified?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          image_url: string | null
          parent_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          image_url?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          image_url?: string | null
          parent_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          slug: string
          description: string
          price: number
          compare_at_price: number | null
          main_image: string | null
          condition: 'new' | 'like_new' | 'excellent' | 'good' | 'fair' | 'salvage'
          category_id: string
          seller_id: string
          location: string | null
          is_second_hand: boolean
          usage_history: string | null
          defects_disclosure: string | null
          quantity: number
          status: 'draft' | 'active' | 'inactive' | 'sold'
          views: number
          featured: boolean
          featured_order: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description: string
          price: number
          compare_at_price?: number | null
          main_image?: string | null
          condition: 'new' | 'like_new' | 'excellent' | 'good' | 'fair' | 'salvage'
          category_id: string
          seller_id: string
          location?: string | null
          is_second_hand?: boolean
          usage_history?: string | null
          defects_disclosure?: string | null
          quantity?: number
          status?: 'draft' | 'active' | 'inactive' | 'sold'
          views?: number
          featured?: boolean
          featured_order?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string
          price?: number
          compare_at_price?: number | null
          main_image?: string | null
          condition?: 'new' | 'like_new' | 'excellent' | 'good' | 'fair' | 'salvage'
          category_id?: string
          seller_id?: string
          location?: string | null
          is_second_hand?: boolean
          usage_history?: string | null
          defects_disclosure?: string | null
          quantity?: number
          status?: 'draft' | 'active' | 'inactive' | 'sold'
          views?: number
          featured?: boolean
          featured_order?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      product_images: {
        Row: {
          id: string
          url: string
          alt_text: string | null
          position: number
          product_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          url: string
          alt_text?: string | null
          position?: number
          product_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          url?: string
          alt_text?: string | null
          position?: number
          product_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          user_id: string
          total: number
          status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          shipping_address: Json
          billing_address: Json | null
          payment_id: string | null
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
          shipping_method: string | null
          shipping_cost: number
          tax: number
          discount: number
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          total: number
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          shipping_address: Json
          billing_address?: Json | null
          payment_id?: string | null
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          shipping_method?: string | null
          shipping_cost?: number
          tax?: number
          discount?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          total?: number
          status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          shipping_address?: Json
          billing_address?: Json | null
          payment_id?: string | null
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          shipping_method?: string | null
          shipping_cost?: number
          tax?: number
          discount?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      order_items: {
        Row: {
          id: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          total: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          total: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          order_id?: string
          product_id?: string
          quantity?: number
          price?: number
          total?: number
          created_at?: string
          updated_at?: string
        }
      }
      wishlist: {
        Row: {
          id: string
          user_id: string
          product_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      reviews: {
        Row: {
          id: string
          product_id: string
          user_id: string
          rating: number
          title: string | null
          content: string | null
          status: 'pending' | 'approved' | 'rejected'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          user_id: string
          rating: number
          title?: string | null
          content?: string | null
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          user_id?: string
          rating?: number
          title?: string | null
          content?: string | null
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
