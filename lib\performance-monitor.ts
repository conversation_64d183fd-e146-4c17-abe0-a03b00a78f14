import { NextRequest, NextResponse } from 'next/server'

interface PerformanceMetric {
  id: string
  name: string
  value: number
  unit: string
  timestamp: number
  metadata?: Record<string, any>
}

interface PageLoadMetric {
  url: string
  loadTime: number
  ttfb: number // Time to First Byte
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  cls: number // Cumulative Layout Shift
  fid: number // First Input Delay
  timestamp: number
  userAgent: string
  userId?: string
}

interface APIMetric {
  endpoint: string
  method: string
  statusCode: number
  responseTime: number
  timestamp: number
  userId?: string
  error?: string
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetric[] = []
  private pageLoadMetrics: PageLoadMetric[] = []
  private apiMetrics: APIMetric[] = []
  private maxMetrics = 10000 // Keep last 10k metrics in memory

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // Record a custom performance metric
  recordMetric(name: string, value: number, unit: string, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      value,
      unit,
      timestamp: Date.now(),
      metadata,
    }

    this.metrics.push(metric)
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${value}${unit}`, metadata)
    }
  }

  // Record page load metrics
  recordPageLoad(metric: Omit<PageLoadMetric, 'timestamp'>) {
    const pageLoadMetric: PageLoadMetric = {
      ...metric,
      timestamp: Date.now(),
    }

    this.pageLoadMetrics.push(pageLoadMetric)
    
    if (this.pageLoadMetrics.length > this.maxMetrics) {
      this.pageLoadMetrics = this.pageLoadMetrics.slice(-this.maxMetrics)
    }

    // Record individual metrics
    this.recordMetric('page_load_time', metric.loadTime, 'ms', { url: metric.url })
    this.recordMetric('ttfb', metric.ttfb, 'ms', { url: metric.url })
    this.recordMetric('fcp', metric.fcp, 'ms', { url: metric.url })
    this.recordMetric('lcp', metric.lcp, 'ms', { url: metric.url })
    this.recordMetric('cls', metric.cls, 'score', { url: metric.url })
    this.recordMetric('fid', metric.fid, 'ms', { url: metric.url })
  }

  // Record API performance metrics
  recordAPICall(metric: Omit<APIMetric, 'timestamp'>) {
    const apiMetric: APIMetric = {
      ...metric,
      timestamp: Date.now(),
    }

    this.apiMetrics.push(apiMetric)
    
    if (this.apiMetrics.length > this.maxMetrics) {
      this.apiMetrics = this.apiMetrics.slice(-this.maxMetrics)
    }

    // Record as general metric
    this.recordMetric('api_response_time', metric.responseTime, 'ms', {
      endpoint: metric.endpoint,
      method: metric.method,
      statusCode: metric.statusCode,
    })
  }

  // Get performance summary
  getPerformanceSummary(timeRange: number = 3600000) { // Default: last hour
    const now = Date.now()
    const cutoff = now - timeRange

    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff)
    const recentPageLoads = this.pageLoadMetrics.filter(m => m.timestamp >= cutoff)
    const recentAPICalls = this.apiMetrics.filter(m => m.timestamp >= cutoff)

    // Calculate averages
    const avgPageLoadTime = this.calculateAverage(recentPageLoads.map(m => m.loadTime))
    const avgTTFB = this.calculateAverage(recentPageLoads.map(m => m.ttfb))
    const avgFCP = this.calculateAverage(recentPageLoads.map(m => m.fcp))
    const avgLCP = this.calculateAverage(recentPageLoads.map(m => m.lcp))
    const avgCLS = this.calculateAverage(recentPageLoads.map(m => m.cls))
    const avgFID = this.calculateAverage(recentPageLoads.map(m => m.fid))
    const avgAPIResponseTime = this.calculateAverage(recentAPICalls.map(m => m.responseTime))

    // Calculate error rates
    const totalAPICalls = recentAPICalls.length
    const errorCalls = recentAPICalls.filter(m => m.statusCode >= 400).length
    const errorRate = totalAPICalls > 0 ? (errorCalls / totalAPICalls) * 100 : 0

    // Get slowest endpoints
    const endpointStats = this.groupBy(recentAPICalls, 'endpoint')
    const slowestEndpoints = Object.entries(endpointStats)
      .map(([endpoint, calls]) => ({
        endpoint,
        avgResponseTime: this.calculateAverage(calls.map(c => c.responseTime)),
        callCount: calls.length,
        errorRate: (calls.filter(c => c.statusCode >= 400).length / calls.length) * 100,
      }))
      .sort((a, b) => b.avgResponseTime - a.avgResponseTime)
      .slice(0, 10)

    return {
      timeRange: timeRange / 1000 / 60, // Convert to minutes
      totalMetrics: recentMetrics.length,
      pageLoad: {
        totalPageLoads: recentPageLoads.length,
        avgLoadTime: avgPageLoadTime,
        avgTTFB,
        avgFCP,
        avgLCP,
        avgCLS,
        avgFID,
      },
      api: {
        totalCalls: totalAPICalls,
        avgResponseTime: avgAPIResponseTime,
        errorRate,
        slowestEndpoints,
      },
    }
  }

  // Get metrics by name
  getMetricsByName(name: string, timeRange: number = 3600000) {
    const now = Date.now()
    const cutoff = now - timeRange

    return this.metrics
      .filter(m => m.name === name && m.timestamp >= cutoff)
      .sort((a, b) => a.timestamp - b.timestamp)
  }

  // Clear old metrics
  clearOldMetrics(maxAge: number = 86400000) { // Default: 24 hours
    const cutoff = Date.now() - maxAge

    this.metrics = this.metrics.filter(m => m.timestamp >= cutoff)
    this.pageLoadMetrics = this.pageLoadMetrics.filter(m => m.timestamp >= cutoff)
    this.apiMetrics = this.apiMetrics.filter(m => m.timestamp >= cutoff)
  }

  // Helper methods
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const group = String(item[key])
      groups[group] = groups[group] || []
      groups[group].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }
}

// Middleware for API performance monitoring
export function withPerformanceMonitoring(handler: Function) {
  return async (req: NextRequest, ...args: any[]) => {
    const startTime = Date.now()
    const monitor = PerformanceMonitor.getInstance()
    
    try {
      const response = await handler(req, ...args)
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Record API metric
      monitor.recordAPICall({
        endpoint: req.nextUrl.pathname,
        method: req.method,
        statusCode: response.status || 200,
        responseTime,
        userId: req.headers.get('x-user-id') || undefined,
      })

      return response
    } catch (error) {
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Record error metric
      monitor.recordAPICall({
        endpoint: req.nextUrl.pathname,
        method: req.method,
        statusCode: 500,
        responseTime,
        userId: req.headers.get('x-user-id') || undefined,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      throw error
    }
  }
}

// Client-side performance monitoring
export const clientPerformanceMonitor = {
  // Record Web Vitals
  recordWebVitals: (metric: any) => {
    if (typeof window === 'undefined') return

    const data = {
      name: metric.name,
      value: metric.value,
      id: metric.id,
      url: window.location.href,
      timestamp: Date.now(),
    }

    // Send to analytics endpoint
    fetch('/api/analytics/web-vitals', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }).catch(console.error)
  },

  // Record custom client-side metrics
  recordMetric: (name: string, value: number, metadata?: Record<string, any>) => {
    if (typeof window === 'undefined') return

    const data = {
      name,
      value,
      metadata,
      url: window.location.href,
      timestamp: Date.now(),
    }

    fetch('/api/analytics/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }).catch(console.error)
  },

  // Record page load time
  recordPageLoad: () => {
    if (typeof window === 'undefined') return

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        const data = {
          url: window.location.href,
          loadTime: navigation.loadEventEnd - navigation.fetchStart,
          ttfb: navigation.responseStart - navigation.fetchStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          timestamp: Date.now(),
        }

        fetch('/api/analytics/page-load', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        }).catch(console.error)
      }
    })
  },
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// Cache implementation
class SimpleCache {
  private cache = new Map<string, { value: any; expiry: number }>()

  set(key: string, value: any, ttlSeconds: number = 300) {
    const expiry = Date.now() + (ttlSeconds * 1000)
    this.cache.set(key, { value, expiry })
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }

  delete(key: string) {
    this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  // Clean expired entries
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }
}

export const cache = new SimpleCache()

// Auto-cleanup cache every 5 minutes
if (typeof window === 'undefined') {
  setInterval(() => {
    cache.cleanup()
    performanceMonitor.clearOldMetrics()
  }, 5 * 60 * 1000)
}
