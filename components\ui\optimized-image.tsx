"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { getResponsiveSizes, isExternalImage, generateBlurPlaceholder } from "@/lib/image-utils"

interface OptimizedImageProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
  priority?: boolean
  sizes?: string
  quality?: number
  fill?: boolean
  style?: React.CSSProperties
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down"
  objectPosition?: string
  placeholder?: "blur" | "empty" | "data:image/..."
  blurColor?: string
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  sizes,
  quality = 80,
  fill = false,
  style,
  objectFit = "cover",
  objectPosition = "center",
  placeholder = "empty",
  blurColor = "#f0f0f0",
  onLoad,
  onError,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [blurDataUrl, setBlurDataUrl] = useState<string | undefined>(undefined)

  // Generate blur placeholder if needed
  useEffect(() => {
    if (placeholder === "blur" && !blurDataUrl) {
      setBlurDataUrl(generateBlurPlaceholder(width, height, blurColor))
    }
  }, [placeholder, blurDataUrl, width, height, blurColor])

  // Handle external images
  const external = isExternalImage(src)

  // Default to responsive sizes if not provided
  const imageSizes = sizes || getResponsiveSizes(width)

  // Combine style with objectFit and objectPosition
  const combinedStyle = {
    ...style,
    objectFit,
    objectPosition,
  }

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true)
    if (onLoad) onLoad()
  }

  // Handle image error
  const handleError = () => {
    console.error(`Failed to load image: ${src}`)
    setError(true)
    setIsLoaded(true) // Mark as loaded to remove loading state

    // Try to load a local fallback if the image is external
    if (external && src !== "/placeholder.svg") {
      console.log(`Attempting to load fallback for: ${src}`)
    }

    if (onError) onError()
  }

  return (
    <div
      className={cn(
        "relative overflow-hidden",
        isLoaded ? "animate-none" : "animate-pulse bg-slate-200",
        className
      )}
      style={{
        aspectRatio: `${width} / ${height}`,
        ...(!fill ? { width, height } : {})
      }}
    >
      {error ? (
        <div className="flex items-center justify-center w-full h-full bg-slate-100 text-slate-400 text-sm">
          <div className="flex flex-col items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-2">
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
              <circle cx="9" cy="9" r="2"></circle>
              <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
            </svg>
            {alt || "Image not available"}
          </div>
        </div>
      ) : (
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0",
          )}
          sizes={imageSizes}
          quality={quality}
          priority={priority}
          fill={fill}
          style={combinedStyle}
          onLoad={handleLoad}
          onError={handleError}
          placeholder={blurDataUrl ? "blur" : "empty"}
          blurDataURL={blurDataUrl}
          unoptimized={external}
          {...props}
        />
      )}

      {/* Add a fallback image that's hidden by default but shown when the main image errors */}
      {error && external && (
        <Image
          src="/placeholder.svg"
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          className="absolute inset-0 z-10"
          sizes={imageSizes}
          fill={fill}
          style={combinedStyle}
          unoptimized={false}
        />
      )}
    </div>
  )
}
