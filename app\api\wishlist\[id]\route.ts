import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthUser } from '@/lib/auth';

// Remove a product from the wishlist
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authUser = await getAuthUser(req);
    
    if (!authUser) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    const { id } = params;
    
    // Check if the wishlist item exists and belongs to the user
    const wishlistItem = await prisma.wishlist.findFirst({
      where: {
        productId: id,
        userId: authUser.id,
      },
    });
    
    if (!wishlistItem) {
      return NextResponse.json(
        { error: 'Wishlist item not found' },
        { status: 404 }
      );
    }
    
    // Delete the wishlist item
    await prisma.wishlist.delete({
      where: {
        id: wishlistItem.id,
      },
    });
    
    return NextResponse.json({
      message: 'Product removed from wishlist',
    });
  } catch (error) {
    console.error('Remove from wishlist error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
