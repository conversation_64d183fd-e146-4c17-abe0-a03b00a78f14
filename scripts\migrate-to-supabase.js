#!/usr/bin/env node

/**
 * This script helps migrate data from a Prisma-managed PostgreSQL database to Supabase.
 * It reads data from the Prisma database and inserts it into Supabase.
 * 
 * Prerequisites:
 * - Both Prisma and Supabase must be configured with environment variables
 * - The Supabase schema must already be created (using the schema.sql file)
 * 
 * Usage:
 * node scripts/migrate-to-supabase.js
 */

const { PrismaClient } = require('@prisma/client');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key is missing. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateUsers() {
  console.log('Migrating users...');
  const users = await prisma.user.findMany();
  
  for (const user of users) {
    const { error } = await supabase
      .from('users')
      .insert({
        id: user.id,
        name: user.name,
        email: user.email,
        password: user.password,
        role: user.role,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
        avatar: user.avatar
      });
    
    if (error) {
      console.error(`Error migrating user ${user.id}:`, error);
    } else {
      console.log(`Migrated user: ${user.email}`);
    }
  }
}

async function migrateSellerProfiles() {
  console.log('Migrating seller profiles...');
  const sellerProfiles = await prisma.sellerProfile.findMany();
  
  for (const profile of sellerProfiles) {
    const { error } = await supabase
      .from('seller_profiles')
      .insert({
        id: profile.id,
        user_id: profile.userId,
        store_name: profile.storeName,
        description: profile.description,
        location: profile.location,
        rating: profile.rating,
        verified: profile.verified,
        created_at: profile.createdAt,
        updated_at: profile.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating seller profile ${profile.id}:`, error);
    } else {
      console.log(`Migrated seller profile for user: ${profile.userId}`);
    }
  }
}

async function migrateCategories() {
  console.log('Migrating categories...');
  const categories = await prisma.category.findMany();
  
  for (const category of categories) {
    const { error } = await supabase
      .from('categories')
      .insert({
        id: category.id,
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        parent_id: category.parentId,
        created_at: category.createdAt,
        updated_at: category.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating category ${category.id}:`, error);
    } else {
      console.log(`Migrated category: ${category.name}`);
    }
  }
}

async function migrateProducts() {
  console.log('Migrating products...');
  const products = await prisma.product.findMany();
  
  for (const product of products) {
    const { error } = await supabase
      .from('products')
      .insert({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        original_price: product.originalPrice,
        main_image: product.mainImage,
        condition: product.condition,
        category_id: product.categoryId,
        seller_id: product.sellerId,
        location: product.location,
        is_second_hand: product.isSecondHand,
        usage_history: product.usageHistory,
        defects_disclosure: product.defectsDisclosure,
        quantity: product.quantity,
        status: product.status,
        views: product.views,
        created_at: product.createdAt,
        updated_at: product.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating product ${product.id}:`, error);
    } else {
      console.log(`Migrated product: ${product.name}`);
    }
  }
}

async function migrateProductImages() {
  console.log('Migrating product images...');
  const images = await prisma.productImage.findMany();
  
  for (const image of images) {
    const { error } = await supabase
      .from('product_images')
      .insert({
        id: image.id,
        url: image.url,
        product_id: image.productId,
        created_at: image.createdAt,
        updated_at: image.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating product image ${image.id}:`, error);
    } else {
      console.log(`Migrated product image for product: ${image.productId}`);
    }
  }
}

async function migrateOrders() {
  console.log('Migrating orders...');
  const orders = await prisma.order.findMany();
  
  for (const order of orders) {
    const { error } = await supabase
      .from('orders')
      .insert({
        id: order.id,
        user_id: order.userId,
        total: order.total,
        status: order.status,
        address: order.address,
        payment_id: order.paymentId,
        created_at: order.createdAt,
        updated_at: order.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating order ${order.id}:`, error);
    } else {
      console.log(`Migrated order: ${order.id}`);
    }
  }
}

async function migrateOrderItems() {
  console.log('Migrating order items...');
  const orderItems = await prisma.orderItem.findMany();
  
  for (const item of orderItems) {
    const { error } = await supabase
      .from('order_items')
      .insert({
        id: item.id,
        order_id: item.orderId,
        product_id: item.productId,
        quantity: item.quantity,
        price: item.price,
        created_at: item.createdAt,
        updated_at: item.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating order item ${item.id}:`, error);
    } else {
      console.log(`Migrated order item for order: ${item.orderId}`);
    }
  }
}

async function migrateWishlist() {
  console.log('Migrating wishlist items...');
  const wishlistItems = await prisma.wishlist.findMany();
  
  for (const item of wishlistItems) {
    const { error } = await supabase
      .from('wishlist')
      .insert({
        id: item.id,
        user_id: item.userId,
        product_id: item.productId,
        created_at: item.createdAt,
        updated_at: item.updatedAt
      });
    
    if (error) {
      console.error(`Error migrating wishlist item ${item.id}:`, error);
    } else {
      console.log(`Migrated wishlist item for user: ${item.userId}`);
    }
  }
}

async function main() {
  try {
    console.log('Starting migration from Prisma to Supabase...');
    
    // Migrate in order of dependencies
    await migrateUsers();
    await migrateSellerProfiles();
    await migrateCategories();
    await migrateProducts();
    await migrateProductImages();
    await migrateOrders();
    await migrateOrderItems();
    await migrateWishlist();
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
