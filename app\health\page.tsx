'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/use-auth';

export default function HealthPage() {
  const { toast } = useToast();
  const { user, isLoading } = useAuth();
  const [healthData, setHealthData] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [authStatus, setAuthStatus] = useState<'unknown' | 'success' | 'error'>('unknown');
  const [databaseStatus, setDatabaseStatus] = useState<'unknown' | 'success' | 'error'>('unknown');
  const [supabaseStatus, setSupabaseStatus] = useState<'unknown' | 'success' | 'error'>('unknown');

  const checkHealth = async () => {
    setIsChecking(true);
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      setHealthData(data);
      
      // Update status indicators
      setDatabaseStatus(data.services.database.status === 'connected' ? 'success' : 'error');
      setSupabaseStatus(data.services.supabase.status === 'connected' ? 'success' : 'error');
      setAuthStatus(data.services.auth.status === 'authenticated' ? 'success' : 'error');
      
      toast({
        title: 'Health Check Complete',
        description: 'System health check completed successfully.',
      });
    } catch (error) {
      console.error('Health check error:', error);
      toast({
        title: 'Health Check Failed',
        description: 'Failed to perform system health check.',
        variant: 'destructive',
      });
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkHealth();
  }, []);

  const getStatusBadge = (status: 'unknown' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Healthy</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">System Health Check</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Authentication</CardTitle>
            <CardDescription>JWT-based auth system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span>Status:</span>
              {getStatusBadge(authStatus)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Database</CardTitle>
            <CardDescription>Prisma with SQLite</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span>Status:</span>
              {getStatusBadge(databaseStatus)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Supabase</CardTitle>
            <CardDescription>Cloud database (optional)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <span>Status:</span>
              {getStatusBadge(supabaseStatus)}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="auth">Authentication</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="environment">Environment</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>Overall system health and status</CardDescription>
            </CardHeader>
            <CardContent>
              {healthData ? (
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium">System Status</p>
                    <p className="text-2xl font-bold">{healthData.status}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium">Response Time</p>
                    <p>{healthData.responseTime}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium">Timestamp</p>
                    <p>{new Date(healthData.timestamp).toLocaleString()}</p>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <p className="text-sm font-medium">Current User</p>
                    {isLoading ? (
                      <p>Loading...</p>
                    ) : user ? (
                      <div>
                        <p><strong>Email:</strong> {user.email}</p>
                        <p><strong>Role:</strong> {user.role}</p>
                      </div>
                    ) : (
                      <p>Not authenticated</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">Loading health data...</div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={checkHealth} disabled={isChecking}>
                {isChecking ? 'Checking...' : 'Refresh Health Check'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="auth" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Status</CardTitle>
              <CardDescription>JWT-based authentication system</CardDescription>
            </CardHeader>
            <CardContent>
              {healthData ? (
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium">Auth Status</p>
                    <p className="text-xl font-bold">{healthData.services.auth.status}</p>
                  </div>
                  
                  {healthData.services.auth.user && (
                    <div>
                      <p className="text-sm font-medium">Authenticated User</p>
                      <p><strong>ID:</strong> {healthData.services.auth.user.id}</p>
                      <p><strong>Email:</strong> {healthData.services.auth.user.email}</p>
                      <p><strong>Role:</strong> {healthData.services.auth.user.role}</p>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium">Cookies</p>
                    <p><strong>Token:</strong> {healthData.cookies.token}</p>
                    <p><strong>Auth State:</strong> {healthData.cookies.authState}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">Loading auth data...</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="database" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Database Status</CardTitle>
              <CardDescription>Prisma with SQLite and optional Supabase</CardDescription>
            </CardHeader>
            <CardContent>
              {healthData ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Primary Database</p>
                      <p className="text-xl font-bold">{healthData.services.database.status}</p>
                      {healthData.services.database.error && (
                        <p className="text-red-500">{healthData.services.database.error}</p>
                      )}
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium">Supabase</p>
                      <p className="text-xl font-bold">{healthData.services.supabase.status}</p>
                      {healthData.services.supabase.error && (
                        <p className="text-red-500">{healthData.services.supabase.error}</p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">Loading database data...</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="environment" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Environment</CardTitle>
              <CardDescription>System environment configuration</CardDescription>
            </CardHeader>
            <CardContent>
              {healthData ? (
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium">Node Environment</p>
                    <p className="text-xl font-bold">{healthData.environment.nodeEnv}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">JWT Secret</p>
                      <p>{healthData.environment.jwtSecret}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium">Supabase URL</p>
                      <p>{healthData.environment.supabaseUrl}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">Loading environment data...</div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
