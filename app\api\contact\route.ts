import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema for contact form
const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  inquiryType: z.enum(['general', 'support', 'billing', 'partnership', 'feedback']),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Validate request body
    const result = contactSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: result.error.format() },
        { status: 400 }
      );
    }
    
    const { name, email, subject, message, inquiryType } = result.data;
    
    // In a real application, you would:
    // 1. Store the message in your database
    // 2. Send an email notification to your support team
    // 3. Send a confirmation email to the user
    
    // For now, we'll just simulate success
    
    // Example of how you might store the message in a database:
    /*
    await prisma.contactMessage.create({
      data: {
        name,
        email,
        subject,
        message,
        inquiryType,
      },
    });
    */
    
    // Example of how you might send an email:
    /*
    await sendEmail({
      to: '<EMAIL>',
      subject: `New Contact Form Submission: ${subject}`,
      text: `
        Name: ${name}
        Email: ${email}
        Inquiry Type: ${inquiryType}
        
        Message:
        ${message}
      `,
    });
    */
    
    return NextResponse.json({
      success: true,
      message: 'Your message has been received. We will get back to you soon.',
    });
  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
