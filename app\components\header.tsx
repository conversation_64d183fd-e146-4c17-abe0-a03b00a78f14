"use client"

import Link from "next/link"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Search,
  ShoppingCart,
  Heart,
  User,
  Menu,
  X,
  ChevronDown,
  Laptop,
  Cpu,
  HardDrive,
  Monitor,
  Keyboard,
  Headphones,
  LogOut,
  Shirt,
  Home,
  BookOpen,
  Gamepad2,
  Car,
  Dog,
  Watch,
  Wrench,
  Briefcase,
  Music,
  Dumbbell,
  Palette,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/hooks/use-auth"
import { useCart } from "@/hooks/use-cart"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import NotificationsDropdown from "./notifications-dropdown"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, logout } = useAuth()
  const { itemCount } = useCart()

  // Get initials for avatar fallback
  const getInitials = (name: string | null) => {
    if (!name) return "U"
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <div className="flex items-center gap-2 mr-4">
          <Link href="/" className="flex items-center gap-2">
            <span className="text-xl font-bold text-teal-600">PASSDOWN</span>
          </Link>
        </div>

        <div className="hidden md:flex items-center gap-6 flex-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-1">
                Categories
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-64">
              <div className="grid grid-cols-2 gap-1 p-2">
                <div>
                  <h3 className="font-medium text-sm px-2 py-1 text-slate-500">Electronics</h3>
                  <DropdownMenuItem asChild>
                    <Link href="/category/electronics/smartphones" className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      Smartphones
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/electronics/laptops" className="flex items-center gap-2">
                      <Laptop className="h-4 w-4" />
                      Laptops & Computers
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/electronics/components" className="flex items-center gap-2">
                      <Cpu className="h-4 w-4" />
                      Computer Components
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/electronics/accessories" className="flex items-center gap-2">
                      <Headphones className="h-4 w-4" />
                      Accessories
                    </Link>
                  </DropdownMenuItem>
                </div>
                <div>
                  <h3 className="font-medium text-sm px-2 py-1 text-slate-500">Fashion</h3>
                  <DropdownMenuItem asChild>
                    <Link href="/category/clothing-fashion/mens" className="flex items-center gap-2">
                      <Shirt className="h-4 w-4" />
                      Men's Clothing
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/clothing-fashion/womens" className="flex items-center gap-2">
                      <Shirt className="h-4 w-4" />
                      Women's Clothing
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/jewelry-watches" className="flex items-center gap-2">
                      <Watch className="h-4 w-4" />
                      Jewelry & Watches
                    </Link>
                  </DropdownMenuItem>
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="grid grid-cols-2 gap-1 p-2">
                <div>
                  <h3 className="font-medium text-sm px-2 py-1 text-slate-500">Home & Lifestyle</h3>
                  <DropdownMenuItem asChild>
                    <Link href="/category/home-kitchen" className="flex items-center gap-2">
                      <Home className="h-4 w-4" />
                      Home & Kitchen
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/tools-improvement" className="flex items-center gap-2">
                      <Wrench className="h-4 w-4" />
                      Tools & Improvement
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/pet-supplies" className="flex items-center gap-2">
                      <Dog className="h-4 w-4" />
                      Pet Supplies
                    </Link>
                  </DropdownMenuItem>
                </div>
                <div>
                  <h3 className="font-medium text-sm px-2 py-1 text-slate-500">Entertainment</h3>
                  <DropdownMenuItem asChild>
                    <Link href="/category/books-media" className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Books & Media
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/toys-games" className="flex items-center gap-2">
                      <Gamepad2 className="h-4 w-4" />
                      Toys & Games
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/category/musical-instruments" className="flex items-center gap-2">
                      <Music className="h-4 w-4" />
                      Musical Instruments
                    </Link>
                  </DropdownMenuItem>
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2">
                <DropdownMenuItem asChild>
                  <Link href="/categories" className="flex items-center justify-center text-teal-600">
                    View All Categories
                  </Link>
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          <Link href="/deals" className="text-sm font-medium hover:text-teal-600 transition-colors">
            Deals
          </Link>
          <Link href="/new-arrivals" className="text-sm font-medium hover:text-teal-600 transition-colors">
            New Arrivals
          </Link>
          {user?.role === "seller" || user?.role === "admin" ? (
            <Link href="/sell" className="text-sm font-medium hover:text-teal-600 transition-colors">
              Sell
            </Link>
          ) : (
            <Link href="/register?role=seller" className="text-sm font-medium hover:text-teal-600 transition-colors">
              Become a Seller
            </Link>
          )}
        </div>

        <div className="hidden md:flex items-center gap-2 flex-1 justify-center">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <form action="/search" method="get">
              <Input
                type="search"
                name="q"
                placeholder="Search for products..."
                className="w-full pl-8 pr-4 rounded-full bg-muted"
              />
            </form>
          </div>
        </div>

        <div className="flex items-center gap-4 justify-end flex-1">
          {user ? (
            <>
              <Link
                href="/wishlist"
                className="hidden md:flex items-center justify-center rounded-full w-9 h-9 hover:bg-muted transition-colors"
              >
                <Heart className="h-5 w-5" />
                <span className="sr-only">Wishlist</span>
              </Link>

              <NotificationsDropdown />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full h-9 w-9 hidden md:flex">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar || undefined} alt={user.name || "User"} />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{user.name}</p>
                      <p className="w-[200px] truncate text-sm text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/account">My Account</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders">Orders</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/wishlist">Wishlist</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/compare">Compare Products</Link>
                  </DropdownMenuItem>
                  {user.role === "seller" && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href="/seller/dashboard">Seller Dashboard</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/account/listings">My Listings</Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => logout()} className="text-red-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Button variant="ghost" size="sm" asChild className="hidden md:inline-flex">
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" className="hidden md:inline-flex bg-teal-600 hover:bg-teal-700" asChild>
                <Link href="/register">Register</Link>
              </Button>
            </>
          )}

          <Link
            href="/cart"
            className="flex items-center justify-center rounded-full w-9 h-9 hover:bg-muted transition-colors relative"
          >
            <ShoppingCart className="h-5 w-5" />
            {itemCount > 0 && (
              <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-teal-600 text-[10px] font-medium text-white flex items-center justify-center">
                {itemCount > 99 ? '99+' : itemCount}
              </span>
            )}
            <span className="sr-only">Cart</span>
          </Link>

          <Button variant="ghost" size="icon" className="md:hidden" onClick={() => setIsMenuOpen(true)}>
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 bg-background md:hidden">
          <div className="flex h-16 items-center justify-between px-4 border-b">
            <Link href="/" className="flex items-center gap-2">
              <span className="text-xl font-bold text-teal-600">PASSDOWN</span>
            </Link>
            <Button variant="ghost" size="icon" onClick={() => setIsMenuOpen(false)}>
              <X className="h-5 w-5" />
              <span className="sr-only">Close menu</span>
            </Button>
          </div>
          <div className="p-4 space-y-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <form action="/search" method="get">
                <Input
                  type="search"
                  name="q"
                  placeholder="Search for products..."
                  className="w-full pl-8 pr-4 rounded-full bg-muted"
                />
              </form>
            </div>

            {user && (
              <div className="flex items-center gap-3 p-4 border rounded-lg">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={user.avatar || undefined} alt={user.name || "User"} />
                  <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>
            )}

            <div className="space-y-1">
              <p className="text-sm font-medium text-slate-500 px-3 py-2">Electronics</p>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/electronics/smartphones" className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  Smartphones
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/electronics/laptops" className="flex items-center gap-2">
                  <Laptop className="h-4 w-4" />
                  Laptops & Computers
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/electronics/components" className="flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  Computer Components
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/electronics/accessories" className="flex items-center gap-2">
                  <Headphones className="h-4 w-4" />
                  Accessories
                </Link>
              </Button>

              <p className="text-sm font-medium text-slate-500 px-3 py-2 mt-4">Fashion</p>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/clothing-fashion/mens" className="flex items-center gap-2">
                  <Shirt className="h-4 w-4" />
                  Men's Clothing
                </Link>
              </Button>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/clothing-fashion/womens" className="flex items-center gap-2">
                  <Shirt className="h-4 w-4" />
                  Women's Clothing
                </Link>
              </Button>

              <p className="text-sm font-medium text-slate-500 px-3 py-2 mt-4">Home & Lifestyle</p>
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link href="/category/home-kitchen" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Home & Kitchen
                </Link>
              </Button>

              <div className="pt-4">
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link href="/deals" className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Deals
                  </Link>
                </Button>
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link href="/new-arrivals" className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    New Arrivals
                  </Link>
                </Button>
                <Button variant="outline" className="w-full justify-center mt-2" asChild>
                  <Link href="/categories">View All Categories</Link>
                </Button>
              </div>

              <div className="pt-4">
                {user?.role === "seller" || user?.role === "admin" ? (
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/sell">Sell</Link>
                  </Button>
                ) : (
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/register?role=seller">Become a Seller</Link>
                  </Button>
                )}
              </div>
            </div>
            <div className="space-y-1 pt-4 border-t">
              {user ? (
                <>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/account" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      My Account
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/account/orders" className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      My Orders
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/wishlist" className="flex items-center gap-2">
                      <Heart className="h-4 w-4" />
                      Wishlist
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/account/notifications" className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      Notifications
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/compare" className="flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      Compare Products
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/cart" className="flex items-center gap-2">
                      <ShoppingCart className="h-4 w-4" />
                      Cart
                    </Link>
                  </Button>
                  {user?.role === "seller" && (
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link href="/seller/dashboard" className="flex items-center gap-2">
                        <Laptop className="h-4 w-4" />
                        Seller Dashboard
                      </Link>
                    </Button>
                  )}
                  <Button variant="ghost" className="w-full justify-start text-red-600" onClick={() => logout()}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link href="/login">Login</Link>
                  </Button>
                  <Button className="w-full bg-teal-600 hover:bg-teal-700" asChild>
                    <Link href="/register">Register</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
