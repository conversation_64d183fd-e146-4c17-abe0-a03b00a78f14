import { supabase, isSupabaseConfigured } from './supabase';
import prisma from './db';

/**
 * A wrapper for database operations that tries Supabase first,
 * then falls back to local Prisma if Supabase is not configured
 * or if the Supabase operation fails.
 */
export class DbClient {
  /**
   * Check if we should use Supabase or fall back to Prisma
   */
  private static shouldUseSupabase(): boolean {
    return isSupabaseConfigured();
  }

  /**
   * Log database operation for debugging
   */
  private static logOperation(operation: string, table: string, useSupabase: boolean): void {
    console.log(`[DB] ${operation} on ${table} using ${useSupabase ? 'Supabase' : 'Prisma'}`);
  }

  /**
   * Find a single record by ID
   */
  static async findById(table: string, id: string, options: any = {}) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('findById', table, useSupabase);

    try {
      if (useSupabase) {
        // Convert Prisma-style options to Supabase format
        const query = supabase.from(table).select(this.formatSelect(options));

        // Add filters
        const { data, error } = await query.eq('id', id).single();

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error(`[DB] Supabase findById error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].findUnique({
      where: { id },
      ...options,
    });
  }

  /**
   * Find a single record by a field value
   */
  static async findByField(table: string, field: string, value: any, options: any = {}) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('findByField', table, useSupabase);

    try {
      if (useSupabase) {
        // Convert Prisma-style options to Supabase format
        const query = supabase.from(table).select(this.formatSelect(options));

        // Add filters
        const { data, error } = await query.eq(field, value).single();

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error(`[DB] Supabase findByField error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].findFirst({
      where: { [field]: value },
      ...options,
    });
  }

  /**
   * Find multiple records with optional filtering
   */
  static async findMany(table: string, options: any = {}) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('findMany', table, useSupabase);

    try {
      if (useSupabase) {
        // Convert Prisma-style options to Supabase format
        let query = supabase.from(table).select(this.formatSelect(options));

        // Add filters if provided
        if (options.where) {
          query = this.applyFilters(query, options.where);
        }

        // Add ordering
        if (options.orderBy) {
          query = this.applyOrderBy(query, options.orderBy);
        }

        // Add pagination
        if (options.skip) {
          query = query.range(options.skip, options.skip + (options.take || 10) - 1);
        } else if (options.take) {
          query = query.limit(options.take);
        }

        const { data, error } = await query;

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error(`[DB] Supabase findMany error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].findMany(options);
  }

  /**
   * Create a new record
   */
  static async create(table: string, data: any) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('create', table, useSupabase);

    try {
      if (useSupabase) {
        // Convert Prisma-style nested creates to Supabase format
        const formattedData = this.formatCreateData(data);

        const { data: result, error } = await supabase
          .from(table)
          .insert(formattedData)
          .select()
          .single();

        if (error) throw error;
        return result;
      }
    } catch (error) {
      console.error(`[DB] Supabase create error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].create({
      data,
    });
  }

  /**
   * Update an existing record
   */
  static async update(table: string, id: string, data: any) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('update', table, useSupabase);

    try {
      if (useSupabase) {
        // Add updated_at timestamp
        const updateData = {
          ...data,
          updated_at: new Date().toISOString(),
        };

        const { data: result, error } = await supabase
          .from(table)
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

        if (error) throw error;
        return result;
      }
    } catch (error) {
      console.error(`[DB] Supabase update error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a record
   */
  static async delete(table: string, id: string) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('delete', table, useSupabase);

    try {
      if (useSupabase) {
        const { data, error } = await supabase
          .from(table)
          .delete()
          .eq('id', id)
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    } catch (error) {
      console.error(`[DB] Supabase delete error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].delete({
      where: { id },
    });
  }

  /**
   * Count records with optional filtering
   */
  static async count(table: string, where: any = {}) {
    const useSupabase = this.shouldUseSupabase();
    this.logOperation('count', table, useSupabase);

    try {
      if (useSupabase) {
        let query = supabase.from(table).select('*', { count: 'exact', head: true });

        // Add filters if provided
        query = this.applyFilters(query, where);

        const { count, error } = await query;

        if (error) throw error;
        return count || 0;
      }
    } catch (error) {
      console.error(`[DB] Supabase count error:`, error);
      // Fall back to Prisma
    }

    // Prisma fallback
    return prisma[this.toPrismaModelName(table)].count({
      where,
    });
  }

  /**
   * Helper to convert table name to Prisma model name
   */
  private static toPrismaModelName(table: string): string {
    // Convert snake_case to camelCase and handle pluralization
    const singular = table.endsWith('s') ? table.slice(0, -1) : table;
    return singular.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Helper to format Prisma-style select options to Supabase format
   */
  private static formatSelect(options: any): string {
    if (!options.include) return '*';

    // Start with all fields from the main table
    let select = '*';

    // Add related tables
    for (const [relation, value] of Object.entries(options.include)) {
      if (value === true) {
        // Simple inclusion
        select += `,${relation}(*)`;
      } else if (typeof value === 'object') {
        // Nested inclusion with its own select/include
        select += `,${relation}(${this.formatSelect(value)})`;
      }
    }

    return select;
  }

  /**
   * Helper to apply Prisma-style filters to Supabase query
   */
  private static applyFilters(query: any, where: any): any {
    for (const [field, value] of Object.entries(where)) {
      if (value === null) {
        query = query.is(field, null);
      } else if (typeof value === 'object') {
        // Handle operators like equals, not, in, etc.
        for (const [op, opValue] of Object.entries(value)) {
          switch (op) {
            case 'equals':
              query = query.eq(field, opValue);
              break;
            case 'not':
              query = query.neq(field, opValue);
              break;
            case 'in':
              query = query.in(field, opValue);
              break;
            case 'notIn':
              query = query.not(field, 'in', opValue);
              break;
            case 'lt':
              query = query.lt(field, opValue);
              break;
            case 'lte':
              query = query.lte(field, opValue);
              break;
            case 'gt':
              query = query.gt(field, opValue);
              break;
            case 'gte':
              query = query.gte(field, opValue);
              break;
            case 'contains':
              query = query.ilike(field, `%${opValue}%`);
              break;
            case 'startsWith':
              query = query.ilike(field, `${opValue}%`);
              break;
            case 'endsWith':
              query = query.ilike(field, `%${opValue}`);
              break;
          }
        }
      } else {
        // Simple equality
        query = query.eq(field, value);
      }
    }
    return query;
  }

  /**
   * Helper to apply Prisma-style orderBy to Supabase query
   */
  private static applyOrderBy(query: any, orderBy: any): any {
    if (Array.isArray(orderBy)) {
      // Handle array of order specifications
      for (const order of orderBy) {
        for (const [field, direction] of Object.entries(order)) {
          query = query.order(field, { ascending: direction === 'asc' });
        }
      }
    } else {
      // Handle single order specification
      for (const [field, direction] of Object.entries(orderBy)) {
        query = query.order(field, { ascending: direction === 'asc' });
      }
    }
    return query;
  }

  /**
   * Helper to format Prisma-style create data to Supabase format
   */
  private static formatCreateData(data: any): any {
    const result = { ...data };

    // Handle nested creates
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'object' && value !== null && 'create' in value) {
        // This is a nested create, which we'll need to handle separately
        delete result[key];
      }
    }

    return result;
  }
}
