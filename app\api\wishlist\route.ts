import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import { getAuthUser } from '@/lib/auth';

// Get all wishlist items for the current user
export async function GET(req: NextRequest) {
  try {
    const authUser = await getAuthUser(req);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const wishlistItems = await prisma.wishlist.findMany({
      where: {
        userId: authUser.id,
      },
      include: {
        product: {
          include: {
            category: true,
            seller: {
              select: {
                id: true,
                name: true,
                avatar: true,
                sellerProfile: {
                  select: {
                    rating: true,
                    verified: true,
                  },
                },
              },
            },
            images: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform wishlist items for response
    const transformedItems = wishlistItems.map(item => ({
      id: item.id,
      productId: item.productId,
      product: {
        id: item.product.id,
        name: item.product.name,
        description: item.product.description,
        price: item.product.price,
        originalPrice: item.product.originalPrice,
        image: item.product.mainImage || item.product.images[0]?.url || '/placeholder.svg?height=400&width=400',
        condition: item.product.condition,
        category: item.product.category.slug,
        seller: item.product.seller.name || 'Unknown Seller',
        location: item.product.location,
        sellerInfo: {
          id: item.product.seller.id,
          name: item.product.seller.name,
          avatar: item.product.seller.avatar,
          rating: item.product.seller.sellerProfile?.rating,
          verified: item.product.seller.sellerProfile?.verified,
        },
      },
      createdAt: item.createdAt,
    }));

    return NextResponse.json(transformedItems);
  } catch (error) {
    console.error('Get wishlist error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}

// Add a product to the wishlist
export async function POST(req: NextRequest) {
  try {
    const authUser = await getAuthUser(req);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { productId } = body;

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check if product is already in wishlist
    const existingWishlistItem = await prisma.wishlist.findUnique({
      where: {
        userId_productId: {
          userId: authUser.id,
          productId,
        },
      },
    });

    if (existingWishlistItem) {
      return NextResponse.json(
        { error: 'Product is already in wishlist' },
        { status: 400 }
      );
    }

    // Add product to wishlist
    const wishlistItem = await prisma.wishlist.create({
      data: {
        userId: authUser.id,
        productId,
      },
    });

    return NextResponse.json({
      message: 'Product added to wishlist',
      wishlistItem,
    });
  } catch (error) {
    console.error('Add to wishlist error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}

// Remove a product from the wishlist
export async function DELETE(req: NextRequest) {
  try {
    const authUser = await getAuthUser(req);

    if (!authUser) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { productId } = body;

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Check if product is in wishlist
    const existingWishlistItem = await prisma.wishlist.findUnique({
      where: {
        userId_productId: {
          userId: authUser.id,
          productId,
        },
      },
    });

    if (!existingWishlistItem) {
      return NextResponse.json(
        { error: 'Product not found in wishlist' },
        { status: 404 }
      );
    }

    // Remove product from wishlist
    await prisma.wishlist.delete({
      where: {
        userId_productId: {
          userId: authUser.id,
          productId,
        },
      },
    });

    return NextResponse.json({
      message: 'Product removed from wishlist',
    });
  } catch (error) {
    console.error('Remove from wishlist error:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
