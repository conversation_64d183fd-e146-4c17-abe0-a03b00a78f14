"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { LazyImage } from "@/components/ui/lazy-image"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { 
  X, 
  Plus, 
  Check, 
  ShoppingCart, 
  MoreHorizontal,
  Heart,
  Share2,
  Star,
  Info
} from "lucide-react"
import { useCart } from "@/hooks/use-cart"
import type { Product } from "@/app/types"

interface ProductComparisonProps {
  products: Product[]
  onRemoveProduct: (productId: string) => void
  onAddProduct: () => void
  maxProducts?: number
}

export default function ProductComparison({
  products,
  onRemoveProduct,
  onAddProduct,
  maxProducts = 4
}: ProductComparisonProps) {
  const { addItem } = useCart()
  const [addingToCart, setAddingToCart] = useState<Record<string, boolean>>({})
  
  // Get all unique specs from all products
  const getUniqueSpecs = () => {
    const specs = new Set<string>()
    
    products.forEach(product => {
      // In a real app, you would have a specs object
      // For now, we'll create some mock specs based on the product properties
      specs.add("Brand")
      specs.add("Model")
      specs.add("Condition")
      specs.add("Price")
      specs.add("Category")
      specs.add("Warranty")
      specs.add("In Stock")
      specs.add("Shipping")
      specs.add("Return Policy")
    })
    
    return Array.from(specs)
  }
  
  const specs = getUniqueSpecs()
  
  // Get spec value for a product
  const getSpecValue = (product: Product, spec: string) => {
    // In a real app, you would get this from the product's specs object
    // For now, we'll return mock values based on the spec name
    switch (spec) {
      case "Brand":
        return product.seller || "Unknown"
      case "Model":
        return product.name.split(' ')[0] || "Unknown"
      case "Condition":
        return product.condition
      case "Price":
        return `₹${product.price.toLocaleString("en-IN")}`
      case "Category":
        return product.category
      case "Warranty":
        return product.condition === "New" ? "1 Year" : "30 Days"
      case "In Stock":
        return "Yes"
      case "Shipping":
        return "Free"
      case "Return Policy":
        return "30 Days"
      default:
        return "N/A"
    }
  }
  
  // Handle add to cart
  const handleAddToCart = (product: Product) => {
    setAddingToCart(prev => ({ ...prev, [product.id]: true }))
    addItem(product, 1)
    setTimeout(() => {
      setAddingToCart(prev => ({ ...prev, [product.id]: false }))
    }, 1000)
  }
  
  // Check if specs are different
  const areSpecsDifferent = (spec: string) => {
    if (products.length <= 1) return false
    
    const firstValue = getSpecValue(products[0], spec)
    return products.some(product => getSpecValue(product, spec) !== firstValue)
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Product Comparison</h2>
        <Button 
          variant="outline" 
          onClick={onAddProduct}
          disabled={products.length >= maxProducts}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Product
        </Button>
      </div>
      
      {products.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="flex flex-col items-center">
              <div className="rounded-full bg-slate-100 p-3 mb-4">
                <Plus className="h-6 w-6 text-slate-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">No products to compare</h3>
              <p className="text-slate-500 mb-6 max-w-md">
                Add products to compare their features and specifications side by side.
              </p>
              <Button onClick={onAddProduct}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="overflow-x-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow>
                <TableHead className="w-40 bg-slate-50">Product</TableHead>
                {products.map((product) => (
                  <TableHead key={product.id} className="min-w-[250px]">
                    <div className="relative pb-4">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-0 right-0 h-6 w-6 rounded-full"
                        onClick={() => onRemoveProduct(product.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                      
                      <div className="flex flex-col items-center">
                        <div className="relative w-32 h-32 mb-3">
                          <LazyImage
                            src={product.image || "/placeholder.svg"}
                            alt={product.name}
                            width={128}
                            height={128}
                            className="object-cover rounded-md"
                          />
                        </div>
                        
                        <Link 
                          href={`/product/${product.id}`}
                          className="text-center font-medium hover:text-teal-600 transition-colors line-clamp-2 min-h-[48px]"
                        >
                          {product.name}
                        </Link>
                        
                        <div className="flex items-center gap-1 mt-1 mb-2">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-4 w-4 ${
                                star <= 4 ? "text-yellow-400 fill-yellow-400" : "text-slate-200"
                              }`}
                            />
                          ))}
                          <span className="text-xs text-slate-500 ml-1">(24)</span>
                        </div>
                        
                        <div className="flex flex-col items-center gap-1">
                          <span className="font-bold text-teal-600">
                            ₹{product.price.toLocaleString("en-IN")}
                          </span>
                          {product.originalPrice && (
                            <span className="text-sm text-slate-500 line-through">
                              ₹{product.originalPrice.toLocaleString("en-IN")}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex gap-2 mt-4">
                          <Button
                            size="sm"
                            className="bg-teal-600 hover:bg-teal-700"
                            onClick={() => handleAddToCart(product)}
                            disabled={addingToCart[product.id]}
                          >
                            {addingToCart[product.id] ? (
                              <>
                                <Check className="h-4 w-4 mr-1" />
                                Added
                              </>
                            ) : (
                              <>
                                <ShoppingCart className="h-4 w-4 mr-1" />
                                Add
                              </>
                            )}
                          </Button>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Heart className="h-4 w-4 mr-2" />
                                Add to Wishlist
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {specs.map((spec) => (
                <TableRow key={spec} className={areSpecsDifferent(spec) ? "bg-yellow-50" : ""}>
                  <TableCell className="font-medium bg-slate-50 flex items-center gap-1">
                    {spec}
                    {areSpecsDifferent(spec) && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-amber-500" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Values differ between products</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </TableCell>
                  {products.map((product) => (
                    <TableCell key={`${product.id}-${spec}`}>
                      {getSpecValue(product, spec)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
