import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Clock, Tag, ArrowRight, Star } from "lucide-react"
import ProductCard from "@/app/components/product-card"
import { allProducts } from "@/app/data/enhanced-products"
import type { Product } from "@/app/types"

// Sort products by newest first (in a real app, this would use createdAt date)
// For mock data, we'll just use the array order as a proxy for recency
const newArrivals = [...allProducts].slice(0, 12)

// Group products by category
const productsByCategory = newArrivals.reduce((acc, product) => {
  const category = product.category
  if (!acc[category]) {
    acc[category] = []
  }
  acc[category].push(product)
  return acc
}, {} as Record<string, Product[]>)

// Get featured new arrivals (first 4 products)
const featuredNewArrivals = newArrivals.slice(0, 4)

// Get trending new arrivals (random selection)
const trendingNewArrivals = [...newArrivals]
  .sort(() => 0.5 - Math.random())
  .slice(0, 4)

export default function NewArrivalsPage() {
  return (
    <div className="container px-4 py-8 md:py-12">
      {/* Hero Section */}
      <div className="relative rounded-xl overflow-hidden mb-12">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900 to-slate-800/80 z-10"></div>
        <Image
          src="/placeholder.svg?height=400&width=1200"
          alt="New Arrivals"
          width={1200}
          height={400}
          className="w-full h-64 md:h-80 object-cover"
        />
        <div className="absolute inset-0 z-20 flex flex-col justify-center px-6 md:px-12">
          <div className="max-w-2xl">
            <Badge className="mb-4 bg-teal-500 hover:bg-teal-600">Just Landed</Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
              New Arrivals
            </h1>
            <p className="text-slate-200 mb-6 max-w-lg">
              Discover the latest tech components that just arrived on our marketplace. Be the first to get your hands on these fresh listings!
            </p>
            <Button className="bg-teal-500 hover:bg-teal-600 text-white">
              Explore All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* New Arrivals Tabs */}
      <Tabs defaultValue="featured" className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl md:text-3xl font-bold">Latest Additions</h2>
          <TabsList>
            <TabsTrigger value="featured">Featured</TabsTrigger>
            <TabsTrigger value="trending">Trending</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="featured" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredNewArrivals.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="trending" className="mt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {trendingNewArrivals.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* New Arrivals by Category */}
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">New Arrivals by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.keys(productsByCategory).slice(0, 4).map((category) => {
            const formattedCategory = category
              .split("-")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")
            
            return (
              <Link
                key={category}
                href={`/category/${category}?sort=newest`}
                className="group relative rounded-lg overflow-hidden"
              >
                <div className="absolute inset-0 bg-black/50 group-hover:bg-black/60 transition-colors z-10"></div>
                <Image
                  src="/placeholder.svg?height=200&width=200"
                  alt={formattedCategory}
                  width={200}
                  height={200}
                  className="w-full aspect-square object-cover"
                />
                <div className="absolute inset-0 z-20 flex items-center justify-center">
                  <h3 className="text-white font-semibold text-lg md:text-xl">{formattedCategory}</h3>
                </div>
              </Link>
            )
          })}
        </div>
      </section>

      {/* Featured New Arrival */}
      {featuredNewArrivals[0] && (
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Featured New Arrival</h2>
          <Card className="overflow-hidden">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="relative aspect-square md:aspect-auto">
                <Image
                  src={featuredNewArrivals[0].image || "/placeholder.svg"}
                  alt={featuredNewArrivals[0].name}
                  fill
                  className="object-cover"
                />
                <Badge className="absolute top-4 left-4 bg-teal-500 hover:bg-teal-600 text-white">
                  New Arrival
                </Badge>
              </div>
              <CardContent className="p-6 flex flex-col justify-center">
                <div className="mb-2">
                  <Badge variant="outline" className="text-teal-600 border-teal-600">
                    <Clock className="mr-1 h-3 w-3" />
                    Just Added
                  </Badge>
                </div>
                <h3 className="text-2xl font-bold mb-2">{featuredNewArrivals[0].name}</h3>
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-2xl font-bold text-teal-600">
                    ₹{featuredNewArrivals[0].price.toLocaleString("en-IN")}
                  </span>
                  {featuredNewArrivals[0].originalPrice && (
                    <span className="text-lg text-slate-500 line-through">
                      ₹{featuredNewArrivals[0].originalPrice.toLocaleString("en-IN")}
                    </span>
                  )}
                </div>
                <p className="text-slate-600 mb-6">{featuredNewArrivals[0].description}</p>
                <div className="flex gap-3">
                  <Button className="bg-teal-600 hover:bg-teal-700">Add to Cart</Button>
                  <Button variant="outline">View Details</Button>
                </div>
              </CardContent>
            </div>
          </Card>
        </section>
      )}

      {/* All New Arrivals */}
      <section>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">All New Arrivals</h2>
          <Button variant="outline" asChild>
            <Link href="/search?sort=newest">View All</Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {newArrivals.slice(0, 8).map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </section>
    </div>
  )
}
