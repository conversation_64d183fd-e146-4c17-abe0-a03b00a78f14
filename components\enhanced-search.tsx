"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import {
  Search,
  Filter,
  X,
  SlidersHorizontal,
  MapPin,
  Star,
  Calendar,
  TrendingUp,
  ChevronDown,
  Check
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useDebounce } from "@/hooks/use-debounce"

interface SearchFilters {
  query: string
  category: string
  minPrice: number
  maxPrice: number
  condition: string[]
  location: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
  isSecondHand: boolean | null
}

interface EnhancedSearchProps {
  onFiltersChange: (filters: SearchFilters) => void
  categories: Array<{ id: string; name: string; slug: string }>
  locations: string[]
  priceRange: { min: number; max: number }
  className?: string
}

const conditionOptions = [
  { value: "New", label: "New", color: "bg-green-100 text-green-800" },
  { value: "Like New", label: "Like New", color: "bg-blue-100 text-blue-800" },
  { value: "Good", label: "Good", color: "bg-yellow-100 text-yellow-800" },
  { value: "Fair", label: "Fair", color: "bg-orange-100 text-orange-800" },
  { value: "Poor", label: "Poor", color: "bg-red-100 text-red-800" }
]

const sortOptions = [
  { value: "created_at", label: "Newest First", order: "desc" },
  { value: "created_at", label: "Oldest First", order: "asc" },
  { value: "price", label: "Price: Low to High", order: "asc" },
  { value: "price", label: "Price: High to Low", order: "desc" },
  { value: "name", label: "Name: A to Z", order: "asc" },
  { value: "name", label: "Name: Z to A", order: "desc" },
  { value: "views", label: "Most Popular", order: "desc" }
]

export function EnhancedSearch({
  onFiltersChange,
  categories,
  locations,
  priceRange,
  className
}: EnhancedSearchProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [filters, setFilters] = useState<SearchFilters>({
    query: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    minPrice: parseInt(searchParams.get('minPrice') || String(priceRange.min)),
    maxPrice: parseInt(searchParams.get('maxPrice') || String(priceRange.max)),
    condition: searchParams.get('condition')?.split(',').filter(Boolean) || [],
    location: searchParams.get('location') || '',
    sortBy: searchParams.get('sortBy') || 'created_at',
    sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    isSecondHand: searchParams.get('isSecondHand') ? searchParams.get('isSecondHand') === 'true' : null
  })

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [categoryOpen, setCategoryOpen] = useState(false)
  const [locationOpen, setLocationOpen] = useState(false)

  // Debounce search query
  const debouncedQuery = useDebounce(filters.query, 300)

  // Update filters when debounced query changes
  useEffect(() => {
    if (debouncedQuery !== filters.query) {
      setFilters(prev => ({ ...prev, query: debouncedQuery }))
    }
  }, [debouncedQuery, filters.query])

  // Notify parent component when filters change
  useEffect(() => {
    onFiltersChange(filters)
    updateURL()
  }, [filters, onFiltersChange])

  const updateURL = useCallback(() => {
    const params = new URLSearchParams()
    
    if (filters.query) params.set('q', filters.query)
    if (filters.category) params.set('category', filters.category)
    if (filters.minPrice !== priceRange.min) params.set('minPrice', String(filters.minPrice))
    if (filters.maxPrice !== priceRange.max) params.set('maxPrice', String(filters.maxPrice))
    if (filters.condition.length > 0) params.set('condition', filters.condition.join(','))
    if (filters.location) params.set('location', filters.location)
    if (filters.sortBy !== 'created_at') params.set('sortBy', filters.sortBy)
    if (filters.sortOrder !== 'desc') params.set('sortOrder', filters.sortOrder)
    if (filters.isSecondHand !== null) params.set('isSecondHand', String(filters.isSecondHand))

    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname
    router.replace(newURL, { scroll: false })
  }, [filters, priceRange, router])

  const clearFilters = () => {
    setFilters({
      query: '',
      category: '',
      minPrice: priceRange.min,
      maxPrice: priceRange.max,
      condition: [],
      location: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
      isSecondHand: null
    })
  }

  const removeFilter = (type: string, value?: string) => {
    switch (type) {
      case 'category':
        setFilters(prev => ({ ...prev, category: '' }))
        break
      case 'condition':
        setFilters(prev => ({ 
          ...prev, 
          condition: prev.condition.filter(c => c !== value) 
        }))
        break
      case 'location':
        setFilters(prev => ({ ...prev, location: '' }))
        break
      case 'price':
        setFilters(prev => ({ 
          ...prev, 
          minPrice: priceRange.min, 
          maxPrice: priceRange.max 
        }))
        break
      case 'isSecondHand':
        setFilters(prev => ({ ...prev, isSecondHand: null }))
        break
    }
  }

  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (filters.category) count++
    if (filters.condition.length > 0) count += filters.condition.length
    if (filters.location) count++
    if (filters.minPrice !== priceRange.min || filters.maxPrice !== priceRange.max) count++
    if (filters.isSecondHand !== null) count++
    return count
  }, [filters, priceRange])

  const selectedCategory = categories.find(cat => cat.slug === filters.category)
  const selectedLocation = filters.location
  const selectedSort = sortOptions.find(opt => 
    opt.value === filters.sortBy && opt.order === filters.sortOrder
  )

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4 space-y-4">
        {/* Main Search Bar */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search products..."
              value={filters.query}
              onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="flex items-center gap-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </div>

        {/* Active Filters */}
        {activeFiltersCount > 0 && (
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm text-gray-600">Active filters:</span>
            
            {selectedCategory && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {selectedCategory.name}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('category')}
                />
              </Badge>
            )}
            
            {filters.condition.map(condition => (
              <Badge key={condition} variant="secondary" className="flex items-center gap-1">
                {condition}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('condition', condition)}
                />
              </Badge>
            ))}
            
            {selectedLocation && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                {selectedLocation}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('location')}
                />
              </Badge>
            )}
            
            {(filters.minPrice !== priceRange.min || filters.maxPrice !== priceRange.max) && (
              <Badge variant="secondary" className="flex items-center gap-1">
                ₹{filters.minPrice.toLocaleString()} - ₹{filters.maxPrice.toLocaleString()}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('price')}
                />
              </Badge>
            )}
            
            {filters.isSecondHand !== null && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.isSecondHand ? 'Second Hand' : 'New Items'}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter('isSecondHand')}
                />
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-red-600 hover:text-red-700"
            >
              Clear all
            </Button>
          </div>
        )}

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <>
            <Separator />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Popover open={categoryOpen} onOpenChange={setCategoryOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={categoryOpen}
                      className="w-full justify-between"
                    >
                      {selectedCategory ? selectedCategory.name : "All Categories"}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search categories..." />
                      <CommandList>
                        <CommandEmpty>No category found.</CommandEmpty>
                        <CommandGroup>
                          <CommandItem
                            onSelect={() => {
                              setFilters(prev => ({ ...prev, category: '' }))
                              setCategoryOpen(false)
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                !filters.category ? "opacity-100" : "opacity-0"
                              )}
                            />
                            All Categories
                          </CommandItem>
                          {categories.map((category) => (
                            <CommandItem
                              key={category.id}
                              onSelect={() => {
                                setFilters(prev => ({ ...prev, category: category.slug }))
                                setCategoryOpen(false)
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  filters.category === category.slug ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {category.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Price Range */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Price Range</label>
                <div className="px-2">
                  <Slider
                    value={[filters.minPrice, filters.maxPrice]}
                    onValueChange={([min, max]) => 
                      setFilters(prev => ({ ...prev, minPrice: min, maxPrice: max }))
                    }
                    max={priceRange.max}
                    min={priceRange.min}
                    step={1000}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>₹{filters.minPrice.toLocaleString()}</span>
                    <span>₹{filters.maxPrice.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Condition Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Condition</label>
                <div className="space-y-2">
                  {conditionOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.value}
                        checked={filters.condition.includes(option.value)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setFilters(prev => ({ 
                              ...prev, 
                              condition: [...prev.condition, option.value] 
                            }))
                          } else {
                            setFilters(prev => ({ 
                              ...prev, 
                              condition: prev.condition.filter(c => c !== option.value) 
                            }))
                          }
                        }}
                      />
                      <label
                        htmlFor={option.value}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <Select
                  value={`${filters.sortBy}-${filters.sortOrder}`}
                  onValueChange={(value) => {
                    const [sortBy, sortOrder] = value.split('-')
                    setFilters(prev => ({ 
                      ...prev, 
                      sortBy, 
                      sortOrder: sortOrder as 'asc' | 'desc' 
                    }))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sort by..." />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem 
                        key={`${option.value}-${option.order}`} 
                        value={`${option.value}-${option.order}`}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
