"use client"

import Image from "next/image"
import Link from "next/link"
import { useState, useEffect } from "react"
import { allExpandedProducts } from "@/app/data/expanded-products"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  ShoppingCart,
  Share2,
  Shield,
  Truck,
  RotateCcw,
  MapPin,
  Star,
  AlertCircle,
  Check
} from "lucide-react"
import ProductCard from "@/app/components/product-card"
import { useCart } from "@/hooks/use-cart"
import { supabase } from "@/lib/supabase"
import type { Product } from "@/app/types"

export default function ProductPage({ params }: { params: { id: string } }) {
  const { id } = params
  const { addItem } = useCart()
  const [quantity, setQuantity] = useState(1)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [product, setProduct] = useState<Product | null>(null)
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchProduct = async () => {
      setIsLoading(true)
      try {
        // Try to fetch from Supabase
        const { data, error } = await supabase
          .from('products')
          .select(`
            *,
            categories (*),
            users!seller_id (*),
            product_images (*)
          `)
          .eq('id', id)
          .single()

        if (error || !data) {
          // Fallback to mock data
          console.log("Using mock data for product:", id)
          const mockProduct = allExpandedProducts.find(p => p.id === id)

          if (mockProduct) {
            setProduct(mockProduct)

            // Get related products (same category, excluding current product)
            const related = allExpandedProducts
              .filter(p => p.category === mockProduct.category && p.id !== id)
              .slice(0, 4)

            setRelatedProducts(related)
          }
        } else {
          // Transform Supabase data to match Product type
          const transformedProduct: Product = {
            id: data.id,
            name: data.name,
            description: data.description,
            price: data.price,
            originalPrice: data.original_price,
            image: data.main_image || data.product_images[0]?.url || '/placeholder.svg',
            condition: data.condition as any,
            category: data.categories.slug as any,
            subcategory: data.categories.slug.split('-')[0] as any,
            seller: data.users.name || "Unknown Seller",
            location: data.location,
            isSecondHand: data.is_second_hand,
            usageHistory: data.usage_history,
            defectsDisclosure: data.defects_disclosure,
          }

          setProduct(transformedProduct)

          // Fetch related products
          const { data: relatedData } = await supabase
            .from('products')
            .select(`
              *,
              categories (*),
              users!seller_id (*),
              product_images (*)
            `)
            .eq('categories.slug', data.categories.slug)
            .neq('id', id)
            .limit(4)

          if (relatedData) {
            const transformedRelated = relatedData.map(item => ({
              id: item.id,
              name: item.name,
              description: item.description,
              price: item.price,
              originalPrice: item.original_price,
              image: item.main_image || item.product_images[0]?.url || '/placeholder.svg',
              condition: item.condition as any,
              category: item.categories.slug as any,
              subcategory: item.categories.slug.split('-')[0] as any,
              seller: item.users.name || "Unknown Seller",
              location: item.location,
            }))

            setRelatedProducts(transformedRelated)
          }
        }
      } catch (error) {
        console.error("Error fetching product:", error)
        // Fallback to mock data
        const mockProduct = allExpandedProducts.find(p => p.id === id)
        if (mockProduct) {
          setProduct(mockProduct)
          setRelatedProducts(
            allExpandedProducts
              .filter(p => p.category === mockProduct.category && p.id !== id)
              .slice(0, 4)
          )
        }
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      fetchProduct()
    }
  }, [id])

  const handleAddToCart = () => {
    if (!product) return

    setIsAddingToCart(true)
    addItem(product, quantity)
    setTimeout(() => setIsAddingToCart(false), 1000)
  }

  if (isLoading) {
    return (
      <div className="container px-4 py-12 md:py-24">
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="container px-4 py-12 md:py-24 text-center">
        <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="mb-8">The product you are looking for does not exist or has been removed.</p>
        <Button asChild>
          <Link href="/">Return to Home</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
        {/* Product Images */}
        <div className="w-full lg:w-1/2">
          <div className="sticky top-24 space-y-4">
            <div className="aspect-square relative rounded-lg overflow-hidden border">
              <Image src={product.image || "/placeholder.svg"} alt={product.name} fill className="object-cover" />
              {product.condition && <Badge className="absolute top-4 left-4 z-10">{product.condition}</Badge>}
            </div>
            <div className="grid grid-cols-4 gap-2">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="aspect-square relative rounded-md overflow-hidden border cursor-pointer">
                  <Image
                    src={product.image || "/placeholder.svg"}
                    alt={`${product.name} - View ${i + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="w-full lg:w-1/2">
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline" className="text-teal-600 border-teal-600">
                  {product.isSecondHand ? 'Second Hand' : 'New'}
                </Badge>
                <Badge variant="outline">
                  {product.subcategory}
                </Badge>
              </div>
              <h1 className="text-2xl md:text-3xl font-bold">{product.name}</h1>
              <div className="flex items-center gap-2 mt-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current text-yellow-400" />
                  ))}
                </div>
                <span className="text-sm text-slate-500">(24 reviews)</span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <p className="text-2xl font-bold text-teal-600">₹{product.price.toLocaleString("en-IN")}</p>
                {product.originalPrice && (
                  <p className="text-sm text-slate-500 line-through">₹{product.originalPrice.toLocaleString("en-IN")}</p>
                )}
              </div>
              {product.originalPrice && (
                <p className="text-sm text-green-600">
                  Save ₹{(product.originalPrice - product.price).toLocaleString("en-IN")} ({Math.round((1 - product.price / product.originalPrice) * 100)}% off)
                </p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-slate-500" />
                <span className="text-sm">{product.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-slate-500" />
                <span className="text-sm">Sold by <span className="font-medium">{product.seller}</span></span>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium">Description</h3>
              <p className="text-slate-600">{product.description}</p>
            </div>

            {product.isSecondHand && (
              <div className="space-y-2">
                <h3 className="font-medium">Condition Details</h3>
                <div className="space-y-1">
                  <div className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-teal-600 mt-0.5" />
                    <p className="text-sm text-slate-600">
                      <span className="font-medium">Usage History:</span> {product.usageHistory || "Not specified"}
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <Check className="h-4 w-4 text-teal-600 mt-0.5" />
                    <p className="text-sm text-slate-600">
                      <span className="font-medium">Defects Disclosure:</span> {product.defectsDisclosure || "No known defects"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center border rounded-md">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 rounded-r-none"
                    onClick={() => quantity > 1 && setQuantity(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    <span className="text-lg font-medium">-</span>
                  </Button>
                  <div className="h-10 w-12 flex items-center justify-center border-x">
                    <span className="text-sm font-medium">{quantity}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 rounded-l-none"
                    onClick={() => setQuantity(quantity + 1)}
                  >
                    <span className="text-lg font-medium">+</span>
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  className="bg-teal-600 hover:bg-teal-700"
                  onClick={handleAddToCart}
                  disabled={isAddingToCart}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {isAddingToCart ? "Added to Cart" : "Add to Cart"}
                </Button>
                <Button variant="outline" className="border-teal-600 text-teal-600 hover:bg-teal-50">
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </Button>
              </div>
              <Button variant="outline" className="w-full">
                <Share2 className="h-4 w-4 mr-2" />
                Share Product
              </Button>
            </div>

            <div className="space-y-4 pt-4 border-t">
              <h3 className="font-medium">Product Highlights</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Shield className="h-5 w-5 text-teal-600 shrink-0 mt-0.5" />
                  <div>
                    <span className="font-medium">Quality Assured</span>
                    <p className="text-slate-500">All products are thoroughly tested before listing</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <Truck className="h-5 w-5 text-teal-600 shrink-0 mt-0.5" />
                  <div>
                    <span className="font-medium">Fast Delivery</span>
                    <p className="text-slate-500">Ships within 24 hours, delivery in 2-5 business days</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <RotateCcw className="h-5 w-5 text-teal-600 shrink-0 mt-0.5" />
                  <div>
                    <span className="font-medium">7-Day Returns</span>
                    <p className="text-slate-500">Not satisfied? Return within 7 days for a full refund</p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="pt-4 border-t">
              <Tabs defaultValue="description">
                <TabsList className="w-full">
                  <TabsTrigger value="description" className="flex-1">
                    Description
                  </TabsTrigger>
                  <TabsTrigger value="specifications" className="flex-1">
                    Specifications
                  </TabsTrigger>
                  <TabsTrigger value="reviews" className="flex-1">
                    Reviews
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="description" className="pt-4">
                  <p className="text-slate-600">{product.description}</p>
                  <p className="mt-4 text-slate-600">
                    This {product.condition.toLowerCase()} {product.name.toLowerCase()} is in excellent working
                    condition and has been thoroughly tested to ensure quality and performance. The seller,{" "}
                    {product.seller}, has maintained it well and is offering it at a competitive price.
                  </p>
                </TabsContent>
                <TabsContent value="specifications" className="pt-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">Condition</div>
                      <div>{product.condition}</div>

                      <div className="font-medium">Brand</div>
                      <div>Premium Tech</div>

                      <div className="font-medium">Model</div>
                      <div>XYZ-123</div>

                      <div className="font-medium">Warranty</div>
                      <div>30 days seller warranty</div>

                      <div className="font-medium">Package Contents</div>
                      <div>Main unit, cables, documentation</div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="reviews" className="pt-4">
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="pb-4 border-b last:border-0">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">Rahul S.</div>
                            <div className="text-sm text-slate-500">Verified Buyer</div>
                          </div>
                          <div className="flex">
                            {[...Array(5)].map((_, j) => (
                              <Star key={j} className="h-4 w-4 fill-current text-yellow-400" />
                            ))}
                          </div>
                        </div>
                        <p className="mt-2 text-sm">
                          Great product, exactly as described. Fast shipping and excellent packaging. Would buy from
                          this seller again.
                        </p>
                        <div className="mt-2 text-xs text-slate-500">Posted on 12 May 2023</div>
                      </div>
                    ))}
                    <Button variant="outline" className="w-full">
                      Load More Reviews
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>

      {/* Related Products */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold mb-8">Related Products</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {relatedProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </div>
  )
}
