"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import {
  AlertCircle,
  Loader2
} from "lucide-react"
import { allProducts } from "@/app/data/enhanced-products"
import ProductComparison from "@/components/product-comparison"
import ProductGrid from "@/components/product-grid"
import type { Product } from "@/app/types"

export default function ComparePage() {
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [comparedProducts, setComparedProducts] = useState<Product[]>([])
  const [availableProducts, setAvailableProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showProductSelector, setShowProductSelector] = useState(false)

  // Get product IDs from URL
  useEffect(() => {
    const ids = searchParams.get("ids")?.split(",").filter(Boolean) || []

    if (ids.length > 0) {
      // Filter products by IDs
      const products = allProducts.filter(product => ids.includes(product.id))
      setComparedProducts(products)
    }

    // Set available products (excluding already compared ones)
    setAvailableProducts(allProducts.filter(product => !ids.includes(product.id)))
    setIsLoading(false)
  }, [searchParams])

  // Handle add product to comparison
  const handleAddProduct = (productId: string) => {
    if (!productId) return

    const product = allProducts.find(p => p.id === productId)
    if (product) {
      // Maximum 4 products for comparison
      if (comparedProducts.length >= 4) {
        toast({
          title: "Maximum Products Reached",
          description: "You can compare up to 4 products at a time. Remove a product to add another.",
          variant: "destructive"
        })
        return
      }

      setComparedProducts(prev => [...prev, product])
      setAvailableProducts(prev => prev.filter(p => p.id !== productId))

      // Update URL
      const newIds = [...comparedProducts.map(p => p.id), productId].join(",")
      window.history.pushState({}, "", `/compare?ids=${newIds}`)

      // Hide product selector after selection
      setShowProductSelector(false)
    }
  }

  // Handle remove product from comparison
  const handleRemoveProduct = (productId: string) => {
    const product = comparedProducts.find(p => p.id === productId)
    if (product) {
      setComparedProducts(prev => prev.filter(p => p.id !== productId))
      setAvailableProducts(prev => [...prev, product])

      // Update URL
      const newIds = comparedProducts.filter(p => p.id !== productId).map(p => p.id).join(",")
      if (newIds) {
        window.history.pushState({}, "", `/compare?ids=${newIds}`)
      } else {
        window.history.pushState({}, "", `/compare`)
      }
    }
  }

  // Handle showing product selector
  const handleShowProductSelector = () => {
    setShowProductSelector(true)
  }

  if (isLoading) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-teal-600" />
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load the comparison.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Compare Products</h1>
            <p className="text-slate-500 mt-1">
              Compare features and specifications of different products
            </p>
          </div>

          {/* Add product selector */}
          <div className="w-full md:w-auto">
            {showProductSelector ? (
              <Select onValueChange={handleAddProduct}>
                <SelectTrigger className="w-full md:w-[250px]">
                  <SelectValue placeholder="Select a product to compare" />
                </SelectTrigger>
                <SelectContent>
                  {availableProducts.length > 0 ? (
                    availableProducts.map(product => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No more products to compare
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            ) : (
              <Button
                onClick={handleShowProductSelector}
                disabled={comparedProducts.length >= 4 || availableProducts.length === 0}
              >
                Add Product to Compare
              </Button>
            )}
          </div>
        </div>

        {/* Product Comparison Component */}
        <ProductComparison
          products={comparedProducts}
          onRemoveProduct={handleRemoveProduct}
          onAddProduct={handleShowProductSelector}
          maxProducts={4}
        />

        {/* Recently Viewed Products */}
        <div className="mt-16">
          <h2 className="text-xl font-bold mb-6">You May Also Like</h2>
          <ProductGrid
            products={availableProducts.slice(0, 8)}
            columns={4}
            initialLimit={8}
            showLoadMore={false}
          />
        </div>
      </div>
    </div>
  )
}
