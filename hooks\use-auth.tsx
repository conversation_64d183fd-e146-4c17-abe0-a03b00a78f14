"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { useSupabaseAuth } from "@/hooks/use-supabase-auth"
import { useToast } from "@/hooks/use-toast"

interface User {
  id: string
  name: string | null
  email: string
  role: "buyer" | "seller" | "admin"
  avatar: string | null
  createdAt: string
  sellerProfile?: {
    id: string
    storeName: string | null
    description: string | null
    location: string | null
    rating: number | null
    verified: boolean
  }
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (name: string, email: string, password: string, role: "buyer" | "seller") => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const { user: supabaseUser, isLoading: supabaseLoading, login: supabaseLogin, register: supabaseRegister, logout: supabaseLogout, refreshUser: supabaseRefreshUser } = useSupabaseAuth()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  // Use Supabase user data when it changes
  useEffect(() => {
    if (!supabaseLoading) {
      if (supabaseUser) {
        // Convert Supabase user to our app's user format
        setUser({
          id: supabaseUser.id,
          name: supabaseUser.name,
          email: supabaseUser.email,
          role: supabaseUser.role,
          avatar: supabaseUser.avatar,
          createdAt: supabaseUser.createdAt,
          sellerProfile: supabaseUser.sellerProfile
        })

        // Store user role in localStorage for backward compatibility
        localStorage.setItem("userRole", supabaseUser.role)
        localStorage.setItem("isLoggedIn", "true")
      } else {
        setUser(null)
        // Clear client-side auth indicators
        localStorage.removeItem("isLoggedIn")
        localStorage.removeItem("userRole")
      }
      setIsLoading(false)
    }
  }, [supabaseUser, supabaseLoading])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // Use Supabase login
      await supabaseLogin(email, password)

      // The user state will be updated by the useEffect that watches supabaseUser
      toast({
        title: "Login successful",
        description: "Welcome back!",
      })

      router.refresh()
    } catch (error) {
      console.error("Login error:", error)

      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "An error occurred during login",
        variant: "destructive",
      })

      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (name: string, email: string, password: string, role: "buyer" | "seller") => {
    setIsLoading(true)
    try {
      // Use Supabase registration
      await supabaseRegister(name, email, password, role)

      toast({
        title: "Registration successful",
        description: "Your account has been created.",
      })

      router.refresh()
    } catch (error) {
      console.error("Registration error:", error)

      toast({
        title: "Registration failed",
        description: error instanceof Error ? error.message : "An error occurred during registration",
        variant: "destructive",
      })

      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      console.log("Logging out user...")

      // Use Supabase logout
      await supabaseLogout()

      // Clear user state (should be handled by useEffect, but as a backup)
      setUser(null)

      // Clear client-side auth state for backward compatibility
      localStorage.removeItem("isLoggedIn")
      localStorage.removeItem("userRole")

      toast({
        title: "Logged out",
        description: "You have been logged out successfully.",
      })

      // Redirect to home page
      router.push("/")
    } catch (error) {
      console.error("Logout error:", error)

      // Even if the API call fails, clear local state
      setUser(null)
      localStorage.removeItem("isLoggedIn")
      localStorage.removeItem("userRole")

      toast({
        title: "Logout issue",
        description: "You've been logged out, but there was an issue with the process.",
        variant: "destructive",
      })

      // Redirect to home page
      router.push("/")
    } finally {
      setIsLoading(false)
    }
  }

  const refreshUser = async () => {
    try {
      // Use Supabase refresh user
      const updatedUser = await supabaseRefreshUser()

      if (updatedUser) {
        // Convert to our app's user format
        setUser({
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role,
          avatar: updatedUser.avatar,
          createdAt: updatedUser.createdAt,
          sellerProfile: updatedUser.sellerProfile
        })
        return updatedUser
      } else {
        // Clear user if not authenticated
        setUser(null)
        return null
      }
    } catch (error) {
      console.error("Failed to refresh user:", error)
      // Clear user on error
      setUser(null)
      return null
    }
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, login, register, logout, refreshUser }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
