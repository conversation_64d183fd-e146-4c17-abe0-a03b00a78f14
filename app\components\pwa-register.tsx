"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, X } from "lucide-react"

export function PWARegister() {
  const [installPrompt, setInstallPrompt] = useState<any>(null)
  const [showInstallBanner, setShowInstallBanner] = useState(false)
  
  useEffect(() => {
    // Register service worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope)
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error)
          })
      })
    }
    
    // Handle PWA install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault()
      // Stash the event so it can be triggered later
      setInstallPrompt(e)
      
      // Check if user has already dismissed or installed
      const hasPromptBeenDismissed = localStorage.getItem('pwaPromptDismissed')
      if (!hasPromptBeenDismissed) {
        setShowInstallBanner(true)
      }
    })
    
    // Handle installed PWA
    window.addEventListener('appinstalled', () => {
      // Hide the install promotion
      setShowInstallBanner(false)
      // Clear the install prompt
      setInstallPrompt(null)
      // Log install to analytics
      console.log('PWA was installed')
    })
  }, [])
  
  const handleInstallClick = () => {
    if (!installPrompt) return
    
    // Show the install prompt
    installPrompt.prompt()
    
    // Wait for the user to respond to the prompt
    installPrompt.userChoice.then((choiceResult: { outcome: string }) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt')
      } else {
        console.log('User dismissed the install prompt')
        // Remember that user dismissed the prompt
        localStorage.setItem('pwaPromptDismissed', 'true')
      }
      // Clear the saved prompt since it can't be used again
      setInstallPrompt(null)
      setShowInstallBanner(false)
    })
  }
  
  const dismissInstallBanner = () => {
    setShowInstallBanner(false)
    // Remember that user dismissed the prompt
    localStorage.setItem('pwaPromptDismissed', 'true')
  }
  
  if (!showInstallBanner) return null
  
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t shadow-lg md:left-auto md:right-4 md:bottom-4 md:max-w-sm md:rounded-lg md:border">
      <div className="flex items-start gap-4">
        <div className="flex-1">
          <h3 className="font-medium">Install PASSDOWN App</h3>
          <p className="text-sm text-slate-500 mt-1">
            Install our app for a better experience with offline access and faster loading.
          </p>
        </div>
        <button 
          onClick={dismissInstallBanner}
          className="text-slate-400 hover:text-slate-500"
          aria-label="Dismiss"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="mt-4 flex gap-3">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={dismissInstallBanner}
          className="flex-1"
        >
          Not now
        </Button>
        <Button 
          size="sm" 
          onClick={handleInstallClick}
          className="flex-1 bg-teal-600 hover:bg-teal-700"
        >
          <Download className="h-4 w-4 mr-2" />
          Install
        </Button>
      </div>
    </div>
  )
}
