#!/usr/bin/env node

/**
 * This script initializes the database with mock data for testing purposes.
 * It creates users, products, categories, and other necessary entities.
 * 
 * Usage:
 * node scripts/init-database.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Mock data for users
const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=0D9488&color=fff',
  },
  {
    name: 'Seller User',
    email: '<EMAIL>',
    password: 'seller123',
    role: 'seller',
    avatar: 'https://ui-avatars.com/api/?name=Seller+User&background=0D9488&color=fff',
  },
  {
    name: 'Buyer User',
    email: '<EMAIL>',
    password: 'buyer123',
    role: 'buyer',
    avatar: 'https://ui-avatars.com/api/?name=Buyer+User&background=0D9488&color=fff',
  },
];

// Mock data for categories
const categories = [
  {
    name: 'Graphics Cards',
    slug: 'graphics-cards',
    description: 'High-performance graphics cards for gaming and content creation',
    image: 'https://images.unsplash.com/photo-1591488320449-011701bb6704?q=80&w=2070&auto=format&fit=crop',
  },
  {
    name: 'Processors',
    slug: 'processors',
    description: 'CPUs for desktop and laptop computers',
    image: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?q=80&w=2070&auto=format&fit=crop',
  },
  {
    name: 'Motherboards',
    slug: 'motherboards',
    description: 'Motherboards for desktop computers',
    image: 'https://images.unsplash.com/photo-1518770660439-4636190af475?q=80&w=2070&auto=format&fit=crop',
  },
  {
    name: 'RAM',
    slug: 'ram',
    description: 'Memory modules for desktop and laptop computers',
    image: 'https://images.unsplash.com/photo-1562976540-1502c2145186?q=80&w=2039&auto=format&fit=crop',
  },
  {
    name: 'Storage',
    slug: 'storage',
    description: 'SSDs and HDDs for desktop and laptop computers',
    image: 'https://images.unsplash.com/photo-1597225244660-1cd128c64284?q=80&w=2074&auto=format&fit=crop',
  },
];

// Mock data for products
const generateProducts = (sellerId) => [
  {
    name: 'NVIDIA GeForce RTX 3070 8GB Graphics Card',
    description: 'High-performance graphics card for gaming and content creation. Used for 6 months, in excellent condition. Features 8GB GDDR6 memory, ray tracing capabilities, and DLSS support.',
    price: 42999,
    originalPrice: 54999,
    image: 'https://images.unsplash.com/photo-1591488320449-011701bb6704?q=80&w=2070&auto=format&fit=crop',
    condition: 'Like New',
    category: 'graphics-cards',
    subcategory: 'graphics-cards',
    sellerId,
    location: 'Mumbai',
    isSecondHand: true,
    usageHistory: 'Used for 6 months in a smoke-free environment',
    defectsDisclosure: 'No known defects or issues',
    quantity: 1,
    status: 'active',
  },
  {
    name: 'AMD Ryzen 7 5800X 8-Core Processor',
    description: 'High-performance CPU for gaming and productivity. Used for 3 months, in perfect condition. Features 8 cores, 16 threads, and boost clock up to 4.7GHz.',
    price: 24999,
    originalPrice: 32999,
    image: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?q=80&w=2070&auto=format&fit=crop',
    condition: 'Like New',
    category: 'processors',
    subcategory: 'processors',
    sellerId,
    location: 'Delhi',
    isSecondHand: true,
    usageHistory: 'Used for 3 months with proper cooling',
    defectsDisclosure: 'No known defects or issues',
    quantity: 1,
    status: 'active',
  },
  {
    name: 'ASUS ROG Strix B550-F Gaming Motherboard',
    description: 'High-end gaming motherboard with excellent VRM and connectivity options. Used for 1 year, in good condition. Features PCIe 4.0, WiFi 6, and RGB lighting.',
    price: 14999,
    originalPrice: 19999,
    image: 'https://images.unsplash.com/photo-1518770660439-4636190af475?q=80&w=2070&auto=format&fit=crop',
    condition: 'Good',
    category: 'motherboards',
    subcategory: 'motherboards',
    sellerId,
    location: 'Bangalore',
    isSecondHand: true,
    usageHistory: 'Used for 1 year in a well-ventilated case',
    defectsDisclosure: 'Minor scratches on the heatsink, does not affect functionality',
    quantity: 1,
    status: 'active',
  },
];

// Function to create users
async function createUsers() {
  console.log('Creating users...');
  
  for (const user of users) {
    const hashedPassword = await bcrypt.hash(user.password, 10);
    
    await prisma.user.upsert({
      where: { email: user.email },
      update: {},
      create: {
        name: user.name,
        email: user.email,
        password: hashedPassword,
        role: user.role,
        avatar: user.avatar,
      },
    });
    
    console.log(`Created user: ${user.email}`);
  }
}

// Function to create seller profiles
async function createSellerProfiles() {
  console.log('Creating seller profiles...');
  
  const seller = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });
  
  if (seller) {
    await prisma.sellerProfile.upsert({
      where: { userId: seller.id },
      update: {},
      create: {
        userId: seller.id,
        storeName: 'Tech Recyclers',
        description: 'We sell high-quality used computer components at affordable prices.',
        location: 'Mumbai',
        rating: 4.8,
        verified: true,
      },
    });
    
    console.log(`Created seller profile for: ${seller.email}`);
  }
}

// Function to create categories
async function createCategories() {
  console.log('Creating categories...');
  
  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: {
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
      },
    });
    
    console.log(`Created category: ${category.name}`);
  }
}

// Function to create products
async function createProducts() {
  console.log('Creating products...');
  
  const seller = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });
  
  if (seller) {
    const products = generateProducts(seller.id);
    
    for (const product of products) {
      const category = await prisma.category.findUnique({
        where: { slug: product.category },
      });
      
      if (category) {
        await prisma.product.create({
          data: {
            name: product.name,
            description: product.description,
            price: product.price,
            originalPrice: product.originalPrice,
            image: product.image,
            condition: product.condition,
            categoryId: category.id,
            sellerId: seller.id,
            location: product.location,
            isSecondHand: product.isSecondHand,
            usageHistory: product.usageHistory,
            defectsDisclosure: product.defectsDisclosure,
            quantity: product.quantity,
            status: product.status,
          },
        });
        
        console.log(`Created product: ${product.name}`);
      }
    }
  }
}

// Main function to initialize the database
async function initDatabase() {
  try {
    console.log('Initializing database...');
    
    await createUsers();
    await createSellerProfiles();
    await createCategories();
    await createProducts();
    
    console.log('Database initialization completed successfully!');
  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
initDatabase();
