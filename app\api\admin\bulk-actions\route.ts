import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { supabase } from '@/lib/supabase';
import { getAuthUser } from '@/lib/auth';
import { sendOrderStatusUpdateEmail } from '@/lib/email';

// Validation schema for bulk product actions
const bulkProductActionSchema = z.object({
  action: z.enum(['feature', 'unfeature', 'activate', 'deactivate', 'delete']),
  productIds: z.array(z.string()).min(1),
});

// Validation schema for bulk user actions
const bulkUserActionSchema = z.object({
  action: z.enum(['activate', 'suspend', 'verify', 'delete']),
  userIds: z.array(z.string()).min(1),
});

// Validation schema for bulk order actions
const bulkOrderActionSchema = z.object({
  action: z.enum(['process', 'ship', 'deliver', 'cancel']),
  orderIds: z.array(z.string()).min(1),
  notifyCustomers: z.boolean().optional().default(true),
});

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const authUser = await getAuthUser();
    if (!authUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins can perform bulk actions
    if (authUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: Only admins can perform bulk actions' },
        { status: 403 }
      );
    }

    // Get request body
    const body = await req.json();

    // Determine the type of bulk action based on the request body
    if (body.productIds) {
      return handleBulkProductAction(body);
    } else if (body.userIds) {
      return handleBulkUserAction(body);
    } else if (body.orderIds) {
      return handleBulkOrderAction(body);
    } else {
      return NextResponse.json(
        { error: 'Invalid bulk action request' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error performing bulk action:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}

async function handleBulkProductAction(body: any) {
  // Validate request body
  const result = bulkProductActionSchema.safeParse(body);
  if (!result.success) {
    return NextResponse.json(
      { error: 'Validation failed', details: result.error.format() },
      { status: 400 }
    );
  }

  const { action, productIds } = result.data;

  // Perform the requested action
  let updateData = {};
  let successMessage = '';

  switch (action) {
    case 'feature':
      updateData = { featured: true, updated_at: new Date().toISOString() };
      successMessage = 'Products featured successfully';
      break;
    case 'unfeature':
      updateData = { featured: false, updated_at: new Date().toISOString() };
      successMessage = 'Products unfeatured successfully';
      break;
    case 'activate':
      updateData = { status: 'active', updated_at: new Date().toISOString() };
      successMessage = 'Products activated successfully';
      break;
    case 'deactivate':
      updateData = { status: 'inactive', updated_at: new Date().toISOString() };
      successMessage = 'Products deactivated successfully';
      break;
    case 'delete':
      // For delete, we'll use a different approach
      const { error: deleteError } = await supabase
        .from('products')
        .delete()
        .in('id', productIds);

      if (deleteError) {
        console.error('Error deleting products:', deleteError);
        return NextResponse.json(
          { error: 'Failed to delete products' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        message: 'Products deleted successfully',
        count: productIds.length,
      });
  }

  // Update products
  const { data, error } = await supabase
    .from('products')
    .update(updateData)
    .in('id', productIds)
    .select();

  if (error) {
    console.error(`Error performing bulk ${action} on products:`, error);
    return NextResponse.json(
      { error: `Failed to ${action} products` },
      { status: 500 }
    );
  }

  return NextResponse.json({
    message: successMessage,
    count: data.length,
  });
}

async function handleBulkUserAction(body: any) {
  // Validate request body
  const result = bulkUserActionSchema.safeParse(body);
  if (!result.success) {
    return NextResponse.json(
      { error: 'Validation failed', details: result.error.format() },
      { status: 400 }
    );
  }

  const { action, userIds } = result.data;

  // Perform the requested action
  let updateData = {};
  let successMessage = '';

  switch (action) {
    case 'activate':
      updateData = { status: 'active', updated_at: new Date().toISOString() };
      successMessage = 'Users activated successfully';
      break;
    case 'suspend':
      updateData = { status: 'suspended', updated_at: new Date().toISOString() };
      successMessage = 'Users suspended successfully';
      break;
    case 'verify':
      // For verify, we need to update the seller_profiles table
      const { data: sellerProfiles, error: sellerError } = await supabase
        .from('seller_profiles')
        .update({ verified: true, updated_at: new Date().toISOString() })
        .in('user_id', userIds)
        .select();

      if (sellerError) {
        console.error('Error verifying sellers:', sellerError);
        return NextResponse.json(
          { error: 'Failed to verify sellers' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        message: 'Sellers verified successfully',
        count: sellerProfiles.length,
      });
    case 'delete':
      // For delete, we'll use a different approach
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .in('id', userIds);

      if (deleteError) {
        console.error('Error deleting users:', deleteError);
        return NextResponse.json(
          { error: 'Failed to delete users' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        message: 'Users deleted successfully',
        count: userIds.length,
      });
  }

  // Update users
  const { data, error } = await supabase
    .from('users')
    .update(updateData)
    .in('id', userIds)
    .select();

  if (error) {
    console.error(`Error performing bulk ${action} on users:`, error);
    return NextResponse.json(
      { error: `Failed to ${action} users` },
      { status: 500 }
    );
  }

  return NextResponse.json({
    message: successMessage,
    count: data.length,
  });
}

async function handleBulkOrderAction(body: any) {
  // Validate request body
  const result = bulkOrderActionSchema.safeParse(body);
  if (!result.success) {
    return NextResponse.json(
      { error: 'Validation failed', details: result.error.format() },
      { status: 400 }
    );
  }

  const { action, orderIds, notifyCustomers } = result.data;

  // Map action to order status
  let status = '';
  let successMessage = '';

  switch (action) {
    case 'process':
      status = 'processing';
      successMessage = 'Orders marked as processing successfully';
      break;
    case 'ship':
      status = 'shipped';
      successMessage = 'Orders marked as shipped successfully';
      break;
    case 'deliver':
      status = 'delivered';
      successMessage = 'Orders marked as delivered successfully';
      break;
    case 'cancel':
      status = 'cancelled';
      successMessage = 'Orders cancelled successfully';
      break;
  }

  // Update orders
  const { data, error } = await supabase
    .from('orders')
    .update({ status, updated_at: new Date().toISOString() })
    .in('id', orderIds)
    .select(`
      *,
      users!inner(email)
    `);

  if (error) {
    console.error(`Error performing bulk ${action} on orders:`, error);
    return NextResponse.json(
      { error: `Failed to ${action} orders` },
      { status: 500 }
    );
  }

  // Send email notifications if requested
  if (notifyCustomers) {
    const emailPromises = data.map(order => 
      sendOrderStatusUpdateEmail(
        order.id,
        status,
        order.users.email
      )
    );

    try {
      await Promise.all(emailPromises);
    } catch (emailError) {
      console.warn('Error sending some order status update emails:', emailError);
    }
  }

  return NextResponse.json({
    message: successMessage,
    count: data.length,
  });
}
