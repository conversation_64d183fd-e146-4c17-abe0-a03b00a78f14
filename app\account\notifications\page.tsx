"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Bell, 
  Loader2, 
  ShoppingBag, 
  CreditCard, 
  Truck, 
  Settings, 
  Tag,
  Trash2
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { useNotifications, NotificationType } from "@/hooks/use-notifications"

// Format date for notifications
function formatNotificationDate(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleDateString("en-IN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  })
}

// Icon mapping for notification types
function NotificationIcon({ type }: { type: NotificationType }) {
  switch (type) {
    case "order_status":
      return <ShoppingBag className="h-5 w-5 text-blue-500" />
    case "payment":
      return <CreditCard className="h-5 w-5 text-green-500" />
    case "delivery":
      return <Truck className="h-5 w-5 text-amber-500" />
    case "system":
      return <Settings className="h-5 w-5 text-slate-500" />
    case "promotion":
      return <Tag className="h-5 w-5 text-purple-500" />
    default:
      return <Bell className="h-5 w-5 text-slate-500" />
  }
}

export default function NotificationsPage() {
  const router = useRouter()
  const { user, isLoading: authLoading } = useAuth()
  const { 
    notifications, 
    markAsRead, 
    markAllAsRead, 
    clearNotifications 
  } = useNotifications()
  const [activeTab, setActiveTab] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?callbackUrl=/account/notifications")
    } else {
      // Simulate loading data
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [user, authLoading, router])

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === "all") return true
    if (activeTab === "unread") return !notification.read
    return notification.type === activeTab
  })

  // Count unread notifications by type
  const unreadCounts = {
    all: notifications.filter(n => !n.read).length,
    order_status: notifications.filter(n => n.type === "order_status" && !n.read).length,
    payment: notifications.filter(n => n.type === "payment" && !n.read).length,
    delivery: notifications.filter(n => n.type === "delivery" && !n.read).length,
    system: notifications.filter(n => n.type === "system" && !n.read).length,
    promotion: notifications.filter(n => n.type === "promotion" && !n.read).length,
  }

  // Handle notification click
  const handleNotificationClick = (id: string) => {
    markAsRead(id)
  }

  // Show loading or redirect if not authenticated
  if (authLoading || isLoading || !user) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-teal-600" />
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your notifications.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Notifications</h1>
          <p className="text-slate-500 mt-1">
            Stay updated with your orders and account activity
          </p>
        </div>
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={markAllAsRead}
            disabled={unreadCounts.all === 0}
          >
            Mark all as read
          </Button>
          <Button 
            variant="outline" 
            onClick={clearNotifications}
            disabled={notifications.length === 0}
            className="text-red-500 hover:text-red-600"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear all
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6 flex flex-wrap">
          <TabsTrigger value="all" className="relative">
            All
            {unreadCounts.all > 0 && (
              <Badge className="ml-2 bg-red-500">{unreadCounts.all}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="relative">
            Unread
            {unreadCounts.all > 0 && (
              <Badge className="ml-2 bg-red-500">{unreadCounts.all}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="order_status" className="relative">
            Orders
            {unreadCounts.order_status > 0 && (
              <Badge className="ml-2 bg-blue-500">{unreadCounts.order_status}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="payment" className="relative">
            Payments
            {unreadCounts.payment > 0 && (
              <Badge className="ml-2 bg-green-500">{unreadCounts.payment}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="delivery" className="relative">
            Delivery
            {unreadCounts.delivery > 0 && (
              <Badge className="ml-2 bg-amber-500">{unreadCounts.delivery}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="promotion" className="relative">
            Promotions
            {unreadCounts.promotion > 0 && (
              <Badge className="ml-2 bg-purple-500">{unreadCounts.promotion}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="system" className="relative">
            System
            {unreadCounts.system > 0 && (
              <Badge className="ml-2 bg-slate-500">{unreadCounts.system}</Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === "all" ? "All Notifications" :
                 activeTab === "unread" ? "Unread Notifications" :
                 activeTab === "order_status" ? "Order Notifications" :
                 activeTab === "payment" ? "Payment Notifications" :
                 activeTab === "delivery" ? "Delivery Notifications" :
                 activeTab === "promotion" ? "Promotions" :
                 "System Notifications"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredNotifications.length === 0 ? (
                <div className="text-center py-12">
                  <Bell className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No notifications</h3>
                  <p className="text-slate-500 mb-6">
                    {activeTab === "all" 
                      ? "You don't have any notifications yet." 
                      : activeTab === "unread"
                      ? "You don't have any unread notifications."
                      : `You don't have any ${activeTab} notifications.`}
                  </p>
                </div>
              ) : (
                <div className="space-y-1">
                  {filteredNotifications.map((notification, index) => (
                    <div key={notification.id}>
                      <Link
                        href={notification.link || "#"}
                        className={`block p-4 rounded-lg hover:bg-slate-50 transition-colors ${!notification.read ? "bg-slate-50" : ""}`}
                        onClick={() => handleNotificationClick(notification.id)}
                      >
                        <div className="flex gap-4">
                          <div className="mt-0.5">
                            <NotificationIcon type={notification.type} />
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1">
                              <h3 className={`font-medium ${!notification.read ? "text-black" : "text-slate-700"}`}>
                                {notification.title}
                              </h3>
                              <span className="text-xs text-slate-500">
                                {formatNotificationDate(notification.createdAt)}
                              </span>
                            </div>
                            <p className="text-sm text-slate-600 mt-1">
                              {notification.message}
                            </p>
                          </div>
                        </div>
                      </Link>
                      {index < filteredNotifications.length - 1 && <Separator />}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
