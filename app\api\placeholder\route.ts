import { NextRequest, NextResponse } from 'next/server';

/**
 * Generate a placeholder SVG image with specified dimensions and optional text
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const width = parseInt(searchParams.get('width') || '300', 10);
  const height = parseInt(searchParams.get('height') || '300', 10);
  const text = searchParams.get('text') || '';
  const bgColor = searchParams.get('bgColor') || '#f0f0f0';
  const textColor = searchParams.get('textColor') || '#888888';

  // Validate dimensions
  if (width <= 0 || height <= 0 || width > 2000 || height > 2000) {
    return NextResponse.json(
      { error: 'Invalid dimensions. Width and height must be between 1 and 2000 pixels.' },
      { status: 400 }
    );
  }

  // Generate SVG placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${bgColor}" />
      ${text ? `
        <text 
          x="50%" 
          y="50%" 
          font-family="Arial, sans-serif" 
          font-size="${Math.max(12, Math.min(width, height) / 10)}px" 
          fill="${textColor}" 
          text-anchor="middle" 
          dominant-baseline="middle"
        >
          ${text}
        </text>
      ` : ''}
    </svg>
  `;

  // Return SVG with appropriate headers
  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000, immutable',
    },
  });
}
