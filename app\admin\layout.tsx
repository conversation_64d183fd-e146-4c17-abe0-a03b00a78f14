"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/hooks/use-auth"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Tag,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isLoading, logout } = useAuth()
  const router = useRouter()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeSection, setActiveSection] = useState<string | null>(null)

  // Check if user is admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== "admin")) {
      router.push("/login?callbackUrl=/admin")
    }
  }, [user, isLoading, router])

  // If still loading or not admin, show loading state
  if (isLoading || !user || user.role !== "admin") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading admin dashboard...</p>
        </div>
      </div>
    )
  }

  const navigationItems = [
    {
      name: "Dashboard",
      href: "/admin",
      icon: LayoutDashboard,
      exact: true,
    },
    {
      name: "Products",
      href: "/admin/products",
      icon: Package,
      children: [
        { name: "All Products", href: "/admin/products" },
        { name: "Featured Products", href: "/admin/products/featured" },
        { name: "Categories", href: "/admin/products/categories" },
      ],
    },
    {
      name: "Orders",
      href: "/admin/orders",
      icon: ShoppingCart,
    },
    {
      name: "Users",
      href: "/admin/users",
      icon: Users,
      children: [
        { name: "All Users", href: "/admin/users" },
        { name: "Sellers", href: "/admin/users/sellers" },
        { name: "Buyers", href: "/admin/users/buyers" },
      ],
    },
    {
      name: "Promotions",
      href: "/admin/promotions",
      icon: Tag,
      children: [
        { name: "Deals", href: "/admin/promotions/deals" },
        { name: "Discounts", href: "/admin/promotions/discounts" },
        { name: "Featured Items", href: "/admin/promotions/featured" },
      ],
    },
    {
      name: "Analytics",
      href: "/admin/analytics",
      icon: BarChart3,
    },
    {
      name: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ]

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const toggleSection = (section: string) => {
    setActiveSection(activeSection === section ? null : section)
  }

  const isActive = (href: string, exact = false) => {
    if (typeof window === "undefined") return false
    const path = window.location.pathname
    return exact ? path === href : path.startsWith(href)
  }

  return (
    <div className="flex min-h-screen bg-slate-50">
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex md:w-64 flex-col bg-white border-r border-slate-200">
        <div className="p-4 border-b border-slate-200">
          <Link href="/admin" className="flex items-center">
            <h1 className="text-xl font-bold text-teal-600">PASSDOWN</h1>
            <span className="ml-2 text-xs bg-teal-100 text-teal-800 px-2 py-0.5 rounded">Admin</span>
          </Link>
        </div>
        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-1 px-2">
            {navigationItems.map((item) => (
              <li key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleSection(item.name)}
                      className={cn(
                        "flex items-center w-full px-3 py-2 text-sm rounded-md",
                        isActive(item.href, item.exact) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                      )}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      <span>{item.name}</span>
                      <ChevronDown className={cn("ml-auto h-4 w-4 transition-transform", activeSection === item.name ? "transform rotate-180" : "")} />
                    </button>
                    {activeSection === item.name && (
                      <ul className="mt-1 pl-10 space-y-1">
                        {item.children.map((child) => (
                          <li key={child.name}>
                            <Link
                              href={child.href}
                              className={cn(
                                "block px-3 py-2 text-sm rounded-md",
                                isActive(child.href, true) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                              )}
                            >
                              {child.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm rounded-md",
                      isActive(item.href, item.exact) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                    )}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    <span>{item.name}</span>
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t border-slate-200">
          <Button
            variant="outline"
            className="w-full justify-start text-slate-600"
            onClick={logout}
          >
            <LogOut className="h-5 w-5 mr-3" />
            <span>Log out</span>
          </Button>
        </div>
      </aside>

      {/* Mobile menu button */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 px-4 py-3 flex items-center justify-between">
        <Link href="/admin" className="flex items-center">
          <h1 className="text-xl font-bold text-teal-600">PASSDOWN</h1>
          <span className="ml-2 text-xs bg-teal-100 text-teal-800 px-2 py-0.5 rounded">Admin</span>
        </Link>
        <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
          {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </Button>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 z-40 bg-white pt-16">
          <nav className="h-full overflow-y-auto py-4">
            <ul className="space-y-1 px-4">
              {navigationItems.map((item) => (
                <li key={item.name}>
                  {item.children ? (
                    <div>
                      <button
                        onClick={() => toggleSection(item.name)}
                        className={cn(
                          "flex items-center w-full px-3 py-2 text-sm rounded-md",
                          isActive(item.href, item.exact) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                        )}
                      >
                        <item.icon className="h-5 w-5 mr-3" />
                        <span>{item.name}</span>
                        <ChevronDown className={cn("ml-auto h-4 w-4 transition-transform", activeSection === item.name ? "transform rotate-180" : "")} />
                      </button>
                      {activeSection === item.name && (
                        <ul className="mt-1 pl-10 space-y-1">
                          {item.children.map((child) => (
                            <li key={child.name}>
                              <Link
                                href={child.href}
                                className={cn(
                                  "block px-3 py-2 text-sm rounded-md",
                                  isActive(child.href, true) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                                )}
                                onClick={toggleMobileMenu}
                              >
                                {child.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm rounded-md",
                        isActive(item.href, item.exact) ? "bg-teal-50 text-teal-600" : "text-slate-600 hover:bg-slate-100"
                      )}
                      onClick={toggleMobileMenu}
                    >
                      <item.icon className="h-5 w-5 mr-3" />
                      <span>{item.name}</span>
                    </Link>
                  )}
                </li>
              ))}
              <li>
                <Button
                  variant="outline"
                  className="w-full justify-start text-slate-600 mt-4"
                  onClick={() => {
                    toggleMobileMenu()
                    logout()
                  }}
                >
                  <LogOut className="h-5 w-5 mr-3" />
                  <span>Log out</span>
                </Button>
              </li>
            </ul>
          </nav>
        </div>
      )}

      {/* Main content */}
      <main className="flex-1 md:ml-64 pt-16 md:pt-0">
        <div className="p-6">{children}</div>
      </main>
    </div>
  )
}
