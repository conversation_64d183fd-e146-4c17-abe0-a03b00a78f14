@echo off
echo ======================================================
echo PASSDOWN E-commerce Platform - Development Server
echo ======================================================
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
  echo ERROR: Node.js is not installed or not in your PATH.
  echo Please install Node.js from https://nodejs.org/
  goto error
)

REM Check for npm
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
  echo ERROR: npm is not installed or not in your PATH.
  echo Please install Node.js from https://nodejs.org/
  goto error
)

REM Check Node.js version
for /f "tokens=*" %%a in ('node --version') do set NODE_VERSION=%%a
echo Node.js version: %NODE_VERSION%
echo.

REM Check if .env.local file exists
if not exist .env.local (
  echo WARNING: .env.local file not found.
  echo Creating .env.local file with required configuration...

  echo # Environment variables > .env.local
  echo DATABASE_URL="file:./dev.db" >> .env.local
  echo JWT_SECRET="your-secret-key-at-least-32-characters-long-for-security" >> .env.local
  echo NEXT_PUBLIC_API_URL="http://localhost:3000/api" >> .env.local
  echo. >> .env.local
  echo # Supabase Configuration >> .env.local
  echo NEXT_PUBLIC_SUPABASE_URL=https://ajanudmpznxgmfzxzjwo.supabase.co >> .env.local
  echo NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqYW51ZG1wem54Z21menhaandvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTU1MzA0NzcsImV4cCI6MjAzMTEwNjQ3N30.Wd9EhQdYPVQnJXLcWBGYrHlGPxTHj9pKQDCIXVE-eFY >> .env.local

  if errorlevel 1 (
    echo Failed to create .env.local file. Please create it manually.
    goto error
  )
  echo .env.local file created successfully.
)

REM Check if node_modules exists
if not exist node_modules (
  echo node_modules not found. Installing dependencies...
  call npm install
  if errorlevel 1 (
    echo Failed to install dependencies.
    goto error
  )
  echo Dependencies installed successfully.
)

REM Generate Prisma client
echo Generating Prisma client...
call npx prisma generate
if errorlevel 1 (
  echo Failed to generate Prisma client.
  goto error
)

REM Clear any previous build errors
if exist .next (
  echo Clearing previous build cache...
  rmdir /s /q .next
  if errorlevel 1 (
    echo Warning: Failed to clear .next directory. Continuing anyway...
  )
)

REM Check if database needs to be initialized
if not exist prisma\dev.db (
  echo Database not found. Running migrations and seeding...
  call npx prisma migrate reset --force
  if errorlevel 1 (
    echo Failed to run database migrations.
    goto error
  )
) else (
  echo Database already exists. Skipping migration.
  echo If you want to reset the database, delete prisma\dev.db and run this script again.
)

echo.
echo ======================================================
echo PASSDOWN E-commerce Platform - Ready to Start
echo ======================================================
echo.
echo The development server will start on http://localhost:3000
echo.
echo Admin credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Seller credentials:
echo Email: <EMAIL>
echo Password: seller123
echo.
echo Buyer credentials:
echo Email: <EMAIL>
echo Password: buyer123
echo.
echo Press Ctrl+C to stop the server
echo ======================================================
echo.

REM Start the development server
echo Starting Next.js development server...
call npm run dev

if errorlevel 1 (
  echo Failed to start development server.
  goto error
)

goto end

:error
echo.
echo ======================================================
echo ERROR: Failed to start the development server.
echo.
echo Troubleshooting steps:
echo 1. Make sure Node.js is installed (v18 or higher)
echo    - Current Node version: %NODE_VERSION%
echo.
echo 2. Check that .env.local file exists with proper environment variables
echo    - If you see syntax errors, try manually editing the .env.local file
echo    - Make sure DATABASE_URL is set correctly
echo    - Make sure JWT_SECRET is set correctly
echo.
echo 3. Try fixing common issues:
echo    - Run: npm install
echo    - Run: rmdir /s /q .next
echo    - Run: npx prisma generate
echo    - Run: npx prisma migrate reset --force
echo    - Run: npm run dev
echo.
echo 4. Check for specific errors in the console output above
echo    - Look for "Syntax Error" or "Module not found" messages
echo.
echo 5. If you see authentication issues:
echo    - Run: node scripts/check-auth.js
echo    - Run: node test-auth.js
echo ======================================================
echo.
echo Press any key to exit...
pause > nul
exit /b 1

:end
exit /b 0
