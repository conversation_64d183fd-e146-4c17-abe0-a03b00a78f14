import { Metadata } from 'next';
import Header from '@/app/components/header';
import Footer from '@/app/components/footer';

export const metadata: Metadata = {
  title: 'System Health Check - PASSDOWN',
  description: 'Check the health and status of the PASSDOWN e-commerce platform',
};

interface HealthLayoutProps {
  children: React.ReactNode;
}

export default function HealthLayout({ children }: HealthLayoutProps) {
  return (
    <div className="relative flex min-h-screen flex-col">
      <Header />
      <div className="flex-1">{children}</div>
      <Footer />
    </div>
  );
}
