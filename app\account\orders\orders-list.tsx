"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChevronRight, Package } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/components/ui/use-toast"

// Mock order data - in a real app, this would come from your API
const mockOrders = [
  {
    id: "ORD123456",
    date: "2023-05-15T10:30:00Z",
    status: "delivered",
    total: 54999,
    items: [
      {
        id: "1",
        name: "NVIDIA GeForce RTX 3070 8GB Graphics Card",
        price: 42999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      },
      {
        id: "5",
        name: "Corsair Vengeance LPX 32GB (2x16GB) DDR4 3200MHz RAM",
        price: 9999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ]
  },
  {
    id: "ORD789012",
    date: "2023-05-20T14:15:00Z",
    status: "processing",
    total: 24999,
    items: [
      {
        id: "2",
        name: "AMD Ryzen 7 5800X Desktop Processor",
        price: 24999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ]
  },
  {
    id: "ORD345678",
    date: "2023-05-18T09:45:00Z",
    status: "shipped",
    total: 15999,
    items: [
      {
        id: "3",
        name: "ASUS ROG Strix B550-F Gaming Motherboard",
        price: 15999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ]
  },
  {
    id: "ORD901234",
    date: "2023-05-10T16:20:00Z",
    status: "cancelled",
    total: 8499,
    items: [
      {
        id: "4",
        name: "Samsung 970 EVO Plus 1TB NVMe SSD",
        price: 8499,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ]
  },
  {
    id: "ORD567890",
    date: "2023-05-05T11:10:00Z",
    status: "delivered",
    total: 36999,
    items: [
      {
        id: "6",
        name: "Intel Core i9-12900K Desktop Processor",
        price: 36999,
        quantity: 1,
        image: "/placeholder.svg?height=200&width=200"
      }
    ]
  }
]

// Format date
function formatDate(dateString: string) {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  return new Date(dateString).toLocaleDateString('en-IN', options)
}

// Status badge configuration
const statusBadgeConfig: Record<string, { variant: "default" | "destructive" | "outline" | "secondary" | "success", label: string }> = {
  pending: { variant: "secondary", label: "Pending" },
  processing: { variant: "secondary", label: "Processing" },
  shipped: { variant: "default", label: "Shipped" },
  delivered: { variant: "success", label: "Delivered" },
  cancelled: { variant: "destructive", label: "Cancelled" }
}

export default function OrdersList({ status = "all" }: { status?: string }) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [orders, setOrders] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // In a real app, you would fetch from your API
        // For now, we'll use mock data
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        let filteredOrders = mockOrders
        
        if (status !== "all") {
          filteredOrders = mockOrders.filter(order => order.status === status)
        }
        
        setOrders(filteredOrders)
      } catch (error) {
        console.error("Error fetching orders:", error)
        toast({
          title: "Error",
          description: "Failed to load orders. Please try again later.",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (user) {
      fetchOrders()
    }
  }, [user, status, toast])

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="h-12 w-12 text-slate-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2">No orders found</h3>
        <p className="text-slate-500 mb-6">
          {status === "all" 
            ? "You haven't placed any orders yet." 
            : `You don't have any ${status} orders.`}
        </p>
        <Button asChild className="bg-teal-600 hover:bg-teal-700">
          <Link href="/">Browse Products</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => (
        <Card key={order.id} className="overflow-hidden">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <h3 className="font-medium">Order #{order.id}</h3>
                  <p className="text-sm text-slate-500">Placed on {formatDate(order.date)}</p>
                </div>
                <div className="flex items-center gap-2 mt-2 md:mt-0">
                  <Badge variant={statusBadgeConfig[order.status]?.variant || "default"}>
                    {statusBadgeConfig[order.status]?.label || order.status}
                  </Badge>
                  <span className="font-semibold">₹{order.total.toLocaleString("en-IN")}</span>
                </div>
              </div>
              
              <div className="flex flex-col md:flex-row gap-4 pt-4">
                <div className="flex-1">
                  <div className="flex flex-wrap gap-4">
                    {order.items.slice(0, 3).map((item: any) => (
                      <div key={item.id} className="flex items-center gap-3">
                        <div className="relative w-16 h-16 rounded-md overflow-hidden">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium line-clamp-1">{item.name}</p>
                          <p className="text-xs text-slate-500">
                            ₹{item.price.toLocaleString("en-IN")} × {item.quantity}
                          </p>
                        </div>
                      </div>
                    ))}
                    {order.items.length > 3 && (
                      <div className="flex items-center">
                        <Badge variant="outline">+{order.items.length - 3} more items</Badge>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" asChild>
                    <Link href={`/account/orders/${order.id}`}>
                      View Details
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                  {order.status === "delivered" && (
                    <Button variant="outline">Buy Again</Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
