import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);

    // Pagination parameters
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 12;
    const offset = (page - 1) * limit;

    // Filter parameters
    const category = searchParams.get('category');
    const featured = searchParams.get('featured') === 'true';
    const search = searchParams.get('search');
    const minPrice = searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined;
    const maxPrice = searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined;
    const condition = searchParams.get('condition');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build where clause
    let whereClause: any = {};

    if (category) {
      const categoryRecord = await prisma.category.findUnique({
        where: { slug: category },
      });

      if (categoryRecord) {
        whereClause.categoryId = categoryRecord.id;
      }
    }

    if (search) {
      whereClause.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
      ];
    }

    if (minPrice !== undefined) {
      whereClause.price = { ...whereClause.price, gte: minPrice };
    }

    if (maxPrice !== undefined) {
      whereClause.price = { ...whereClause.price, lte: maxPrice };
    }

    if (condition) {
      whereClause.condition = condition;
    }

    if (featured) {
      whereClause.featured = true;
    }

    // Build order by
    let orderBy: any = {};

    switch (sortBy) {
      case 'price':
        orderBy.price = sortOrder;
        break;
      case 'name':
        orderBy.name = sortOrder;
        break;
      case 'views':
        orderBy.views = sortOrder;
        break;
      default:
        orderBy.createdAt = sortOrder;
    }

    // Get total count for pagination
    const totalCount = await prisma.product.count({
      where: whereClause,
    });

    // Get products with pagination
    const products = await prisma.product.findMany({
      where: whereClause,
      skip: offset,
      take: limit,
      include: {
        category: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            sellerProfile: true,
          },
        },
        images: true,
      },
      orderBy,
    });

    // Transform the data to match the expected format in the frontend
    const transformedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      originalPrice: product.originalPrice,
      image: product.mainImage || product.images[0]?.url || '/placeholder.svg?height=400&width=400',
      condition: product.condition,
      category: product.category.slug,
      seller: product.seller.name || 'Unknown Seller',
      location: product.location,
      images: product.images.map(img => img.url),
      isSecondHand: product.isSecondHand,
      usageHistory: product.usageHistory,
      defectsDisclosure: product.defectsDisclosure,
      quantity: product.quantity,
      status: product.status,
      views: product.views,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
    }));

    // Return products with pagination metadata
    return NextResponse.json({
      products: transformedProducts,
      pagination: {
        page,
        limit,
        totalItems: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: offset + products.length < totalCount
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
