"use client"

import { useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Shield, Store, User, Info } from "lucide-react"

// Form validation schema
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
})

type LoginFormValues = z.infer<typeof loginSchema>

// Demo credentials for different roles
const demoCredentials = {
  admin: { email: "<EMAIL>", password: "admin123" },
  seller: { email: "<EMAIL>", password: "seller123" },
  buyer: { email: "<EMAIL>", password: "buyer123" }
}

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams.get("callbackUrl") || "/"
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRole, setSelectedRole] = useState<"admin" | "seller" | "buyer">("buyer")

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  const fillDemoCredentials = (role: "admin" | "seller" | "buyer") => {
    const credentials = demoCredentials[role]
    setValue("email", credentials.email)
    setValue("password", credentials.password)
    setSelectedRole(role)
  }

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true)
    setError(null)

    try {
      console.log("Attempting login with:", data.email)

      // Normalize email to lowercase to ensure consistent matching
      const normalizedData = {
        ...data,
        email: data.email.toLowerCase().trim()
      }

      // Special case for admin login
      const isAdminLogin = normalizedData.email === "<EMAIL>" && normalizedData.password === "admin123"
      if (isAdminLogin) {
        console.log("Admin login detected")
      }

      // Special case for seller login
      const isSellerLogin = normalizedData.email === "<EMAIL>" && normalizedData.password === "seller123"
      if (isSellerLogin) {
        console.log("Seller login detected")
      }

      // Special case for buyer login
      const isBuyerLogin = normalizedData.email === "<EMAIL>" && normalizedData.password === "buyer123"
      if (isBuyerLogin) {
        console.log("Buyer login detected")
      }

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(normalizedData),
        credentials: "include", // Important for cookies
      })

      // Check for auth debug header
      const authDebug = response.headers.get("X-Auth-Debug")
      console.log("Auth debug header:", authDebug)

      const result = await response.json()
      console.log("Login response:", response.status, result)

      if (!response.ok) {
        throw new Error(result.error || "Failed to login")
      }

      // Set a client-side flag to indicate successful login
      // This helps with client-side auth state detection
      localStorage.setItem("isLoggedIn", "true")

      // Store user role for client-side role checks
      if (result.user && result.user.role) {
        localStorage.setItem("userRole", result.user.role)
        console.log(`User role stored: ${result.user.role}`)
      }

      // Wait a moment to ensure cookies are set
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log("Login successful, redirecting to:", callbackUrl)

      // For admin users, redirect to admin dashboard
      if (result.user && result.user.role === "admin") {
        console.log("Admin user detected, redirecting to admin dashboard")
        router.push("/admin")
      } else {
        // Redirect to callback URL or home page
        router.push(callbackUrl)
      }

      router.refresh()
    } catch (err) {
      console.error("Login error:", err)
      setError(err instanceof Error ? err.message : "An error occurred during login")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
      <Card className="w-full max-w-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Login to PASSDOWN</CardTitle>
          <CardDescription>Choose your role and enter your credentials</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Demo Credentials Section */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Demo Credentials</span>
            </div>
            <div className="grid grid-cols-1 gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fillDemoCredentials("admin")}
                className="flex items-center gap-2 justify-start h-auto p-3"
              >
                <Shield className="h-4 w-4 text-red-600" />
                <div className="text-left">
                  <div className="font-medium">Admin Access</div>
                  <div className="text-xs text-gray-500"><EMAIL></div>
                </div>
                <Badge variant="destructive" className="ml-auto">Admin</Badge>
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fillDemoCredentials("seller")}
                className="flex items-center gap-2 justify-start h-auto p-3"
              >
                <Store className="h-4 w-4 text-orange-600" />
                <div className="text-left">
                  <div className="font-medium">Seller Dashboard</div>
                  <div className="text-xs text-gray-500"><EMAIL></div>
                </div>
                <Badge variant="secondary" className="ml-auto">Seller</Badge>
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => fillDemoCredentials("buyer")}
                className="flex items-center gap-2 justify-start h-auto p-3"
              >
                <User className="h-4 w-4 text-green-600" />
                <div className="text-left">
                  <div className="font-medium">Customer Account</div>
                  <div className="text-xs text-gray-500"><EMAIL></div>
                </div>
                <Badge variant="outline" className="ml-auto">Buyer</Badge>
              </Button>
            </div>
          </div>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
                disabled={isLoading}
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="/forgot-password" className="text-sm text-teal-600 hover:underline">
                  Forgot password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                {...register("password")}
                disabled={isLoading}
              />
              {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
            </div>
            <Button type="submit" className="w-full bg-teal-600 hover:bg-teal-700" disabled={isLoading}>
              {isLoading ? "Logging in..." : `Login as ${selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}`}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-slate-500">
            Don't have an account?{" "}
            <Link href="/register" className="text-teal-600 hover:underline">
              Register
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
