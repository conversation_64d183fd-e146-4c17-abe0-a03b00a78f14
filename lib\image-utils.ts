/**
 * Utility functions for image optimization
 */

/**
 * Generate responsive image sizes for different screen widths
 * @param baseSize Base size of the image in pixels
 * @returns A sizes string for the next/image component
 */
export function getResponsiveSizes(baseSize: number): string {
  return `(max-width: 640px) ${Math.min(baseSize, 100)}vw, (max-width: 1024px) ${Math.min(baseSize, 50)}vw, ${baseSize}px`;
}

/**
 * Generate a placeholder image URL with specified dimensions
 * @param width Width of the placeholder image
 * @param height Height of the placeholder image
 * @param text Optional text to display on the placeholder
 * @returns A URL for a placeholder image
 */
export function getPlaceholderImage(width: number, height: number, text?: string): string {
  const textParam = text ? `&text=${encodeURIComponent(text)}` : '';
  return `/api/placeholder?width=${width}&height=${height}${textParam}`;
}

/**
 * Get image dimensions based on aspect ratio
 * @param width Width of the image
 * @param aspectRatio Aspect ratio (width/height)
 * @returns An object with width and height
 */
export function getImageDimensions(width: number, aspectRatio: number = 1): { width: number; height: number } {
  const height = Math.round(width / aspectRatio);
  return { width, height };
}

/**
 * Generate srcSet for responsive images
 * @param src Base image URL
 * @param widths Array of widths to generate
 * @returns A srcSet string
 */
export function generateSrcSet(src: string, widths: number[]): string {
  if (!src.includes('?')) {
    return widths.map(w => `${src}?width=${w} ${w}w`).join(', ');
  }

  // If the URL already has query parameters
  return widths.map(w => `${src}&width=${w} ${w}w`).join(', ');
}

/**
 * Check if an image URL is external (not from our domain)
 * @param url Image URL to check
 * @returns Boolean indicating if the URL is external
 */
export function isExternalImage(url: string): boolean {
  if (!url) return false;

  try {
    // Check if the URL is absolute (has protocol)
    if (url.startsWith('http://') || url.startsWith('https://')) {
      const currentDomain = typeof window !== 'undefined' ? window.location.hostname : '';
      const urlObj = new URL(url);
      return urlObj.hostname !== currentDomain;
    }
    return false;
  } catch (e) {
    return false;
  }
}

/**
 * Get image format based on browser support
 * @returns The best supported image format
 */
export function getOptimalImageFormat(): 'webp' | 'avif' | 'jpeg' {
  if (typeof window === 'undefined') return 'webp'; // Default for SSR

  // Check for AVIF support
  const avifSupport =
    document.createElement('canvas')
    .toDataURL('image/avif')
    .indexOf('data:image/avif') === 0;

  if (avifSupport) return 'avif';

  // Check for WebP support
  const webpSupport =
    document.createElement('canvas')
    .toDataURL('image/webp')
    .indexOf('data:image/webp') === 0;

  return webpSupport ? 'webp' : 'jpeg';
}

/**
 * Generate a blurred placeholder data URL for an image
 * @param width Width of the placeholder
 * @param height Height of the placeholder
 * @param color Base color for the placeholder (hex)
 * @returns A data URL for a blurred placeholder
 */
export function generateBlurPlaceholder(width: number, height: number, color: string = '#f0f0f0'): string {
  // Simple SVG-based blur placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <filter id="b" x="0" y="0">
        <feGaussianBlur stdDeviation="20" />
      </filter>
      <rect width="100%" height="100%" fill="${color}" />
      <rect width="100%" height="100%" filter="url(#b)" opacity="0.5" />
    </svg>
  `;

  // Use btoa for client-side compatibility instead of Buffer
  if (typeof window !== 'undefined') {
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  // Use Buffer for server-side
  return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
}
