// Initialize Supabase database with required tables and data
// Run with: node scripts/init-supabase.js

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key is missing. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Read SQL schema file
const schemaPath = path.join(__dirname, 'supabase-schema.sql');
let schemaSQL;

try {
  schemaSQL = fs.readFileSync(schemaPath, 'utf8');
} catch (error) {
  console.error('Error reading schema file:', error);
  process.exit(1);
}

// Split SQL into individual statements
function splitSQLStatements(sql) {
  // This is a simple implementation and might not handle all SQL edge cases
  return sql
    .replace(/--.*$/gm, '') // Remove comments
    .split(';')
    .map(statement => statement.trim())
    .filter(statement => statement.length > 0);
}

// Execute SQL statements
async function executeSQL(statements) {
  for (const statement of statements) {
    try {
      console.log(`Executing SQL: ${statement.substring(0, 50)}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.warn(`Warning: SQL execution error: ${error.message}`);
        console.warn('Statement:', statement);
        // Continue with next statement
      }
    } catch (error) {
      console.warn(`Warning: SQL execution error: ${error.message}`);
      // Continue with next statement
    }
  }
}

// Create test users
async function createTestUsers() {
  console.log('Creating test users...');
  
  // Admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const { data: admin, error: adminError } = await supabase
    .from('users')
    .upsert({
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();
  
  if (adminError) {
    console.error('Error creating admin user:', adminError);
  } else {
    console.log('Admin user created/updated:', admin.email);
  }
  
  // Seller user
  const sellerPassword = await bcrypt.hash('seller123', 10);
  const { data: seller, error: sellerError } = await supabase
    .from('users')
    .upsert({
      email: '<EMAIL>',
      name: 'Seller User',
      password: sellerPassword,
      role: 'seller',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();
  
  if (sellerError) {
    console.error('Error creating seller user:', sellerError);
  } else {
    console.log('Seller user created/updated:', seller.email);
    
    // Create seller profile
    const { error: profileError } = await supabase
      .from('seller_profiles')
      .upsert({
        user_id: seller.id,
        store_name: 'Tech Treasures',
        description: 'Quality second-hand tech products',
        location: 'Mumbai',
        rating: 4.8,
        verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    
    if (profileError) {
      console.error('Error creating seller profile:', profileError);
    } else {
      console.log('Seller profile created/updated for:', seller.email);
    }
  }
  
  // Buyer user
  const buyerPassword = await bcrypt.hash('buyer123', 10);
  const { data: buyer, error: buyerError } = await supabase
    .from('users')
    .upsert({
      email: '<EMAIL>',
      name: 'Buyer User',
      password: buyerPassword,
      role: 'buyer',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .select()
    .single();
  
  if (buyerError) {
    console.error('Error creating buyer user:', buyerError);
  } else {
    console.log('Buyer user created/updated:', buyer.email);
  }
}

// Create categories
async function createCategories() {
  console.log('Creating categories...');
  
  const categories = [
    {
      name: 'Electronics',
      slug: 'electronics',
      description: 'Electronic devices and accessories',
      image: 'https://images.unsplash.com/photo-1550009158-9ebf69173e03?w=500&h=500&fit=crop',
    },
    {
      name: 'Computers',
      slug: 'computers',
      description: 'Computers, laptops, and accessories',
      image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=500&h=500&fit=crop',
    },
    {
      name: 'Smartphones',
      slug: 'smartphones',
      description: 'Mobile phones and accessories',
      image: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=500&h=500&fit=crop',
    },
    {
      name: 'Clothing',
      slug: 'clothing',
      description: 'Clothes, shoes, and accessories',
      image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=500&h=500&fit=crop',
    },
    {
      name: 'Home & Kitchen',
      slug: 'home-kitchen',
      description: 'Home and kitchen appliances and accessories',
      image: 'https://images.unsplash.com/photo-1556911220-bda9f7f7597e?w=500&h=500&fit=crop',
    },
  ];
  
  for (const category of categories) {
    const { error } = await supabase
      .from('categories')
      .upsert({
        name: category.name,
        slug: category.slug,
        description: category.description,
        image: category.image,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    
    if (error) {
      console.error(`Error creating category ${category.name}:`, error);
    } else {
      console.log(`Category created/updated: ${category.name}`);
    }
  }
  
  // Set parent-child relationships
  const { data: computers } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', 'computers')
    .single();
  
  const { data: smartphones } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', 'smartphones')
    .single();
  
  const { data: electronics } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', 'electronics')
    .single();
  
  if (computers && electronics) {
    const { error } = await supabase
      .from('categories')
      .update({ parent_id: electronics.id })
      .eq('id', computers.id);
    
    if (error) {
      console.error('Error setting parent for computers category:', error);
    }
  }
  
  if (smartphones && electronics) {
    const { error } = await supabase
      .from('categories')
      .update({ parent_id: electronics.id })
      .eq('id', smartphones.id);
    
    if (error) {
      console.error('Error setting parent for smartphones category:', error);
    }
  }
}

// Main function
async function main() {
  console.log('Initializing Supabase database...');
  
  try {
    // Execute schema SQL
    const statements = splitSQLStatements(schemaSQL);
    console.log(`Found ${statements.length} SQL statements to execute`);
    await executeSQL(statements);
    
    // Create test data
    await createTestUsers();
    await createCategories();
    
    console.log('Supabase initialization completed successfully!');
  } catch (error) {
    console.error('Initialization error:', error);
  }
}

main();
