"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { OptimizedImage } from "@/components/ui/optimized-image"
import {
  Search,
  Plus,
  X,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Star,
  StarOff,
  Trash2,
  MoveUp,
  MoveDown,
} from "lucide-react"
import { cn } from "@/lib/utils"
import type { Product } from "@/app/types"

export default function FeaturedProductsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [allProducts, setAllProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState("featured")

  const productsPerPage = 10

  // Fetch featured products and all products
  useEffect(() => {
    // Simulate API call with mock data
    setTimeout(() => {
      // Mock data for featured products
      const mockFeaturedProducts = [
        {
          id: "1",
          name: "NVIDIA GeForce RTX 3070 8GB Graphics Card",
          description: "High-performance graphics card for gaming and content creation.",
          price: 42999,
          originalPrice: 54999,
          image: "https://images.unsplash.com/photo-1591488320449-011701bb6704?q=80&w=2070&auto=format&fit=crop",
          condition: "Like New" as "Like New",
          category: "graphics-cards" as any,
          subcategory: "graphics" as any,
          seller: "Tech Treasures",
          location: "Mumbai",
          isSecondHand: true,
          usageHistory: "Used for 6 months in a smoke-free environment",
          quantity: 1,
          status: "active" as "active",
          views: 245,
          createdAt: "2023-05-10T10:30:00Z",
          updatedAt: "2023-05-10T10:30:00Z",
          featuredOrder: 1,
        },
        {
          id: "2",
          name: "AMD Ryzen 9 5900X 12-Core Processor",
          description: "High-end desktop processor for gaming and content creation.",
          price: 36999,
          originalPrice: 44999,
          image: "https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?q=80&w=2070&auto=format&fit=crop",
          condition: "New" as "New",
          category: "processors" as any,
          subcategory: "processors" as any,
          seller: "CPU World",
          location: "Delhi",
          isSecondHand: false,
          quantity: 3,
          status: "active" as "active",
          views: 189,
          createdAt: "2023-05-08T14:20:00Z",
          updatedAt: "2023-05-08T14:20:00Z",
          featuredOrder: 2,
        },
        {
          id: "3",
          name: "Samsung 970 EVO Plus 1TB NVMe SSD",
          description: "High-performance NVMe SSD for fast storage.",
          price: 9999,
          originalPrice: 12999,
          image: "https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?q=80&w=2070&auto=format&fit=crop",
          condition: "New" as "New",
          category: "storage" as any,
          subcategory: "storage" as any,
          seller: "Storage Solutions",
          location: "Bangalore",
          isSecondHand: false,
          quantity: 5,
          status: "active" as "active",
          views: 132,
          createdAt: "2023-05-05T09:15:00Z",
          updatedAt: "2023-05-05T09:15:00Z",
          featuredOrder: 3,
        },
      ]

      // Mock data for all products
      const mockAllProducts = [
        ...mockFeaturedProducts,
        {
          id: "4",
          name: "Corsair Vengeance RGB Pro 32GB DDR4 RAM",
          description: "High-performance RGB memory for gaming PCs.",
          price: 12999,
          originalPrice: 15999,
          image: "https://images.unsplash.com/photo-1592664474505-51c549ad15c5?q=80&w=2070&auto=format&fit=crop",
          condition: "New" as "New",
          category: "memory" as any,
          subcategory: "memory" as any,
          seller: "Memory Masters",
          location: "Chennai",
          isSecondHand: false,
          quantity: 8,
          status: "active" as "active",
          views: 98,
          createdAt: "2023-05-03T11:45:00Z",
          updatedAt: "2023-05-03T11:45:00Z",
        },
        {
          id: "5",
          name: "ASUS ROG Strix B550-F Gaming Motherboard",
          description: "High-performance gaming motherboard for AMD processors.",
          price: 18999,
          originalPrice: 22999,
          image: "https://images.unsplash.com/photo-1563791877383-eb10ee7c9c8a?q=80&w=2070&auto=format&fit=crop",
          condition: "Like New" as "Like New",
          category: "motherboards" as any,
          subcategory: "motherboards" as any,
          seller: "PC Components",
          location: "Hyderabad",
          isSecondHand: true,
          usageHistory: "Used for 3 months in a clean environment",
          quantity: 2,
          status: "active" as "active",
          views: 76,
          createdAt: "2023-05-01T16:30:00Z",
          updatedAt: "2023-05-01T16:30:00Z",
        },
      ]

      setFeaturedProducts(mockFeaturedProducts)
      setAllProducts(mockAllProducts)
      setFilteredProducts(mockAllProducts)
      setTotalPages(Math.ceil(mockAllProducts.length / productsPerPage))
      setIsLoading(false)
    }, 1000)
  }, [])

  // Filter products based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredProducts(allProducts)
      setTotalPages(Math.ceil(allProducts.length / productsPerPage))
    } else {
      const filtered = allProducts.filter((product) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredProducts(filtered)
      setTotalPages(Math.ceil(filtered.length / productsPerPage))
    }
    setCurrentPage(1)
  }, [searchQuery, allProducts])

  // Get current products for pagination
  const getCurrentProducts = () => {
    const startIndex = (currentPage - 1) * productsPerPage
    const endIndex = startIndex + productsPerPage
    return filteredProducts.slice(startIndex, endIndex)
  }

  // Add product to featured
  const addToFeatured = (product: Product) => {
    if (featuredProducts.some((p) => p.id === product.id)) {
      return
    }

    const updatedProduct = {
      ...product,
      featuredOrder: featuredProducts.length + 1,
    }

    setFeaturedProducts([...featuredProducts, updatedProduct])
  }

  // Remove product from featured
  const removeFromFeatured = (productId: string) => {
    const updatedFeatured = featuredProducts
      .filter((p) => p.id !== productId)
      .map((p, index) => ({
        ...p,
        featuredOrder: index + 1,
      }))

    setFeaturedProducts(updatedFeatured)
  }

  // Move product up in featured list
  const moveUp = (index: number) => {
    if (index === 0) return

    const newFeatured = [...featuredProducts]
    const temp = newFeatured[index]
    newFeatured[index] = newFeatured[index - 1]
    newFeatured[index - 1] = temp

    // Update order numbers
    const updatedFeatured = newFeatured.map((p, i) => ({
      ...p,
      featuredOrder: i + 1,
    }))

    setFeaturedProducts(updatedFeatured)
  }

  // Move product down in featured list
  const moveDown = (index: number) => {
    if (index === featuredProducts.length - 1) return

    const newFeatured = [...featuredProducts]
    const temp = newFeatured[index]
    newFeatured[index] = newFeatured[index + 1]
    newFeatured[index + 1] = temp

    // Update order numbers
    const updatedFeatured = newFeatured.map((p, i) => ({
      ...p,
      featuredOrder: i + 1,
    }))

    setFeaturedProducts(updatedFeatured)
  }

  // Save featured products
  const saveFeaturedProducts = () => {
    // In a real application, this would make an API call to save the featured products
    console.log("Saving featured products:", featuredProducts)
    alert("Featured products saved successfully!")
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4"></div>
          <p className="text-slate-600">Loading products...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Featured Products</h1>
          <p className="text-slate-500 mt-1">
            Manage which products appear in the featured section on the homepage
          </p>
        </div>
        <Button onClick={saveFeaturedProducts} className="bg-teal-600 hover:bg-teal-700">
          Save Changes
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="featured">Featured Products ({featuredProducts.length})</TabsTrigger>
          <TabsTrigger value="all">All Products ({allProducts.length})</TabsTrigger>
        </TabsList>

        {/* Featured Products Tab */}
        <TabsContent value="featured" className="space-y-6">
          {featuredProducts.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6 min-h-[200px]">
                <Star className="h-16 w-16 text-slate-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Featured Products</h3>
                <p className="text-slate-500 text-center max-w-md mb-4">
                  You haven't added any featured products yet. Featured products will be displayed prominently on the
                  homepage.
                </p>
                <Button variant="outline" onClick={() => setActiveTab("all")}>
                  Add Featured Products
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Current Featured Products</CardTitle>
                <CardDescription>
                  Drag to reorder or use the arrows. Products will appear on the homepage in this order.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left font-medium p-2 pl-0">Order</th>
                        <th className="text-left font-medium p-2">Product</th>
                        <th className="text-left font-medium p-2">Price</th>
                        <th className="text-left font-medium p-2">Category</th>
                        <th className="text-left font-medium p-2">Condition</th>
                        <th className="text-right font-medium p-2 pr-0">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {featuredProducts.map((product, index) => (
                        <tr key={product.id} className="border-b">
                          <td className="p-2 pl-0 font-medium">{product.featuredOrder}</td>
                          <td className="p-2">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded overflow-hidden flex-shrink-0">
                                <OptimizedImage
                                  src={product.image || "/placeholder.svg"}
                                  alt={product.name}
                                  width={40}
                                  height={40}
                                  className="object-cover"
                                />
                              </div>
                              <div className="truncate max-w-[200px]">
                                <p className="font-medium truncate">{product.name}</p>
                                <p className="text-xs text-slate-500 truncate">ID: {product.id}</p>
                              </div>
                            </div>
                          </td>
                          <td className="p-2">
                            <div>
                              <p className="font-medium">₹{product.price.toLocaleString("en-IN")}</p>
                              {product.originalPrice && (
                                <p className="text-xs text-slate-500 line-through">
                                  ₹{product.originalPrice.toLocaleString("en-IN")}
                                </p>
                              )}
                            </div>
                          </td>
                          <td className="p-2">
                            <Badge variant="outline">{product.category}</Badge>
                          </td>
                          <td className="p-2">
                            <Badge
                              className={
                                product.condition === "New"
                                  ? "bg-green-100 text-green-800 hover:bg-green-100"
                                  : product.condition === "Like New"
                                  ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                                  : "bg-amber-100 text-amber-800 hover:bg-amber-100"
                              }
                            >
                              {product.condition}
                            </Badge>
                          </td>
                          <td className="p-2 pr-0 text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => moveUp(index)}
                                disabled={index === 0}
                                className="h-8 w-8"
                              >
                                <MoveUp className="h-4 w-4" />
                                <span className="sr-only">Move up</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => moveDown(index)}
                                disabled={index === featuredProducts.length - 1}
                                className="h-8 w-8"
                              >
                                <MoveDown className="h-4 w-4" />
                                <span className="sr-only">Move down</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => removeFromFeatured(product.id)}
                                className="h-8 w-8 text-red-500 hover:text-red-600"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Remove</span>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* All Products Tab */}
        <TabsContent value="all" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>All Products</CardTitle>
              <CardDescription>Select products to feature on the homepage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                  <Input
                    placeholder="Search products..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                      onClick={() => setSearchQuery("")}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Clear search</span>
                    </Button>
                  )}
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left font-medium p-2 pl-0">Product</th>
                      <th className="text-left font-medium p-2">Price</th>
                      <th className="text-left font-medium p-2">Category</th>
                      <th className="text-left font-medium p-2">Condition</th>
                      <th className="text-right font-medium p-2 pr-0">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getCurrentProducts().map((product) => {
                      const isFeatured = featuredProducts.some((p) => p.id === product.id)
                      return (
                        <tr key={product.id} className="border-b">
                          <td className="p-2 pl-0">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded overflow-hidden flex-shrink-0">
                                <OptimizedImage
                                  src={product.image || "/placeholder.svg"}
                                  alt={product.name}
                                  width={40}
                                  height={40}
                                  className="object-cover"
                                />
                              </div>
                              <div className="truncate max-w-[200px]">
                                <p className="font-medium truncate">{product.name}</p>
                                <p className="text-xs text-slate-500 truncate">ID: {product.id}</p>
                              </div>
                            </div>
                          </td>
                          <td className="p-2">
                            <div>
                              <p className="font-medium">₹{product.price.toLocaleString("en-IN")}</p>
                              {product.originalPrice && (
                                <p className="text-xs text-slate-500 line-through">
                                  ₹{product.originalPrice.toLocaleString("en-IN")}
                                </p>
                              )}
                            </div>
                          </td>
                          <td className="p-2">
                            <Badge variant="outline">{product.category}</Badge>
                          </td>
                          <td className="p-2">
                            <Badge
                              className={
                                product.condition === "New"
                                  ? "bg-green-100 text-green-800 hover:bg-green-100"
                                  : product.condition === "Like New"
                                  ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                                  : "bg-amber-100 text-amber-800 hover:bg-amber-100"
                              }
                            >
                              {product.condition}
                            </Badge>
                          </td>
                          <td className="p-2 pr-0 text-right">
                            <Button
                              variant={isFeatured ? "outline" : "default"}
                              size="sm"
                              onClick={() => (isFeatured ? removeFromFeatured(product.id) : addToFeatured(product))}
                              className={isFeatured ? "text-amber-600 border-amber-200" : "bg-teal-600 hover:bg-teal-700"}
                            >
                              {isFeatured ? (
                                <>
                                  <StarOff className="h-4 w-4 mr-1" /> Remove
                                </>
                              ) : (
                                <>
                                  <Star className="h-4 w-4 mr-1" /> Feature
                                </>
                              )}
                            </Button>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-slate-500">
                    Showing {(currentPage - 1) * productsPerPage + 1} to{" "}
                    {Math.min(currentPage * productsPerPage, filteredProducts.length)} of {filteredProducts.length} products
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="h-8 w-8"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      <span className="sr-only">Previous page</span>
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="h-8 w-8"
                    >
                      <ChevronRight className="h-4 w-4" />
                      <span className="sr-only">Next page</span>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
