import { supabase } from './supabase';
import type { Product, ProductCategory, ProductSubcategory } from '@/app/types';
import { generateCache<PERSON>ey, getCache, setCache, withCache } from './cache-utils';

// Type definitions for database tables
export type DbUser = {
  id: string;
  name: string | null;
  email: string;
  password: string;
  role: 'buyer' | 'seller' | 'admin';
  created_at: string;
  updated_at: string;
  avatar: string | null;
};

export type DbSellerProfile = {
  id: string;
  user_id: string;
  store_name: string | null;
  description: string | null;
  location: string | null;
  rating: number | null;
  verified: boolean;
  created_at: string;
  updated_at: string;
};

export type DbCategory = {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  image: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string;
};

export type DbProduct = {
  id: string;
  name: string;
  description: string;
  price: number;
  original_price: number | null;
  main_image: string | null;
  condition: string;
  category_id: string;
  seller_id: string;
  location: string;
  is_second_hand: boolean;
  usage_history: string | null;
  defects_disclosure: string | null;
  quantity: number;
  status: string;
  views: number;
  created_at: string;
  updated_at: string;
};

export type DbProductImage = {
  id: string;
  url: string;
  product_id: string;
  created_at: string;
  updated_at: string;
};

// Conversion functions between database and application types
export function dbProductToProduct(
  dbProduct: DbProduct,
  category: DbCategory,
  seller: DbUser,
  images: DbProductImage[]
): Product {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.main_image || images[0]?.url || '/placeholder.svg?height=400&width=400',
    condition: dbProduct.condition as "New" | "Like New" | "Good" | "Fair" | "Poor",
    category: category.slug as ProductCategory,
    subcategory: category.slug.split('-')[0] as ProductSubcategory,
    seller: seller.name || 'Unknown Seller',
    location: dbProduct.location,
    isSecondHand: dbProduct.is_second_hand,
    usageHistory: dbProduct.usage_history || undefined,
    defectsDisclosure: dbProduct.defects_disclosure || undefined,
    quantity: dbProduct.quantity,
    status: dbProduct.status as "active" | "sold" | "hidden",
    views: dbProduct.views,
    createdAt: dbProduct.created_at,
    updatedAt: dbProduct.updated_at,
  };
}

// Database operations

// Get products with pagination and filtering
export async function getProducts(options: {
  page?: number;
  limit?: number;
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  skipCache?: boolean;
} = {}) {
  const {
    page = 1,
    limit = 12,
    category,
    search,
    minPrice,
    maxPrice,
    condition,
    sortBy = 'created_at',
    sortOrder = 'desc',
    skipCache = false,
  } = options;

  // Generate cache key
  const cacheKey = generateCacheKey('getProducts', {
    page, limit, category, search, minPrice, maxPrice, condition, sortBy, sortOrder
  });

  // Skip cache if requested or for searches (which are less likely to be repeated)
  if (skipCache || search) {
    return fetchProducts();
  }

  // Try to get from cache
  return withCache(fetchProducts, cacheKey, 300); // Cache for 5 minutes

  // Function to fetch products from the database
  async function fetchProducts() {
    // Calculate pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Start building the query
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (*),
        users!seller_id (*),
        product_images (*)
      `, { count: 'exact' });

    // Apply filters
    if (category) {
      // Get category ID (this could also be cached)
      const categoryId = await getCategoryIdBySlug(category);
      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (minPrice !== undefined) {
      query = query.gte('price', minPrice);
    }

    if (maxPrice !== undefined) {
      query = query.lte('price', maxPrice);
    }

    if (condition) {
      query = query.eq('condition', condition);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(from, to);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }

    // Transform the data to match the expected format in the frontend
    const products = data?.map(item => {
      const product = item as unknown as DbProduct & {
        categories: DbCategory;
        users: DbUser;
        product_images: DbProductImage[];
      };

      return dbProductToProduct(
        product,
        product.categories,
        product.users,
        product.product_images
      );
    }) || [];

    return {
      products,
      totalCount: count || 0,
    };
  }
}

// Helper function to get category ID by slug (with caching)
async function getCategoryIdBySlug(slug: string): Promise<string | null> {
  const cacheKey = generateCacheKey('categoryIdBySlug', slug);

  // Try to get from cache
  const cachedId = getCache<string>(cacheKey);
  if (cachedId) return cachedId;

  // Fetch from database
  const { data } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', slug)
    .single();

  if (!data) return null;

  // Cache the result for 1 hour (categories don't change often)
  setCache(cacheKey, data.id, 3600);

  return data.id;
}

// Get a single product by ID
export async function getProductById(id: string, skipCache: boolean = false) {
  const cacheKey = generateCacheKey('getProductById', id);

  // Skip cache if requested
  if (skipCache) {
    return fetchProduct();
  }

  // Try to get from cache
  return withCache(fetchProduct, cacheKey, 300); // Cache for 5 minutes

  async function fetchProduct() {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (*),
        users!seller_id (*),
        product_images (*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      throw error;
    }

    if (!data) {
      return null;
    }

    const product = data as unknown as DbProduct & {
      categories: DbCategory;
      users: DbUser;
      product_images: DbProductImage[];
    };

    return dbProductToProduct(
      product,
      product.categories,
      product.users,
      product.product_images
    );
  }
}

// Get categories
export async function getCategories(parentSlug?: string, skipCache: boolean = false) {
  const cacheKey = generateCacheKey('getCategories', parentSlug || 'root');

  // Skip cache if requested
  if (skipCache) {
    return fetchCategories();
  }

  // Categories change very rarely, so we can cache them for longer
  return withCache(fetchCategories, cacheKey, 3600); // Cache for 1 hour

  async function fetchCategories() {
    let query = supabase.from('categories').select('*');

    if (parentSlug) {
      // Get parent category ID (using the cached function)
      const parentId = await getCategoryIdBySlug(parentSlug);

      if (parentId) {
        query = query.eq('parent_id', parentId);
      }
    } else {
      query = query.is('parent_id', null);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    return data || [];
  }
}
