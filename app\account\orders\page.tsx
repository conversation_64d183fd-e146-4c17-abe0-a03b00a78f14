"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import {
  Package,
  ShoppingBag,
  TruckIcon,
  CheckCircle2,
  XCircle,
  Loader2
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { allProducts } from "@/app/data/enhanced-products"
import OrderManagement from "./order-management"

// Mock order data
const mockOrders = [
  {
    id: "ORD-001",
    date: "2023-05-15",
    total: 42999,
    status: "Delivered",
    items: [
      {
        id: "1",
        name: "NVIDIA GeForce RTX 3070 8GB Graphics Card",
        price: 42999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1591488320449-011701bb6704?q=80&w=2070&auto=format&fit=crop"
      }
    ],
    tracking: {
      number: "IND123456789",
      carrier: "BlueDart",
      url: "#"
    },
    deliveryAddress: {
      name: "John Doe",
      line1: "123 Main Street",
      line2: "Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      postalCode: "400001",
      country: "India"
    },
    paymentMethod: "Credit Card",
    deliveryDate: "2023-05-20"
  },
  {
    id: "ORD-002",
    date: "2023-05-10",
    total: 64998,
    status: "Shipped",
    items: [
      {
        id: "3",
        name: "AMD Ryzen 7 5800X Desktop Processor",
        price: 24999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?q=80&w=1974&auto=format&fit=crop"
      },
      {
        id: "7",
        name: "ASUS ROG Strix B550-F Gaming Motherboard",
        price: 15999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1563770660941-10a63607692e?q=80&w=2070&auto=format&fit=crop"
      },
      {
        id: "9",
        name: "Corsair Vengeance LPX 32GB (2x16GB) DDR4 3200MHz RAM",
        price: 9999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1562976540-1502c2145186?q=80&w=1931&auto=format&fit=crop"
      },
      {
        id: "11",
        name: "Samsung 970 EVO Plus 1TB NVMe SSD",
        price: 8499,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1597138804456-e7dca7f59d54?q=80&w=1974&auto=format&fit=crop"
      },
      {
        id: "15",
        name: "Cooler Master Hyper 212 RGB CPU Cooler",
        price: 2999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1587202372775-e229f172b9d7?q=80&w=2070&auto=format&fit=crop"
      },
      {
        id: "12",
        name: "Western Digital Black 4TB 7200RPM HDD",
        price: 7999,
        quantity: 0.5,
        image: "https://images.unsplash.com/photo-1531492746076-161ca9bcad58?q=80&w=1974&auto=format&fit=crop"
      }
    ],
    tracking: {
      number: "IND987654321",
      carrier: "DTDC",
      url: "#"
    },
    deliveryAddress: {
      name: "John Doe",
      line1: "123 Main Street",
      line2: "Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      postalCode: "400001",
      country: "India"
    },
    paymentMethod: "UPI",
    estimatedDelivery: "2023-05-25"
  },
  {
    id: "ORD-003",
    date: "2023-04-28",
    total: 94999,
    status: "Delivered",
    items: [
      {
        id: "6",
        name: "ASUS ROG Zephyrus G14 Gaming Laptop",
        price: 94999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-1603302576837-37561b2e2302?q=80&w=1968&auto=format&fit=crop"
      }
    ],
    tracking: {
      number: "IND456789123",
      carrier: "FedEx",
      url: "#"
    },
    deliveryAddress: {
      name: "John Doe",
      line1: "123 Main Street",
      line2: "Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      postalCode: "400001",
      country: "India"
    },
    paymentMethod: "Debit Card",
    deliveryDate: "2023-05-03"
  },
  {
    id: "ORD-004",
    date: "2023-04-15",
    total: 39999,
    status: "Delivered",
    items: [
      {
        id: "2",
        name: "AMD Radeon RX 6800 XT 16GB Graphics Card",
        price: 39999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-*************-8f3d1fcfb75c?q=80&w=1964&auto=format&fit=crop"
      }
    ],
    tracking: {
      number: "IND789123456",
      carrier: "Delhivery",
      url: "#"
    },
    deliveryAddress: {
      name: "John Doe",
      line1: "123 Main Street",
      line2: "Apartment 4B",
      city: "Mumbai",
      state: "Maharashtra",
      postalCode: "400001",
      country: "India"
    },
    paymentMethod: "Net Banking",
    deliveryDate: "2023-04-20"
  },
  {
    id: "ORD-005",
    date: "2023-03-30",
    total: 36999,
    status: "Cancelled",
    items: [
      {
        id: "4",
        name: "Intel Core i9-12900K Desktop Processor",
        price: 36999,
        quantity: 1,
        image: "https://images.unsplash.com/photo-**********-5a9d7d11e6bb?q=80&w=1974&auto=format&fit=crop"
      }
    ],
    cancellationReason: "Changed mind about purchase",
    refundStatus: "Refunded",
    refundAmount: 36999,
    refundDate: "2023-04-02"
  }
]

export default function OrderHistoryPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isLoading: authLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [orders, setOrders] = useState(mockOrders)
  const [filteredOrders, setFilteredOrders] = useState(mockOrders)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedOrder, setSelectedOrder] = useState<typeof mockOrders[0] | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login?callbackUrl=/account/orders")
    } else {
      // Simulate loading data
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [user, authLoading, router])

  // Filter orders based on search query and status filter
  useEffect(() => {
    let filtered = orders

    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.items.some(item => item.name.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status.toLowerCase() === statusFilter.toLowerCase())
    }

    setFilteredOrders(filtered)
  }, [orders, searchQuery, statusFilter])

  // Handle order selection for details view
  const handleOrderSelect = (order: typeof mockOrders[0]) => {
    setSelectedOrder(order)
    setShowOrderDetails(true)
  }

  // Handle back button in order details view
  const handleBackToOrders = () => {
    setShowOrderDetails(false)
    setSelectedOrder(null)
  }

  // Show loading or redirect if not authenticated
  if (authLoading || isLoading || !user) {
    return (
      <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-teal-600" />
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-slate-500">Please wait while we load your order history.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="grid gap-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">My Orders</h1>
          <p className="text-slate-500">View and manage your orders</p>
        </div>

        {/* Order Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            {
              title: "Total Orders",
              value: mockOrders.length.toString(),
              icon: <Package className="h-5 w-5 text-teal-600" />,
              description: "All time",
            },
            {
              title: "Processing",
              value: mockOrders.filter(o => o.status.toLowerCase() === "processing").length.toString(),
              icon: <ShoppingBag className="h-5 w-5 text-blue-600" />,
              description: "Being prepared",
            },
            {
              title: "Shipped",
              value: mockOrders.filter(o => o.status.toLowerCase() === "shipped").length.toString(),
              icon: <TruckIcon className="h-5 w-5 text-amber-600" />,
              description: "On the way",
            },
            {
              title: "Delivered",
              value: mockOrders.filter(o => o.status.toLowerCase() === "delivered").length.toString(),
              icon: <CheckCircle2 className="h-5 w-5 text-green-600" />,
              description: "Successfully delivered",
            },
          ].map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                {stat.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-slate-500">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Order Management Component */}
        <OrderManagement />
      </div>
    </div>
  )
}
