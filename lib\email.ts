import { supabase } from './supabase';

interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    name: string;
    content: string;
    contentType: string;
  }>;
}

/**
 * Send an email using Supabase Edge Functions
 * @param options Email options including recipient, subject, and content
 * @returns Success status and message
 */
export async function sendEmail(options: EmailOptions): Promise<{ success: boolean; message: string }> {
  try {
    // Call the Supabase Edge Function for sending emails
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: options,
    });

    if (error) {
      console.error('Error sending email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send email',
      };
    }

    return {
      success: true,
      message: 'Email sent successfully',
    };
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

/**
 * Send an order confirmation email to a customer
 * @param orderId The ID of the order
 * @param email The customer's email address
 * @returns Success status and message
 */
export async function sendOrderConfirmationEmail(
  orderId: string,
  email: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get order details from the database
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        users!inner(*),
        order_items!inner(
          *,
          products!inner(*)
        )
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order details:', orderError);
      return {
        success: false,
        message: 'Failed to fetch order details',
      };
    }

    if (!order) {
      return {
        success: false,
        message: 'Order not found',
      };
    }

    // Format order items for the email
    const orderItems = order.order_items.map((item: any) => ({
      name: item.products.name,
      quantity: item.quantity,
      price: item.price,
      total: item.price * item.quantity,
    }));

    // Create email content
    const subject = `PASSDOWN Order Confirmation #${orderId.slice(0, 8)}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #008080; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">PASSDOWN</h1>
        </div>
        <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
          <h2>Order Confirmation</h2>
          <p>Dear ${order.users.name || 'Customer'},</p>
          <p>Thank you for your order! We're processing it now and will notify you when it ships.</p>
          
          <div style="margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
            <h3 style="margin-top: 0;">Order Summary</h3>
            <p><strong>Order ID:</strong> ${orderId.slice(0, 8)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p>
            <p><strong>Order Status:</strong> ${order.status}</p>
            <p><strong>Total Amount:</strong> ₹${order.total.toLocaleString('en-IN')}</p>
          </div>
          
          <h3>Order Items</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background-color: #f2f2f2;">
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Product</th>
                <th style="padding: 10px; text-align: center; border: 1px solid #ddd;">Quantity</th>
                <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Price</th>
                <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${orderItems.map((item: any) => `
                <tr>
                  <td style="padding: 10px; border: 1px solid #ddd;">${item.name}</td>
                  <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${item.quantity}</td>
                  <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₹${item.price.toLocaleString('en-IN')}</td>
                  <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₹${item.total.toLocaleString('en-IN')}</td>
                </tr>
              `).join('')}
            </tbody>
            <tfoot>
              <tr style="background-color: #f2f2f2;">
                <td colspan="3" style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>Total</strong></td>
                <td style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>₹${order.total.toLocaleString('en-IN')}</strong></td>
              </tr>
            </tfoot>
          </table>
          
          <div style="margin-top: 30px;">
            <p>If you have any questions about your order, please contact our customer support <NAME_EMAIL>.</p>
            <p>Thank you for shopping with PASSDOWN!</p>
          </div>
        </div>
        <div style="background-color: #f2f2f2; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p>© ${new Date().getFullYear()} PASSDOWN. All rights reserved.</p>
          <p>This email was sent to ${email}. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    // Send the email
    return await sendEmail({
      to: email,
      subject,
      html,
    });
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

/**
 * Send an order status update email to a customer
 * @param orderId The ID of the order
 * @param newStatus The new status of the order
 * @param email The customer's email address
 * @returns Success status and message
 */
export async function sendOrderStatusUpdateEmail(
  orderId: string,
  newStatus: string,
  email: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get order details from the database
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        users!inner(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order details:', orderError);
      return {
        success: false,
        message: 'Failed to fetch order details',
      };
    }

    if (!order) {
      return {
        success: false,
        message: 'Order not found',
      };
    }

    // Create status-specific content
    let statusMessage = '';
    let nextSteps = '';

    switch (newStatus.toLowerCase()) {
      case 'processing':
        statusMessage = 'We\'re now processing your order and preparing it for shipment.';
        nextSteps = 'We\'ll notify you again when your order ships.';
        break;
      case 'shipped':
        statusMessage = 'Your order has been shipped and is on its way to you!';
        nextSteps = 'You can track your order using the tracking information provided by the seller.';
        break;
      case 'delivered':
        statusMessage = 'Your order has been delivered!';
        nextSteps = 'We hope you enjoy your purchase. If you have any issues, please contact us.';
        break;
      case 'cancelled':
        statusMessage = 'Your order has been cancelled as requested.';
        nextSteps = 'If you did not request this cancellation, please contact our customer support immediately.';
        break;
      default:
        statusMessage = `Your order status has been updated to: ${newStatus}`;
        nextSteps = 'If you have any questions, please contact our customer support.';
    }

    // Create email content
    const subject = `PASSDOWN Order Update: ${newStatus.toUpperCase()} #${orderId.slice(0, 8)}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #008080; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">PASSDOWN</h1>
        </div>
        <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
          <h2>Order Status Update</h2>
          <p>Dear ${order.users.name || 'Customer'},</p>
          <p>${statusMessage}</p>
          
          <div style="margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
            <h3 style="margin-top: 0;">Order Details</h3>
            <p><strong>Order ID:</strong> ${orderId.slice(0, 8)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p>
            <p><strong>New Status:</strong> <span style="font-weight: bold; color: #008080;">${newStatus}</span></p>
            <p><strong>Total Amount:</strong> ₹${order.total.toLocaleString('en-IN')}</p>
          </div>
          
          <p>${nextSteps}</p>
          
          <div style="margin-top: 30px;">
            <p>If you have any questions about your order, please contact our customer support <NAME_EMAIL>.</p>
            <p>Thank you for shopping with PASSDOWN!</p>
          </div>
        </div>
        <div style="background-color: #f2f2f2; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p>© ${new Date().getFullYear()} PASSDOWN. All rights reserved.</p>
          <p>This email was sent to ${email}. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    // Send the email
    return await sendEmail({
      to: email,
      subject,
      html,
    });
  } catch (error) {
    console.error('Error sending order status update email:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

/**
 * Send a seller notification email when they receive a new order
 * @param orderId The ID of the order
 * @param sellerEmail The seller's email address
 * @returns Success status and message
 */
export async function sendSellerOrderNotificationEmail(
  orderId: string,
  sellerEmail: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Get order details from the database
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        users!inner(*),
        order_items!inner(
          *,
          products!inner(*)
        )
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order details:', orderError);
      return {
        success: false,
        message: 'Failed to fetch order details',
      };
    }

    if (!order) {
      return {
        success: false,
        message: 'Order not found',
      };
    }

    // Format order items for the email
    const orderItems = order.order_items.map((item: any) => ({
      name: item.products.name,
      quantity: item.quantity,
      price: item.price,
      total: item.price * item.quantity,
    }));

    // Create email content
    const subject = `PASSDOWN: New Order Received #${orderId.slice(0, 8)}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #008080; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0;">PASSDOWN</h1>
        </div>
        <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
          <h2>New Order Received</h2>
          <p>Hello Seller,</p>
          <p>You have received a new order! Please process it as soon as possible.</p>
          
          <div style="margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
            <h3 style="margin-top: 0;">Order Details</h3>
            <p><strong>Order ID:</strong> ${orderId.slice(0, 8)}</p>
            <p><strong>Order Date:</strong> ${new Date(order.created_at).toLocaleDateString()}</p>
            <p><strong>Customer:</strong> ${order.users.name || 'Customer'}</p>
            <p><strong>Total Amount:</strong> ₹${order.total.toLocaleString('en-IN')}</p>
          </div>
          
          <h3>Order Items</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background-color: #f2f2f2;">
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Product</th>
                <th style="padding: 10px; text-align: center; border: 1px solid #ddd;">Quantity</th>
                <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Price</th>
                <th style="padding: 10px; text-align: right; border: 1px solid #ddd;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${orderItems.map((item: any) => `
                <tr>
                  <td style="padding: 10px; border: 1px solid #ddd;">${item.name}</td>
                  <td style="padding: 10px; text-align: center; border: 1px solid #ddd;">${item.quantity}</td>
                  <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₹${item.price.toLocaleString('en-IN')}</td>
                  <td style="padding: 10px; text-align: right; border: 1px solid #ddd;">₹${item.total.toLocaleString('en-IN')}</td>
                </tr>
              `).join('')}
            </tbody>
            <tfoot>
              <tr style="background-color: #f2f2f2;">
                <td colspan="3" style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>Total</strong></td>
                <td style="padding: 10px; text-align: right; border: 1px solid #ddd;"><strong>₹${order.total.toLocaleString('en-IN')}</strong></td>
              </tr>
            </tfoot>
          </table>
          
          <div style="margin-top: 30px;">
            <p>Please log in to your seller dashboard to process this order.</p>
            <p>Thank you for selling with PASSDOWN!</p>
          </div>
        </div>
        <div style="background-color: #f2f2f2; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p>© ${new Date().getFullYear()} PASSDOWN. All rights reserved.</p>
          <p>This email was sent to ${sellerEmail}. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    // Send the email
    return await sendEmail({
      to: sellerEmail,
      subject,
      html,
    });
  } catch (error) {
    console.error('Error sending seller order notification email:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}
