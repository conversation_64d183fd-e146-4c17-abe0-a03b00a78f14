/**
 * Utility functions for caching database queries
 */

// In-memory cache for server-side
const memoryCache = new Map<string, { data: any; expiry: number }>();

/**
 * Generate a cache key from a query and parameters
 * @param queryName Name of the query
 * @param params Parameters for the query
 * @returns A string cache key
 */
export function generateCacheKey(queryName: string, params?: any): string {
  return `${queryName}:${params ? JSON.stringify(params) : ''}`;
}

/**
 * Cache data with an expiry time
 * @param key Cache key
 * @param data Data to cache
 * @param expirySeconds Time in seconds until the cache expires
 */
export function setCache(key: string, data: any, expirySeconds: number = 300): void {
  const expiry = Date.now() + expirySeconds * 1000;
  memoryCache.set(key, { data, expiry });
}

/**
 * Get data from cache if it exists and hasn't expired
 * @param key Cache key
 * @returns Cached data or null if not found or expired
 */
export function getCache<T>(key: string): T | null {
  const cached = memoryCache.get(key);
  
  if (!cached) return null;
  
  // Check if cache has expired
  if (cached.expiry < Date.now()) {
    memoryCache.delete(key);
    return null;
  }
  
  return cached.data as T;
}

/**
 * Clear a specific cache entry
 * @param key Cache key to clear
 */
export function clearCache(key: string): void {
  memoryCache.delete(key);
}

/**
 * Clear all cache entries or those matching a prefix
 * @param prefix Optional prefix to match cache keys
 */
export function clearAllCache(prefix?: string): void {
  if (!prefix) {
    memoryCache.clear();
    return;
  }
  
  // Clear only keys matching the prefix
  for (const key of memoryCache.keys()) {
    if (key.startsWith(prefix)) {
      memoryCache.delete(key);
    }
  }
}

/**
 * Wrapper function to cache the result of an async function
 * @param fn Function to execute and cache the result
 * @param cacheKey Cache key
 * @param expirySeconds Time in seconds until the cache expires
 * @returns Result of the function, either from cache or freshly executed
 */
export async function withCache<T>(
  fn: () => Promise<T>,
  cacheKey: string,
  expirySeconds: number = 300
): Promise<T> {
  // Try to get from cache first
  const cached = getCache<T>(cacheKey);
  if (cached !== null) {
    return cached;
  }
  
  // Execute the function
  const result = await fn();
  
  // Cache the result
  setCache(cacheKey, result, expirySeconds);
  
  return result;
}

/**
 * Get cache statistics
 * @returns Object with cache statistics
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: memoryCache.size,
    keys: Array.from(memoryCache.keys()),
  };
}
