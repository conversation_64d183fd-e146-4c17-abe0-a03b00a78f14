import { supabase } from './supabase';

/**
 * Uploads an image to Supabase Storage
 * @param file The file to upload
 * @param bucket The storage bucket name (default: 'product-images')
 * @param folder Optional folder path within the bucket
 * @returns The public URL of the uploaded image
 */
export async function uploadImage(
  file: File,
  bucket: string = 'product-images',
  folder?: string
): Promise<string> {
  try {
    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    
    // Create the full path including folder if provided
    const filePath = folder ? `${folder}/${fileName}` : fileName;
    
    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });
    
    if (error) {
      throw error;
    }
    
    // Get the public URL
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);
    
    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
}

/**
 * Uploads multiple images to Supabase Storage
 * @param files Array of files to upload
 * @param bucket The storage bucket name (default: 'product-images')
 * @param folder Optional folder path within the bucket
 * @returns Array of public URLs of the uploaded images
 */
export async function uploadMultipleImages(
  files: File[],
  bucket: string = 'product-images',
  folder?: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadImage(file, bucket, folder));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
}

/**
 * Deletes an image from Supabase Storage
 * @param url The public URL of the image to delete
 * @param bucket The storage bucket name (default: 'product-images')
 * @returns Boolean indicating success
 */
export async function deleteImage(
  url: string,
  bucket: string = 'product-images'
): Promise<boolean> {
  try {
    // Extract the path from the URL
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const bucketIndex = pathParts.findIndex(part => part === bucket);
    
    if (bucketIndex === -1) {
      throw new Error(`Could not find bucket '${bucket}' in URL: ${url}`);
    }
    
    const filePath = pathParts.slice(bucketIndex + 1).join('/');
    
    // Delete the file from Supabase Storage
    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);
    
    if (error) {
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
}

/**
 * Creates a Supabase Storage bucket if it doesn't exist
 * @param bucket The bucket name to create
 * @param isPublic Whether the bucket should be public (default: true)
 * @returns Boolean indicating success
 */
export async function createBucketIfNotExists(
  bucket: string,
  isPublic: boolean = true
): Promise<boolean> {
  try {
    // Check if bucket exists
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketExists = buckets?.some(b => b.name === bucket);
    
    if (!bucketExists) {
      // Create the bucket
      const { error } = await supabase.storage.createBucket(bucket, {
        public: isPublic,
      });
      
      if (error) {
        throw error;
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error creating bucket '${bucket}':`, error);
    return false;
  }
}

/**
 * Initializes the storage buckets needed for the application
 */
export async function initializeStorage(): Promise<void> {
  try {
    await createBucketIfNotExists('product-images');
    await createBucketIfNotExists('user-avatars');
    await createBucketIfNotExists('category-images');
    console.log('Storage buckets initialized successfully');
  } catch (error) {
    console.error('Error initializing storage buckets:', error);
  }
}
