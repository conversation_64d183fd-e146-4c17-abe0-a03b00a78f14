"use client"

import { useState, useEffect, createContext, useContext, ReactNode } from "react"

const MOBILE_BREAKPOINT = 640
const TABLET_BREAKPOINT = 1024

interface MobileContextType {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  orientation: "portrait" | "landscape"
  touchDevice: boolean
}

const MobileContext = createContext<MobileContextType | undefined>(undefined)

export function MobileProvider({ children }: { children: ReactNode }) {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isDesktop, setIsDesktop] = useState(true)
  const [orientation, setOrientation] = useState<"portrait" | "landscape">("portrait")
  const [touchDevice, setTouchDevice] = useState(false)

  useEffect(() => {
    // Function to update device type based on screen width
    const updateDeviceType = () => {
      const width = window.innerWidth
      setIsMobile(width < MOBILE_BREAKPOINT)
      setIsTablet(width >= MOBILE_BREAKPOINT && width < TABLET_BREAKPOINT)
      setIsDesktop(width >= TABLET_BREAKPOINT)
      setOrientation(window.innerHeight > window.innerWidth ? "portrait" : "landscape")
    }

    // Check if device has touch capability
    const checkTouchDevice = () => {
      setTouchDevice(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        (navigator as any).msMaxTouchPoints > 0
      )
    }

    // Initial check
    updateDeviceType()
    checkTouchDevice()

    // Add event listeners
    window.addEventListener('resize', updateDeviceType)
    window.addEventListener('orientationchange', updateDeviceType)

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateDeviceType)
      window.removeEventListener('orientationchange', updateDeviceType)
    }
  }, [])

  return (
    <MobileContext.Provider value={{
      isMobile,
      isTablet,
      isDesktop,
      orientation,
      touchDevice
    }}>
      {children}
    </MobileContext.Provider>
  )
}

export function useMobile() {
  const context = useContext(MobileContext)

  if (context === undefined) {
    throw new Error("useMobile must be used within a MobileProvider")
  }

  return context
}

// Legacy hook for backward compatibility
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined)

  useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}
