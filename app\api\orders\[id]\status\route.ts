import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { supabase } from '@/lib/supabase';
import { getAuthUser } from '@/lib/auth';
import { sendOrderStatusUpdateEmail, sendSellerOrderNotificationEmail } from '@/lib/email';

// Validation schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(['pending', 'processing', 'shipped', 'delivered', 'cancelled']),
  notifyCustomer: z.boolean().optional().default(true),
});

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authUser = await getAuthUser();
    if (!authUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only sellers and admins can update order status
    if (authUser.role !== 'seller' && authUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden: Only sellers and admins can update order status' },
        { status: 403 }
      );
    }

    const orderId = params.id;
    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get request body
    const body = await req.json();

    // Validate request body
    const result = statusUpdateSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: result.error.format() },
        { status: 400 }
      );
    }

    const { status, notifyCustomer } = result.data;

    // Check if order exists and belongs to the seller (if not admin)
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        users!inner(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // If user is a seller, check if they own the products in the order
    if (authUser.role === 'seller') {
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          products!inner(seller_id)
        `)
        .eq('order_id', orderId);

      if (itemsError) {
        console.error('Error fetching order items:', itemsError);
        return NextResponse.json(
          { error: 'Failed to verify order ownership' },
          { status: 500 }
        );
      }

      // Check if all products in the order belong to the seller
      const allProductsBelongToSeller = orderItems.every(
        (item) => item.products.seller_id === authUser.id
      );

      if (!allProductsBelongToSeller) {
        return NextResponse.json(
          { error: 'Forbidden: You can only update orders for your products' },
          { status: 403 }
        );
      }
    }

    // Update order status
    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', orderId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating order status:', updateError);
      return NextResponse.json(
        { error: 'Failed to update order status' },
        { status: 500 }
      );
    }

    // Send email notification if requested
    if (notifyCustomer) {
      const emailResult = await sendOrderStatusUpdateEmail(
        orderId,
        status,
        order.users.email
      );

      if (!emailResult.success) {
        console.warn('Failed to send order status update email:', emailResult.message);
      }
    }

    // If status changed to "processing" and it's a new order, notify the seller
    if (status === 'processing' && order.status === 'pending') {
      // Get seller email
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          products!inner(
            seller_id,
            users!inner(email)
          )
        `)
        .eq('order_id', orderId)
        .limit(1)
        .single();

      if (!itemsError && orderItems) {
        const sellerEmail = orderItems.products.users.email;
        
        if (sellerEmail) {
          const notificationResult = await sendSellerOrderNotificationEmail(
            orderId,
            sellerEmail
          );

          if (!notificationResult.success) {
            console.warn('Failed to send seller notification email:', notificationResult.message);
          }
        }
      }
    }

    return NextResponse.json({
      message: 'Order status updated successfully',
      order: updatedOrder,
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authUser = await getAuthUser();
    if (!authUser) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const orderId = params.id;
    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get order status history
    const { data: statusHistory, error: historyError } = await supabase
      .from('order_status_history')
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: false });

    if (historyError) {
      console.error('Error fetching order status history:', historyError);
      return NextResponse.json(
        { error: 'Failed to fetch order status history' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      statusHistory,
    });
  } catch (error) {
    console.error('Error fetching order status history:', error);
    return NextResponse.json(
      { error: 'Something went wrong' },
      { status: 500 }
    );
  }
}
