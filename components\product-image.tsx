"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { OptimizedImage } from "@/components/ui/optimized-image"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Heart,
  ShoppingCart,
  Eye,
  Check,
  Percent,
  Share2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useCart } from "@/hooks/use-cart"
import type { Product } from "@/app/types"

interface ProductImageProps {
  product: Product
  width?: number
  height?: number
  priority?: boolean
  showActions?: boolean
  showDiscount?: boolean
  showCondition?: boolean
  linkToProduct?: boolean
  className?: string
  aspectRatio?: number
  fill?: boolean
  sizes?: string
  onAddToCart?: () => void
  onAddToWishlist?: () => void
}

export default function ProductImage({
  product,
  width = 300,
  height = 300,
  priority = false,
  showActions = true,
  showDiscount = true,
  showCondition = true,
  linkToProduct = true,
  className,
  aspectRatio,
  fill = false,
  sizes = "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",
  onAddToCart,
  onAddToWishlist
}: ProductImageProps) {
  const router = useRouter()
  const { addItem } = useCart()
  const [isHovered, setIsHovered] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Calculate discount percentage if original price exists
  const calculateDiscount = () => {
    if (!product.originalPrice || product.originalPrice <= product.price) return null
    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
  }

  const discount = calculateDiscount()

  // Get condition badge color
  const getConditionBadgeColor = (condition: string) => {
    switch (condition) {
      case "New":
        return "bg-green-500 hover:bg-green-600"
      case "Like New":
        return "bg-blue-500 hover:bg-blue-600"
      case "Good":
        return "bg-teal-500 hover:bg-teal-600"
      case "Fair":
        return "bg-amber-500 hover:bg-amber-600"
      case "Poor":
        return "bg-red-500 hover:bg-red-600"
      default:
        return "bg-slate-500 hover:bg-slate-600"
    }
  }

  // Handle add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setIsAddingToCart(true)
    addItem(product, 1)

    if (onAddToCart) onAddToCart()

    setTimeout(() => {
      setIsAddingToCart(false)
    }, 1000)
  }

  // Handle add to wishlist
  const handleAddToWishlist = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (onAddToWishlist) onAddToWishlist()
  }

  // Image container
  const ImageContainer = ({ children }: { children: React.ReactNode }) => {
    const containerProps = {
      className: cn(
        "relative overflow-hidden",
        className
      ),
      onMouseEnter: () => setIsHovered(true),
      onMouseLeave: () => setIsHovered(false)
    };

    if (linkToProduct) {
      return (
        <div {...containerProps}>
          {children}
        </div>
      )
    }

    return (
      <div {...containerProps}>
        {children}
      </div>
    )
  }

  return (
    <ImageContainer>
      {/* Product Image */}
      <OptimizedImage
        src={product.image || "/placeholder.svg"}
        alt={product.name}
        width={width}
        height={height}
        priority={priority}
        fill={fill}
        sizes={sizes}
        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
        style={{ aspectRatio: aspectRatio ? String(aspectRatio) : undefined }}
        onError={() => console.log(`Failed to load image for product: ${product.id}`)}
        blurColor="#f0f0f0"
        placeholder="blur"
      />

      {/* Product Link - Separate from image to avoid nesting <a> tags */}
      {linkToProduct && (
        <Link
          href={`/product/${product.id}`}
          className="absolute inset-0 z-10"
          aria-label={`View details for ${product.name}`}
          onClick={(e) => {
            // Only navigate if not clicking on action buttons
            if (e.target === e.currentTarget) {
              e.preventDefault();
              router.push(`/product/${product.id}`);
            }
          }}
        />
      )}

      {/* Condition Badge */}
      {showCondition && (
        <Badge
          className={cn(
            "absolute top-2 left-2 z-10",
            getConditionBadgeColor(product.condition)
          )}
        >
          {product.condition}
        </Badge>
      )}

      {/* Discount Badge */}
      {showDiscount && discount && (
        <Badge className="absolute top-2 right-2 z-10 bg-red-500 hover:bg-red-600">
          {discount > 0 && (
            <>
              <Percent className="mr-1 h-3 w-3" />
              {discount}% OFF
            </>
          )}
        </Badge>
      )}

      {/* Hover Actions */}
      {showActions && (
        <div
          className={cn(
            "absolute inset-0 bg-black/60 z-20 flex items-center justify-center gap-3 transition-opacity",
            isHovered ? "opacity-100" : "opacity-0"
          )}
        >
          <Button
            size="icon"
            variant="secondary"
            className="rounded-full h-10 w-10"
            onClick={handleAddToCart}
            disabled={isAddingToCart}
          >
            {isAddingToCart ? <Check className="h-5 w-5" /> : <ShoppingCart className="h-5 w-5" />}
            <span className="sr-only">{isAddingToCart ? "Added to cart" : "Add to cart"}</span>
          </Button>

          <Button
            size="icon"
            variant="secondary"
            className="rounded-full h-10 w-10"
            onClick={handleAddToWishlist}
          >
            <Heart className="h-5 w-5" />
            <span className="sr-only">Add to wishlist</span>
          </Button>

          {linkToProduct && (
            <Button
              size="icon"
              variant="secondary"
              className="rounded-full h-10 w-10"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                router.push(`/product/${product.id}`);
              }}
            >
              <Eye className="h-5 w-5" />
              <span className="sr-only">View details</span>
            </Button>
          )}
        </div>
      )}
    </ImageContainer>
  )
}
