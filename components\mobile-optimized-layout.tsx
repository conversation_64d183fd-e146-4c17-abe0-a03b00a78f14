"use client"

import { useState, useEffect, ReactNode } from "react"
import { useMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronUp } from "lucide-react"

interface MobileOptimizedLayoutProps {
  children: ReactNode
  bottomSheet?: ReactNode
  bottomNav?: ReactNode
  className?: string
  sheetTitle?: string
  sheetDescription?: string
  sheetTriggerText?: string
  sheetTriggerClassName?: string
  hideBottomNavOnScroll?: boolean
}

export function MobileOptimizedLayout({
  children,
  bottomSheet,
  bottomNav,
  className,
  sheetTitle,
  sheetDescription,
  sheetTriggerText = "Show Options",
  sheetTriggerClassName,
  hideBottomNavOnScroll = true,
}: MobileOptimizedLayoutProps) {
  const { isMobile } = useMobile()
  const [isScrollingDown, setIsScrollingDown] = useState(false)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [sheetOpen, setSheetOpen] = useState(false)
  
  // Handle scroll events to hide/show bottom navigation
  useEffect(() => {
    if (!isMobile || !hideBottomNavOnScroll) return
    
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsScrollingDown(true)
      } else {
        setIsScrollingDown(false)
      }
      
      setLastScrollY(currentScrollY)
    }
    
    window.addEventListener("scroll", handleScroll, { passive: true })
    
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [isMobile, lastScrollY, hideBottomNavOnScroll])
  
  return (
    <div className={cn("relative min-h-screen", className)}>
      {/* Main content */}
      <div className={cn(
        "pb-16", // Add padding for bottom navigation
        isMobile && bottomNav ? "pb-16" : "pb-0"
      )}>
        {children}
      </div>
      
      {/* Bottom sheet for mobile */}
      {isMobile && bottomSheet && (
        <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
          <SheetTrigger asChild>
            <Button
              variant="secondary"
              size="sm"
              className={cn(
                "fixed bottom-20 right-4 z-40 rounded-full shadow-lg transition-transform duration-300",
                isScrollingDown ? "translate-y-20" : "translate-y-0",
                sheetTriggerClassName
              )}
            >
              <ChevronUp className="h-4 w-4 mr-1" />
              {sheetTriggerText}
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="max-h-[90vh] overflow-y-auto">
            {bottomSheet}
          </SheetContent>
        </Sheet>
      )}
      
      {/* Bottom navigation for mobile */}
      {isMobile && bottomNav && (
        <div className={cn(
          "fixed bottom-0 left-0 right-0 z-50 bg-background border-t transition-transform duration-300",
          isScrollingDown ? "translate-y-full" : "translate-y-0"
        )}>
          {bottomNav}
        </div>
      )}
    </div>
  )
}
