import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { RotateCcw, Clock, AlertCircle, CheckCircle, Ban, HelpCircle } from "lucide-react"

export default function ReturnsRefundsPage() {
  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-6">Returns & Refunds Policy</h1>
        <p className="text-slate-500 mb-8">Last updated: May 15, 2023</p>
        
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="w-full md:w-1/3 flex justify-center">
                <div className="relative w-40 h-40">
                  <Image 
                    src="/placeholder.svg?height=160&width=160" 
                    alt="Returns illustration" 
                    fill 
                    className="object-contain"
                  />
                </div>
              </div>
              <div className="w-full md:w-2/3">
                <p className="mb-4">
                  At PASSDOWN, we want you to be completely satisfied with your purchase. We understand that sometimes a product may not meet your expectations or may arrive damaged. This Returns & Refunds Policy outlines the process for returning products and requesting refunds.
                </p>
                <p>
                  Please note that as a marketplace platform, PASSDOWN facilitates transactions between buyers and sellers. The specific return policies may vary by seller, but all sellers must comply with our minimum return policy standards outlined below.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <RotateCcw className="h-6 w-6 text-teal-600" />
              Return Policy
            </h2>
            <p className="mb-4">
              PASSDOWN has a 7-day return policy, which means you have 7 days after receiving your item to request a return.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Eligible for Returns</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
                      <span>Item not as described or defective</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
                      <span>Item arrived damaged</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
                      <span>Wrong item received</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 shrink-0" />
                      <span>Item doesn't work as expected (for electronics)</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Not Eligible for Returns</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <Ban className="h-4 w-4 text-red-500 mt-0.5 shrink-0" />
                      <span>Buyer's remorse or change of mind</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Ban className="h-4 w-4 text-red-500 mt-0.5 shrink-0" />
                      <span>Items damaged after delivery due to misuse</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Ban className="h-4 w-4 text-red-500 mt-0.5 shrink-0" />
                      <span>Items with removed tags or packaging</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Ban className="h-4 w-4 text-red-500 mt-0.5 shrink-0" />
                      <span>Items marked as "No Returns" in the listing</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            
            <p className="mt-6 text-sm text-slate-500">
              Note: Some sellers may offer more generous return policies. The specific return policy for each item will be displayed on the product page.
            </p>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Clock className="h-6 w-6 text-teal-600" />
              Return Process
            </h2>
            <p className="mb-4">
              To start a return, follow these steps:
            </p>
            
            <div className="bg-teal-50 p-6 rounded-lg mt-6">
              <h3 className="font-semibold mb-3 text-teal-700">How to Return an Item</h3>
              <ol className="space-y-3 text-slate-700">
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">1</span>
                  <div>
                    <span className="font-medium">Log in to your PASSDOWN account</span>
                    <p className="text-sm text-slate-600 mt-1">Access your account dashboard to view your orders.</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">2</span>
                  <div>
                    <span className="font-medium">Go to "My Orders" and find the item you want to return</span>
                    <p className="text-sm text-slate-600 mt-1">Click on the order containing the item you wish to return.</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">3</span>
                  <div>
                    <span className="font-medium">Click "Return Item" and select the reason for your return</span>
                    <p className="text-sm text-slate-600 mt-1">Provide detailed information about why you're returning the item and upload photos if necessary.</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">4</span>
                  <div>
                    <span className="font-medium">Print the return shipping label (if provided)</span>
                    <p className="text-sm text-slate-600 mt-1">Some sellers provide prepaid return labels. If not, you may need to arrange your own shipping.</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">5</span>
                  <div>
                    <span className="font-medium">Package the item securely and ship it back</span>
                    <p className="text-sm text-slate-600 mt-1">Use the original packaging if possible and include all accessories, manuals, and free gifts that came with the item.</p>
                  </div>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">6</span>
                  <div>
                    <span className="font-medium">Track your return</span>
                    <p className="text-sm text-slate-600 mt-1">You can track the status of your return in the "Returns" section of your account.</p>
                  </div>
                </li>
              </ol>
            </div>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <AlertCircle className="h-6 w-6 text-teal-600" />
              Refund Process
            </h2>
            <p className="mb-4">
              Once your return is received and inspected, we will notify you about the status of your refund. If approved, your refund will be processed according to the following timeline:
            </p>
            
            <div className="space-y-4 mt-6">
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <Clock className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Processing Time</h3>
                  <p className="text-slate-600">
                    Refunds are typically processed within 3-5 business days after the seller receives and inspects the returned item.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <CheckCircle className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Refund Methods</h3>
                  <p className="text-slate-600">
                    Refunds will be issued to the original payment method used for the purchase. For UPI, credit/debit cards, and digital wallets, refunds typically take 5-7 business days to reflect in your account, depending on your bank or payment provider.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <AlertCircle className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Partial Refunds</h3>
                  <p className="text-slate-600">
                    In some cases, partial refunds may be granted (e.g., if an item is returned with signs of use or missing parts). The seller will communicate the reason for any partial refund.
                  </p>
                </div>
              </div>
            </div>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <HelpCircle className="h-6 w-6 text-teal-600" />
              Frequently Asked Questions
            </h2>
            
            <div className="space-y-4 mt-6">
              <div>
                <h3 className="font-semibold">Who pays for return shipping?</h3>
                <p className="text-slate-600">
                  If the return is due to a seller error (wrong item, defective product, etc.), the seller will cover the return shipping costs. If the return is for any other reason, the buyer is typically responsible for return shipping costs, unless the seller's policy states otherwise.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold">What if my item arrives damaged?</h3>
                <p className="text-slate-600">
                  If your item arrives damaged, please take photos of the damaged item and packaging, and contact the seller immediately through the PASSDOWN messaging system. You should also initiate a return request within 48 hours of receiving the item.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold">Can I exchange an item instead of returning it?</h3>
                <p className="text-slate-600">
                  Some sellers offer exchanges. If you prefer an exchange rather than a refund, please contact the seller directly to discuss this option.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold">What if the seller refuses my return request?</h3>
                <p className="text-slate-600">
                  If a seller refuses your return request and you believe it meets our return policy criteria, you can contact PASSDOWN customer support to mediate the dispute.
                </p>
              </div>
            </div>
          </section>
        </div>
        
        <div className="mt-12 flex justify-between items-center">
          <Button variant="outline" asChild>
            <Link href="/shipping">Shipping Policy</Link>
          </Button>
          <Button asChild className="bg-teal-600 hover:bg-teal-700">
            <Link href="/contact">Contact Us</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
