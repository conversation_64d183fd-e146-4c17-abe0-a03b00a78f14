import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Truck, Clock, MapPin, AlertCircle, CheckCircle, Package } from "lucide-react"

export default function ShippingPolicyPage() {
  return (
    <div className="container px-4 py-8 md:py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl md:text-4xl font-bold mb-6">Shipping Policy</h1>
        <p className="text-slate-500 mb-8">Last updated: May 15, 2023</p>
        
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="w-full md:w-1/3 flex justify-center">
                <div className="relative w-40 h-40">
                  <Image 
                    src="/placeholder.svg?height=160&width=160" 
                    alt="Shipping illustration" 
                    fill 
                    className="object-contain"
                  />
                </div>
              </div>
              <div className="w-full md:w-2/3">
                <p className="mb-4">
                  At PASSDOWN, we strive to provide reliable and efficient shipping services for all products purchased through our marketplace. This shipping policy outlines the terms and conditions related to the delivery of products from sellers to buyers.
                </p>
                <p>
                  Please read this policy carefully to understand how shipping works on our platform, including shipping methods, timeframes, costs, and responsibilities.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Truck className="h-6 w-6 text-teal-600" />
              Shipping Methods
            </h2>
            <p className="mb-4">
              PASSDOWN partners with several reliable courier services to ensure safe and timely delivery of products. The available shipping methods may vary depending on the seller's location, the buyer's location, and the nature of the product.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Standard Shipping</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <Clock className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Delivery within 3-7 business days</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Available across all major cities and towns in India</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Package className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Tracking available through seller's chosen courier service</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Express Shipping</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <Clock className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Delivery within 1-3 business days</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Available in select metro cities and major towns</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Package className="h-4 w-4 text-slate-500 mt-0.5 shrink-0" />
                      <span>Priority handling and real-time tracking</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            
            <p className="mt-6 text-sm text-slate-500">
              Note: The availability of shipping methods depends on the seller's location and the delivery address. Some remote areas may have limited shipping options or longer delivery timeframes.
            </p>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Clock className="h-6 w-6 text-teal-600" />
              Shipping Timeframes
            </h2>
            <p className="mb-4">
              The estimated delivery time depends on several factors, including the shipping method chosen, the seller's processing time, and the destination address.
            </p>
            
            <div className="space-y-4 mt-6">
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <Clock className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Processing Time</h3>
                  <p className="text-slate-600">
                    Sellers are required to process and ship orders within 2 business days after receiving an order. Some sellers may ship sooner. The processing time will be displayed on the product page.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <Truck className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Transit Time</h3>
                  <p className="text-slate-600">
                    Standard shipping typically takes 3-7 business days, while express shipping takes 1-3 business days. These timeframes are estimates and may vary based on the destination and other factors.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="bg-slate-100 p-2 rounded-full">
                  <AlertCircle className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <h3 className="font-semibold">Potential Delays</h3>
                  <p className="text-slate-600">
                    Shipping may be delayed due to factors beyond our control, such as weather conditions, customs clearance (for international shipping), or logistical issues. We will notify buyers of any significant delays.
                  </p>
                </div>
              </div>
            </div>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Package className="h-6 w-6 text-teal-600" />
              Shipping Costs
            </h2>
            <p className="mb-4">
              Shipping costs are determined based on the product's weight, dimensions, shipping method, and destination. The shipping cost will be displayed during checkout before you confirm your order.
            </p>
            
            <div className="bg-slate-50 p-6 rounded-lg mt-6">
              <h3 className="font-semibold mb-3">Standard Shipping Rates</h3>
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Weight</th>
                    <th className="text-left py-2">Local (Same City)</th>
                    <th className="text-left py-2">Regional</th>
                    <th className="text-left py-2">National</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="py-2">0-500g</td>
                    <td className="py-2">₹49</td>
                    <td className="py-2">₹79</td>
                    <td className="py-2">₹99</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">501g-1kg</td>
                    <td className="py-2">₹79</td>
                    <td className="py-2">₹99</td>
                    <td className="py-2">₹149</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2">1kg-2kg</td>
                    <td className="py-2">₹99</td>
                    <td className="py-2">₹149</td>
                    <td className="py-2">₹199</td>
                  </tr>
                  <tr>
                    <td className="py-2">2kg+</td>
                    <td className="py-2">₹149+</td>
                    <td className="py-2">₹199+</td>
                    <td className="py-2">₹249+</td>
                  </tr>
                </tbody>
              </table>
              <p className="mt-4 text-xs text-slate-500">
                * Express shipping rates are typically 1.5-2x the standard shipping rates.
              </p>
            </div>
            
            <div className="mt-6">
              <h3 className="font-semibold mb-2">Free Shipping</h3>
              <p className="text-slate-600">
                Some sellers may offer free shipping on certain products or for orders above a specific value. This will be clearly indicated on the product page.
              </p>
            </div>
          </section>
          
          <Separator />
          
          <section>
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-teal-600" />
              Order Tracking
            </h2>
            <p className="mb-4">
              Once your order is shipped, you will receive a confirmation email with tracking information. You can also track your order through your PASSDOWN account under "My Orders."
            </p>
            
            <div className="bg-teal-50 p-6 rounded-lg mt-6">
              <h3 className="font-semibold mb-3 text-teal-700">How to Track Your Order</h3>
              <ol className="space-y-3 text-slate-700">
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">1</span>
                  <span>Log in to your PASSDOWN account</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">2</span>
                  <span>Go to "My Orders" in your account dashboard</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">3</span>
                  <span>Find the order you want to track and click "Track Order"</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="bg-teal-100 text-teal-700 rounded-full w-6 h-6 flex items-center justify-center shrink-0">4</span>
                  <span>You will be redirected to the courier's tracking page or shown the tracking information directly on PASSDOWN</span>
                </li>
              </ol>
            </div>
          </section>
        </div>
        
        <div className="mt-12 flex justify-between items-center">
          <Button variant="outline" asChild>
            <Link href="/returns">Returns & Refunds</Link>
          </Button>
          <Button asChild className="bg-teal-600 hover:bg-teal-700">
            <Link href="/contact">Contact Us</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
